
//  Body Settings
// - - - - - - - - - - - - - - - - - - - - - - - -
$body-background          : $white;
$body-font-color          : $dark-gray-alt; 
$heading-font-family      : 'AzoSansRegular', Helvetica, Arial, sans-serif;
$body-font-family         : 'AzoSansRegular', Helvetica, Arial, sans-serif;
$alt-font-family          : 'AzoSansLight', Helvetica, Arial, sans-serif;
$demi-bold-italic		  : 'AzoSansMediumItalic', Helvetica, Arial, sans-serif;
$demi-bold-regular		  : 'AzoSansMedium', Helvetica, Arial, sans-serif;
$font-medium			  : 'AzoSansMedium', Helvetica, Arial, sans-serif;
$font-italic			  : 'AzoSansItalic', Helvetica, Arial, sans-serif;
$global-weight-light      : 300;
$global-weight-normal     : 400;
$global-weight-semibold   : 600;
$global-weight-bold       : 800;

//  Style Override
// - - - - - - - - - - - - - - - - - - - - - - - -
@include style-override() {

	// HOMEPAGE BACKGROUNDS - CAN BE DONE THROUGH CONTROL PANEL ONCE IMAGE UPLOADS ARE FIXED


	// Links
	a {
		color: $primary-color;
		&:hover {
			color: darken($primary-color, 20%);
		}
	}

	//paragraphs
	p{
		font-size: 18px;
		line-height: 2;
	}

	//table
	table{
		thead{
			border: 0;
			tr{
				td{
					text-transform: uppercase;
					background-color: $primary-color;
					color: $white;
					padding: 20px;
				}
			}
		}
		tbody{
			tr{
				&:nth-child(2n){
					background-color: $light-gray;
				}
				&:nth-child(2n+1){
					background-color: $white;
				}
				td{
					padding: 20px;
				}
			}
		}
	}

	//  Main Column Left & Right (Content & Sidebar)
	div.main-column, div.main-column-left, div.main-column-right {
		@each $breakpoint, $spacing in $responsive-container-spacing {
			@include breakpoint($breakpoint) {
				padding-top: $spacing;
				padding-bottom: $spacing;
			}
	    }
	    & > div.row {
	    	&:first-child { padding-top: 0; }
	    	margin-left: 0; margin-right: 0;
	    	& > * {
	    		padding-left: 0;
	    		padding-right: 0;
	    		&:first-child { padding-top: 0; }
	    	}
	    }
	}
		div.main-column-left {
		>div.row {
			margin-right: 50px;
			@include breakpoint (medium down) {
				margin-right: 0;
			}
			div.core-builder {
				width:100%;
			}
		}
	}
	
}