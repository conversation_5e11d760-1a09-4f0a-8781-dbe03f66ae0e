
//  Pagination
// - - - - - - - - - - - - - - - - - - - - - - - -
$pagination-font-size               : rem-calc(14);
$pagination-margin-bottom           : 0;
$pagination-margin-top           	: $global-margin;
$pagination-item-color              : $black;
$pagination-item-padding            : rem-calc(5 10);
$pagination-item-spacing            : rem-calc(1);
$pagination-radius                  : $global-radius;
$pagination-item-background-hover   : $light-gray;
$pagination-item-background-current : $primary-color;
$pagination-item-color-current      : white;
$pagination-item-color-disabled     : $medium-gray;
$pagination-ellipsis-color          : $black;
$pagination-arrows                  : true;

//  Style Override
// - - - - - - - - - - - - - - - - - - - - - - - -
@include style-override() {

	div.column.pagination-container {
		width: 100%;
	    flex: none;
	    max-width: 100%;
	    margin-top: 100px;
	    padding: 0;
	    @include breakpoint(large down) {
			margin-top: 50px;
		}
	}
	ul.pagination {
		li {
			padding: 0;
			background: transparent;
			border-radius: 0;
			margin: 0;
			margin-right: 20px;

			@include breakpoint(small down) {
				display: none;
			}

			a {
				width: 37px;
				height: 37px;
				border: 2px solid $primary-color;
				padding: 0;
				color: $primary-color;
				font-family: $font-medium;
				font-size: 1.3em;
				line-height: 1.9em;
			}
			&:hover {
				background-color: transparent;
				a {
					background-color: $primary-color;
					color: $white;
				}
			}
			&.current {
				padding: 0;
				background: transparent;
				

				a {
					background: $primary-color;
					color: $white;
				}
			}
		}

		li.current {
			@include breakpoint(small down) {
				display: none;
			}
		}

		li.pagination-previous {
			margin: 0 5% 0 0;
			font-family: $font-medium;
			text-transform: uppercase;
			float: left;
			@include breakpoint(small down) {
				display: block;
			}

			a {
				width: auto;
				height: 35px;
				border-radius: 0;
				border: transparent;
				padding: 0;
				color: $primary-color;
				font-size: 1.2em;
				&:hover {
					background: transparent;
					color: darken($primary-color, 10%);
				}
				&:before {
					font-family: "icomoon";
				    content: "\f0a4";
				}
			}
		}

		li.pagination-next {
			margin: 0 0 0 5%;
			font-family: $font-medium;
			text-transform: uppercase;
			float: right;
			@include breakpoint(small down) {
				display: block;
			}

			a {
				font-size: 1.2em;
				width: auto;
				height: 35px;
				border-radius: 0;
				border: transparent;
				padding: 0;
				color: $primary-color;
				&:hover {
					background: transparent;
					color: darken($primary-color, 10%);
				}
				&:after {
					font-family: "icomoon";
				    content: "\f078";
				}
			}
		}
	}

}