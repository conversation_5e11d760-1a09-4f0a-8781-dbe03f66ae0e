
//  Responsive Navigation
// - - - - - - - - - - - - - - - - - - - - - - - -
$responsive-navigation-background-color : $secondary-color;
$responsive-navigation-opacity-forward  : 0.03;
$responsive-navigation-scale            : 0.95;
$responsive-navigation-color            : white;
$responsive-navigation-font-size        : 5vw;
$responsive-navigation-font-weight      : 600;


//  Style Override
// - - - - - - - - - - - - - - - - - - - - - - - -
@include style-override() {
    [data-responsive-menu] {
        width: 60%;
    }
    div.main-responsive-menu, [data-responsive-menu] {
        @include horizontal-vertical-align();
        display: none;
        color: white;
        z-index: 101;
        position: fixed;
        text-align: center;
        width: 100%;

        div.links {
            animation-duration: .4s;
        }
        li {
            list-style-type: none;
            display: inline-block;
        }
        a {
            @include transitions();
            display: inline-block;
            font-size: 1.4em !important; // Fallback
            font-size: $responsive-navigation-font-size;
            color: $responsive-navigation-color;
            font-weight: $responsive-navigation-font-weight;
            text-transform: uppercase;
            margin-bottom: 10px;
            padding: 14px 20px;
            opacity: 0.8;
            @include breakpoint(small down) {
                 font-size: 1.2em !important;
            }

            &:hover, &.active {
                background: $primary-color;
                opacity: 1;
            }
        }
    }
    section.main-container {
        @include transitions();
        >div {
            @include transitions();
        }
        &.forward {
            @include disable-selection();
            background-color: $responsive-navigation-background-color;
            cursor: default;
            position: relative;
            &:before {
                content: "";
                position: absolute;
                left: 0;
                top: 0;
                z-index: 100;
                opacity: 0;
                width: 100%;
                height: 100%;
            }
            >div {
                -webkit-transform: scale($responsive-navigation-scale);
                -moz-transform: scale($responsive-navigation-scale);
                -ms-transform: scale($responsive-navigation-scale);
                -o-transform: scale($responsive-navigation-scale);
                transform: scale($responsive-navigation-scale);
                z-index: 99;
                opacity: $responsive-navigation-opacity-forward;
            }
        }
    }

}