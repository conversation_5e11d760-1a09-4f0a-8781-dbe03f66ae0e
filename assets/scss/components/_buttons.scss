
//  Buttons
// - - - - - - - - - - - - - - - - - - - - - - - -
$button-padding: 15px 40px;
$button-margin: 0 0 2em 0;
$button-fill: solid;
$button-background: $primary-color;
$button-background-hover: scale-color($button-background, $lightness: -15%);
$button-color: $white;
$button-color-alt: #000;
$button-radius: $global-radius;
$button-sizes: (
	tiny: 0.6rem,
	small: 0.75rem,
	default: 1rem,
	large: 1rem,
);

//  Style Override
// - - - - - - - - - - - - - - - - - - - - - - - -
@include style-override() {

    // Primary Button
    .button {
        text-transform: uppercase;
        border-bottom: 5px solid darken($primary-color, 10%);
        font-family: $font-medium;
        outline: none;
        @include breakpoint(small down) {
            display: block;
        }
        &:focus {
            background: $primary-color;
            color: white;
        }
    }

    // Secondary Button
    .button.secondary {
        border: 1px solid $primary-color;
        background-color: transparent;
        color: $primary-color;
        padding: 17px 40px;
        &:hover { 
            background-color: $primary-color;
            color: $white;
            border: 1px solid $primary-color;
        }
        &:focus {
            background: none;
            color: $primary-color;
        }
    }

}