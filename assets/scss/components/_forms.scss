
//  Forms
// - - - - - - - - - - - - - - - - - - - - - - - -
$form-label-color: $black;
$form-label-font-size: rem-calc(14);
$form-label-font-weight: $global-weight-normal;
$form-label-line-height: 1.8;
$select-background: $white;
$select-triangle-color: #333;
$select-radius: $global-radius;
$input-color: $black;
$input-font-family: inherit;
$input-font-size: rem-calc(16);
$input-background: $white;
$input-background-focus: $white;
$input-background-disabled: $light-gray;
$input-border: 1px solid darken($medium-gray, 7%);
$input-border-focus: 1px solid $dark-gray;
$input-shadow: inset 0 1px 2px rgba($black, 0.1);
$input-shadow-focus: 0 0 5px $medium-gray;
$input-cursor-disabled: default;
$input-radius: $global-radius;
$input-padding: 20px;
$input-height: 60px;
$input-spacing: 25px;

//  Style Override
// - - - - - - - - - - - - - - - - - - - - - - - -
@include style-override() {

	// Ajax Forms
	.wt-ajax-form, form {

		position: relative;

		// Loading spinner
		.loading-spinner {
			display: none;
			@include horizontal-vertical-align();
			top: 40%;
		}

		// Ajax Loading Phase
		.ajax-loading {
			opacity: 0.3;
		}

		// Notices
		.notice.success, .notice.error {
			display: none;
			&.field { display: block; }
		}

		// Field Error
		.field-error {
			border-color: $alert-color;
		}

		// Form Fields
		input[type=text],
		input[type=password],
		input[type=search],
		input[type=email],
		select {
			border: $input-border;
			box-shadow: none;
			margin-bottom: $input-spacing;
			color: $dark-gray;
			padding: $input-padding;
			height: $input-height;
			&:focus { border: $input-border-focus; }
		}
		textarea {
			box-shadow: none;
			border: $input-border;
			color: $dark-gray;
			padding: $input-padding;
			margin-bottom: $input-spacing;
			&:focus { border: $input-border-focus; }
		}
		::-webkit-input-placeholder { color:#B8B8B8; }
		::-moz-placeholder { color:#B8B8B8; } /* firefox 19+ */
		:-ms-input-placeholder { color:#B8B8B8; } /* ie */
		input:-moz-placeholder { color:#B8B8B8; }

		// Submit button
		input[type=submit], button {
			margin-bottom: 0;
			&:focus {
				background: $primary-color;
				color: white;
			}
		}

	}

	span.notice-brochure {
		display: none;
	}

}