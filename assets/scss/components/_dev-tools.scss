
// Dev Tools
// - - - - - - - - - - - - - - - - - - - - - - - -
@if $foundation-ready {
    div#dev-tools {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 9999;
        background-color: $primary-color;
        color: white;
        font-weight: bold;
        text-transform: uppercase;
        font-size: 0.6em;
        letter-spacing: -0.31em;
        >div {
            letter-spacing: 0;
            padding: 5px;
            display: inline-block;
            &:nth-of-type(1) { background-color: $primary-color; }
            &:nth-of-type(2) {
                background-color: darken($primary-color, 5%);
                i.icon {
                    display: inline-block;
                    margin-right: 1px;
                }
            }
            &:nth-of-type(3) {
                cursor: pointer;
                background-color: darken($primary-color, 8%);
                &.enabled {
                    background-color: #AD2727;
                }
                &:hover {
                    color: darken($primary-color, 8%);
                    background-color: white;
                }
                span {
                    display: none;
                    &.loading {
                        display: inline-block;
                        >i {
                            display: block;
                            width: 9px;
                            height: 8px;
                            margin: 0 auto;
                            @include animation-keyframes(rotating 2s linear infinite) {
                                from {
                                    transform: rotate(0deg);
                                }
                                to {
                                    transform: rotate(360deg);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}