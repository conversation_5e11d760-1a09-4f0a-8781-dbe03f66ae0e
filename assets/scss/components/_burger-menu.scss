
// Burger Menu
// - - - - - - - - - - - - - - - - - - - - - - - -
$burger-menu-color       : $primary-color;
$burger-menu-opacity     : 0.8;
$burger-menu-size        : 23px;
$burger-menu-bar-height  : 3px;
$burger-menu-bar-spacing : 5px;

//  Style Override
// - - - - - - - - - - - - - - - - - - - - - - - -
@include style-override() {
    [data-responsive-menu-trigger] {
    	@include transitions();
        display: inline-block;
        padding: 10px;
        opacity: $burger-menu-opacity;
        margin-top: 8px;
        &:hover {
        	opacity: 1;
        }
    }
    .burger-menu {
        @include burger($burger-menu-size, $burger-menu-bar-height, $burger-menu-bar-spacing, $burger-menu-color);
        &.active {
            @include burger-to-cross;
        }
    }
}