
//  Notices
// - - - - - - - - - - - - - - - - - - - - - - - -
$notice-error-color: darken($alert-color, 15%);
$notice-error-background-color: lighten($alert-color, 25%);
$notice-error-border-color: lighten($alert-color, 15%);
$notice-success-color: darken($success-color, 15%);
$notice-success-background-color: lighten($success-color, 25%);
$notice-success-border-color: lighten($success-color, 15%);

//  Style Override
// - - - - - - - - - - - - - - - - - - - - - - - -
@include style-override() {

	.notice {

		padding: $global-padding;
		margin-bottom: $global-margin;

		// Error
		&.error {
			color: $notice-error-color;
			background-color: $notice-error-background-color;
			border: 1px solid $notice-error-border-color;

			// Field Error
			&.field {
				border: none;
				background: none;
				font-weight: bold;
				padding: 0;
				font-size: 0.7em;
			    font-style: italic;
			    padding: 0;
			    margin-bottom: 0;
			}

		}

		// Success
		&.success {
			color: $notice-success-color;
			background-color: $notice-success-background-color;
			border: 1px solid $notice-success-border-color;
		}

	}

}