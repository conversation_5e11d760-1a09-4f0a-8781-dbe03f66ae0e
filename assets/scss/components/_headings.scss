
//  Headings
// - - - - - - - - - - - - - - - - - - - - - - - -
$header-font-family: $alt-font-family;
$header-font-weight: $global-weight-normal;
$header-color: $secondary-color; 
$header-sizes: (
    small: (
        'h1': 30,
        'h2': 30,
        'h3': 26,
        'h4': 22,
        'h5': 22,
        'h6': 22,
    ),
    medium: (
        'h1': 48,
        'h2': 44, 
        'h3': 38,
        'h4': 30,
        'h5': 30,
        'h6': 30,
    ),
);

//  Style Override
// - - - - - - - - - - - - - - - - - - - - - - - -
@include style-override() {
	h1 {
        @include breakpoint(medium down) {
            font-size: 2.6em;
            line-height: 1.3em;
        }
        @include breakpoint(small down) {
            font-size: 2em;
        }
    }
}