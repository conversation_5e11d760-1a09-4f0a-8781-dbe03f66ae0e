
//  Lightboxes
// - - - - - - - - - - - - - - - - - - - - - - - -
$lightbox-overlay-color                     : rgba($primary-color, .9);
$lightbox-background-color                  : white;
$lightbox-close-link-color                  : rgba($dark-gray, 0.5);
$lightbox-close-link-color-hover            : $dark-gray;
$lightbox-video-close-link-background-color : #000;
$lightbox-padding				            : 50px 40px;
$lightbox-loading-color 		            : $primary-color;
$lightbox-loading-border-width	            : 2px;
$lightbox-loading-size			            : 20px;

//  Style Override
// - - - - - - - - - - - - - - - - - - - - - - - -
@include style-override() {

	// Hide the lightbox element by default
	.lightbox {
		display: none;
	}

	// Show the Lightbox
	.js-lightbox, .js-lightbox-redirect, .js-lightbox-video {
		display: none;
		position:fixed;
		top: 0; right: 0; bottom: 0; left: 0;
		z-index: 2147483647;
		text-align: center;
		white-space: nowrap;
		background: $lightbox-overlay-color;

		@include breakpoint(small only) {
			padding: 0 10px;
		}

		&:before {
			content: '';
			display: inline-block;
			height: 100%;
			vertical-align: middle;
			margin-right: -0.25em;
		}
		&-content {
			max-width: 90%;
			position: relative;
			text-align: left;
			vertical-align: middle;
			display: inline-block;
			overflow: auto;
			padding: $lightbox-padding;
			background-color: $lightbox-background-color;
			margin-left: 5%;
			margin-right: 5%;
			max-height: 95%;
			white-space: normal;
			min-width: 600px;
			width: 67%;

			input[type=submit], button {
				max-width: 100%;

				@include breakpoint(small only) {
					width: 100%;
					padding-left: 5px;
					padding-right: 5px;
				}
			}
			@include breakpoint(medium down) {
				min-width: 500px;
				width: 80%;
				margin: 0;
			}
			@include breakpoint(small only) {
				width: 100%;
				margin: 0;
				min-width: 0;
//				padding: 30px;
			}
		}
		&-inner {
			display: block;
		}
		&-close-icon {
			position: absolute;
			z-index: 9999;
			top: 15px;
		    right: 15px;
		    font-size: 1em;
			line-height: 25px;
			width: 25px;
			cursor: pointer;
			text-align: center;
			font-family: Arial, sans-serif;
			color: $lightbox-close-link-color;
			@include transitions();
			&:hover {
				color: $lightbox-close-link-color-hover;
			}
		}

		// Videos / Iframes only
		&-iframe {
			.js-lightbox-content {
				padding: 0;
				background-color: transparent;
				width: 80%;
				height: 80%;
				@include breakpoint(medium only) {
					height: 400px;
				}
				@include breakpoint(small only) {
					height: 300px;
				}
			}
			.js-lightbox-close-icon {
				top: 0;
				right: 0;
				background-color: $lightbox-video-close-link-background-color;
    			width: auto;
    			padding: 5px 15px;
    			&:hover {
    				background-color: none;
    				color: white;
    			}
			}
			iframe {
				display: block;
				width: 100%;
				height: 100%;
			}
		}
		&-video {
			.js-lightbox-video-content {
				padding: 0 !important;
				background-color: transparent;
				width: 60%;
				height: auto !important;
				max-height: 100% !important;
				@include breakpoint(medium down) {
					width: 80%;
				}
			}
			.js-lightbox-close-icon {
				top: 0;
				right: 0;
				background-color: $lightbox-video-close-link-background-color;
    			width: auto;
    			padding: 5px 15px;
    			&:hover {
    				background-color: none;
    				color: white;
    			}
			}
			.video-container {
				position: relative;
				padding-bottom: 56.25%;
				padding-top: 30px;
				height: 0;
				overflow: hidden;
			}
			iframe {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
		}
	}

	// Loading Spinner (Ajax Only)
	.loading-spinner {
		@include animation-keyframes(.75s all infinite) {
			0% {
				-webkit-transform: rotate(0deg);
				-moz-transform: rotate(0deg);
				-ms-transform: rotate(0deg);
				-o-transform: rotate(0deg);
				transform: rotate(0deg);
			}
			100% {
				-webkit-transform: rotate(360deg);
				-moz-transform: rotate(360deg);
				-ms-transform: rotate(360deg);
				-o-transform: rotate(360deg);
				transform: rotate(360deg);
			}
		}
		height: $lightbox-loading-size;
		width: $lightbox-loading-size;
		border: $lightbox-loading-border-width solid $lightbox-loading-color;
		border-right-color: transparent;
		border-radius: 50%;
		display: inline-block;
		@include horizontal-vertical-align();
		top: 40%;
	}

}