
// Content Builder
// - - - - - - - - - - - - - - - - - - - - - - - -
$content-builder-elements-margin: 3rem;

//  Style Override
// - - - - - - - - - - - - - - - - - - - - - - - -
@include style-override() {

	.page-layout[data-layout="standard_page"] {
		div.main-column-left > div.row > * {
			margin-bottom: $content-builder-elements-margin;
			&:last-child { margin-bottom: 0; }
		}
	}
	.content-builder {
		width: 100%;
		
		div.column { margin-bottom: $content-builder-elements-margin; }
		& > div > div > div.column { margin-bottom: 0; }
	}

}