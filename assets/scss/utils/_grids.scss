
//  Grids Mixins
// - - - - - - - - - - - - - - - - - - - - - - - -

// Columns Mixin
@mixin col($columns) {
	@each $breakpoint, $gutter in $grid-column-gutter {
		$padding: rem-calc($gutter);
		@include breakpoint($breakpoint) {
			padding-top: $padding;
		}
    }
    &:nth-of-type(-n+#{$columns}) { padding-top: 0; }
}

// Grid Classes
@each $breakpoint in $breakpoint-classes {
	@include breakpoint($breakpoint up) {
		.grid-#{$breakpoint} {
			@for $i from 1 through 10 {
				&-#{$i} {
					@include flex-grid-layout($i, 'div.column');
				}
				&-#{$i} > div.column {
					@include col($i);
				}
			}
		}
	}
}