/***
 *    ███████╗ █████╗ ███╗   ██╗██████╗ ██████╗  ██████╗ ██╗  ██╗ ██████╗ 
 *    ██╔════╝██╔══██╗████╗  ██║██╔══██╗██╔══██╗██╔═══██╗╚██╗██╔╝██╔════╝ 
 *    ███████╗███████║██╔██╗ ██║██║  ██║██████╔╝██║   ██║ ╚███╔╝ ███████╗ 
 *    ╚════██║██╔══██║██║╚██╗██║██║  ██║██╔══██╗██║   ██║ ██╔██╗ ██╔═══██╗
 *    ███████║██║  ██║██║ ╚████║██████╔╝██████╔╝╚██████╔╝██╔╝ ██╗╚██████╔╝
 *    ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ 
 *    -------------------------------------------------------------------
 *    [ Main SCSS File ]
 */

//  Import Helper
// - - - - - - - - - - - - - - - - - - - - - - - - 
@import 'helper';

//  Include Foundation Components w/ Flex
// - - - - - - - - - - - - - - - - - - - - - - - - 
@include foundation-everything(true);

//  Foundation State (Avoid duplicate sass code)
// - - - - - - - - - - - - - - - - - - - - - - - -
$foundation-ready: true;

//  Override Components Styles
// - - - - - - - - - - - - - - - - - - - - - - - - 
@import 'components/import.scss';

//  Override General Styles
// - - - - - - - - - - - - - - - - - - - - - - - - 
@import 'override';

//  Add Elements transitions
// - - - - - - - - - - - - - - - - - - - - - - - - 
@each $element in $elements {
	#{$element} {
		@include transitions();
	}
}