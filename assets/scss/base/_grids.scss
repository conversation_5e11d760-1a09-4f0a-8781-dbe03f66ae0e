//  Gutters
// - - - - - - - - - - - - - - - - - - - - - - - -
$gutter: 50px;
$extra-gutter: 15px;
$grid-column-gutter: (
    small: $gutter,
    medium: $gutter,
    large: 30px,
);

//  Container Spacing (padding-top & bottom)
// - - - - - - - - - - - - - - - - - - - - - - - -
$responsive-container-spacing: (
    small: 50px,
    medium: 70px,
    large: 100px,
    xlarge: 100px
);

//  Container Mixin
// - - - - - - - - - - - - - - - - - - - - - - - -
@mixin container($force: true) {
    margin-left: auto;
    margin-right: auto;
    @include breakpoint(xlarge up) {
        @if $force { max-width: 1200px !important; }
        else { max-width: 1200px; }
    }
    @include breakpoint(medium only and large only) {
        @if $force { max-width: 75rem !important; }
        else { max-width: 75rem; }
    }
}

//  Rows
// - - - - - - - - - - - - - - - - - - - - - - - -
.row {
    // This is actually a Safari fix.
    display: -webkit-flex;
    &.align-center { -webkit-justify-content: center; }
}
div.row.large-row {
    // Large Rows Only (full-width, outside of the container)
    max-width: 100%;
    >div.column {
        padding-left: 0;
        padding-right: 0;
        >div.row {
            max-width: 100%;
        }
    }
}
.height-100 {
    height: 100%;
}

//  Rows - Containers
// - - - - - - - - - - - - - - - - - - - - - - - -
div.row {
    @include container(false);
    &.container {
        @include container();
    }
}