/*!
Animate.css - http://daneden.me/animate
Licensed under the MIT license - http://opensource.org/licenses/MIT

Copyright (c) 2015 <PERSON> Eden
*/

// CONFIGURATION
// ====================================

// Animation class hook
// Set the class name to trigger animations
// Defaults to "animated"
$AnimateHook: "animated";

// Animation duration
// Sets the hook's animation duration
// Defaults to 1s
$AnimateDuration: 1s;

// FUNCTIONS
// ====================================
//
// Include functions and mixins provided by Animate.css

@import "_functions/mixins";


// BASE STYLES
// ====================================
//
// Setting styles for the animation hook class

.#{$AnimateHook} {
  animation-duration: $AnimateDuration;
  animation-fill-mode: both;

  // Modifier for infinite repetition
  &--infinite {
    animation-iteration-count: infinite;
  }
}

// IMPORT
// ====================================
//
// Imports desired animations. Configuration is pretty straight-forward; just
// remove the import rules for animations you don't want.

// Attention seekers
@import "attention_seekers/bounce";
@import "attention_seekers/flash";
@import "attention_seekers/pulse";
@import "attention_seekers/rubberBand";
@import "attention_seekers/shake";
@import "attention_seekers/swing";
@import "attention_seekers/tada";
@import "attention_seekers/wobble";

// Bouncing entrances
@import "bouncing_entrances/bounceIn";
@import "bouncing_entrances/bounceInDown";
@import "bouncing_entrances/bounceInLeft";
@import "bouncing_entrances/bounceInRight";
@import "bouncing_entrances/bounceInUp";

// Bouncing exits
@import "bouncing_exits/bounceOut";
@import "bouncing_exits/bounceOutDown";
@import "bouncing_exits/bounceOutLeft";
@import "bouncing_exits/bounceOutRight";
@import "bouncing_exits/bounceOutUp";

// Fading entrances
@import "fading_entrances/fadeIn";
@import "fading_entrances/fadeInDown";
@import "fading_entrances/fadeInLeft";
@import "fading_entrances/fadeInRight";
@import "fading_entrances/fadeInUp";
@import "fading_entrances/fadeInDownBig";
@import "fading_entrances/fadeInLeftBig";
@import "fading_entrances/fadeInRightBig";
@import "fading_entrances/fadeInUpBig";

// Fading exits
@import "fading_exits/fadeOut";
@import "fading_exits/fadeOutDown";
@import "fading_exits/fadeOutLeft";
@import "fading_exits/fadeOutRight";
@import "fading_exits/fadeOutUp";
@import "fading_exits/fadeOutDownBig";
@import "fading_exits/fadeOutLeftBig";
@import "fading_exits/fadeOutRightBig";
@import "fading_exits/fadeOutUpBig";

// Flippers
@import "flippers/flip";
@import "flippers/flipInX";
@import "flippers/flipInY";
@import "flippers/flipOutX";
@import "flippers/flipOutY";

// Lightspeed
@import "lightspeed/lightSpeedIn";
@import "lightspeed/lightSpeedOut";

// Rotating entrances
@import "rotating_entrances/rotateIn";
@import "rotating_entrances/rotateInDownLeft";
@import "rotating_entrances/rotateInDownRight";
@import "rotating_entrances/rotateInUpLeft";
@import "rotating_entrances/rotateInUpRight";

// Rotating exits
@import "rotating_exits/rotateOut";
@import "rotating_exits/rotateOutDownLeft";
@import "rotating_exits/rotateOutDownRight";
@import "rotating_exits/rotateOutUpLeft";
@import "rotating_exits/rotateOutUpRight";

// Specials
@import "specials/hinge";
@import "specials/rollIn";
@import "specials/rollOut";

// Zooming entrances
@import "zooming_entrances/zoomIn";
@import "zooming_entrances/zoomInDown";
@import "zooming_entrances/zoomInLeft";
@import "zooming_entrances/zoomInRight";
@import "zooming_entrances/zoomInUp";

// Zooming exits
@import "zooming_exits/zoomOut";
@import "zooming_exits/zoomOutDown";
@import "zooming_exits/zoomOutLeft";
@import "zooming_exits/zoomOutRight";
@import "zooming_exits/zoomOutUp";

