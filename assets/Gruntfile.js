/**
 * --------------------------------------------------------------------
 * [DO NOT TOUCH THIS FILE EXCEPT IF YOU REALLY KNOW WHAT YOU'RE DOING]
 * --------------------------------------------------------------------
 */

'use strict';

module.exports = function(grunt)
{

	/**
	 * [BUILD FUNCTION]
	 * ----------------
	 */
	grunt.registerTask('build', function() {
		grunt.config.set('this_version', 'v0.0.74');
	});
	grunt.task.run('build');

	// ------------------------------------------------------------------------------------

	/**
	 * [PROJECT CONFIGURATION]
	 * -----------------------
	 */
	grunt.initConfig({

		/**
		 * [JSON FILES]
		 * ------------
		 */
		pkg: grunt.file.readJSON('package.json'),

		// --------------------------------------------------------------------------------

		/**
		 * [SASS FILES]
		 * ------------
		 */
		sass: {
			options: {
				outputStyle: 'compressed', // nested, compressed
				sourceMap: false,
			},
			dist: {
				expand: true,
				cwd: '',
				src: ['scss/compile.temp.scss', 'scss/main.scss'],
	            dest: 'css/',
	            rename: function(dest, src) {
	            	if(src == 'scss/main.scss') {
	            		return 'css/wt.main.min.css';
	            	} else {
						return 'css/wt.panels.min.css';
	            	}
				}
			}
		},

		// --------------------------------------------------------------------------------

		/**
		 * [JS FILES (uglify)]
		 * -------------------
		 */
		uglify: {
			options: {
				banner: '/*! JS Minimized - <%= grunt.template.today("yyyy-mm-dd") %> */\n'
			},
			master_function: {
				expand: true,
				cwd: '',
				src: ['../system/panels/**/assets/js/*.js', 'js/plugins/*.js', 'js/sandbox/*.js', '../panels/**/assets/js/*.js'],
	            dest: 'js/',
	            rename: function(dest, src) {
	            	if(src.indexOf('js/plugins/') >= 0 || src.indexOf('js/sandbox/') >= 0) {
	            		var filename = src.replace('js/plugins/', '').replace('js/sandbox/', '').replace('.js', '');
	            		return 'js/wt.' + filename + '.src.min.js';
	            	} else {
						var panel = src.split('/')[2];
						var filename = src.replace(/^.*[\\\/]/, '').replace('.js', '').replace('.scss', '');
						return 'js/wt.' + panel + '.panel.min.js';
					}
	            }
			},
		},

		// --------------------------------------------------------------------------------

		/**
		 * [AUTOPREFIX CSS]
		 * -------------------
		 */
		postcss: {
            options: {
                map: true,
                processors: [
                    require('autoprefixer')({
                        browsers: ['last 10 versions']
                    })
                ]
            },
            dist: {
                src: 'css/wt.*.min.css'
            }
        },

		// --------------------------------------------------------------------------------

		/**
		 * [CONCAT CSS & JS FILES]
		 * -----------------------
		 */
		concat: {
			options: {
				stripBanners: true,
				separator: ''
			},
			global: {
				src: [
					'scss/_helper.scss',
					'../system/panels/**/scss/*.scss',
					'../panels/**/scss/*.scss'
				],
				dest: 'scss/compile.temp.scss'
			},
			panels_js: {
				src: [
					'js/*.panel.min.js'
				],
				dest: 'js/panels.js'
			},
			src_js: {
				src: [
					'bower_components/jquery/dist/jquery.min.js',
					'js/plugins/featherlight.min.js',
					'js/*.src.min.js'
				],
				dest: 'js/sb.main.js'
			},
		},

		// --------------------------------------------------------------------------------

		/**
		 * [GRUNT WATCH TASKS]
		 * -------------------
		 */
		watch: {
			scss_files: {
				files: [
					'../system/panels/**/assets/scss/*.scss',
					'../panels/**/assets/scss/*.scss',
					'scss/*.scss',
					'scss/**/*.scss',
					'scss/**/**/*.scss',
					'scss/**/**/**/*.scss',
					'!scss/compile.temp.scss',
				],
				tasks: [
					'exec:remove_previous_css',
					'concat:global',
					'sass',
					'postcss:dist',
					'uglify:master_function',
					'clean:files',
					'exec:gzip_css_module',
					'exec:gzip_css_rename_module'
				],
				options: {
				  spawn: true, // change to false to get working with grunt.event.on
				},
			},
			js_files: {
				files: [
					'../system/panels/**/assets/js/*.js',
					'../panels/**/assets/js/*.js',
					'js/plugins/*.js',
					'js/sandbox/*.js',
				],
				tasks: [
					'exec:remove_previous_js',
					'uglify:master_function',
					'concat:panels_js',
					'concat:src_js',
					'clean:files',
					'exec:gzip_js_module',
					'exec:gzip_js_rename_module'
				],
				options: {
				  spawn: true, // change to false to get working with grunt.event.on
				},
			}
		},

		// --------------------------------------------------------------------------------

		/**
		 * [CLEAN ALL JS & CSS & TEMPORARY FILES (remove)]
		 * -----------------------------------
		 */
		clean: {
			options: {
				force: true
			},
			files: ["scss/*.temp.scss" , "js/*.panel.min.js", "js/*.src.min.js"],
		},

		// --------------------------------------------------------------------------------

		/**
		 * [CSS & JS GZIP & RENAME]
		 * ------------------------
		 */
		exec: {
			remove_previous_css_js: {
				command: 'touch css/wt5._TEMP.css; touch js/wt5._TEMP.js; rm js/wt5.*; rm css/wt5.*;',
				stdout: false,
				stderr: true
			},
			remove_previous_css: {
				command: 'touch css/wt5._TEMP.css; rm css/wt5.*.css',
				stdout: false,
				stderr: true
			},
			remove_previous_js: {
				command: 'touch js/wt5._TEMP.js; rm js/wt5.*.js',
				stdout: false,
				stderr: true
			},
			gzip_js_module: {
				command: 'gzip -9 js/panels.js && gzip -9 js/sb.main.js',
				stdout: false,
				stderr: true
			},
			gzip_css_module: {
				command: 'gzip -9 css/wt.panels.min.css && gzip -9 css/wt.main.min.css',
				stdout: false,
				stderr: false
			},
			gzip_css_rename_module: {
				command: 'mv css/wt.panels.min.css.gz css/wt5.panels.<%= this_version %>.gz.css && mv css/wt.main.min.css.gz css/wt5.main.<%= this_version %>.gz.css',
				stdout: false,
				stderr: false
			},
			gzip_js_rename_module: {
				command: 'mv js/panels.js.gz js/wt5.panels.<%= this_version %>.min.gz.js && mv js/sb.main.js.gz js/wt5.main.<%= this_version %>.min.gz.js',
				stdout: false,
				stderr: false
			},
		},

		// --------------------------------------------------------------------------------

	});

	// ------------------------------------------------------------------------------------

	/**
	 * [LOAD NPM MODULES]
	 * ------------------
	 */
	grunt.loadNpmTasks('grunt-contrib-uglify');
	grunt.loadNpmTasks('grunt-contrib-watch');
	grunt.loadNpmTasks('grunt-contrib-concat');
	grunt.loadNpmTasks('grunt-sass');
	grunt.loadNpmTasks('grunt-exec');
	grunt.loadNpmTasks('grunt-contrib-clean');
	grunt.loadNpmTasks('grunt-postcss');

	// ------------------------------------------------------------------------------------

	/**
	 * [DEFAULT GRUNT TASKS]
	 * ---------------------
	 */
	grunt.registerTask('default', [
		'exec:remove_previous_css_js',
		'concat:global',
		'sass',
		'postcss:dist',
		'uglify:master_function',
		'concat:panels_js',
		'concat:src_js',
		'clean:files',
		'exec:gzip_css_module',
		'exec:gzip_css_rename_module',
		'exec:gzip_js_module',
		'exec:gzip_js_rename_module'
	]);

};
