<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="azo_sansmedium_italic" horiz-adv-x="1220" >
<font-face units-per-em="2048" ascent="1581" descent="-467" />
<missing-glyph horiz-adv-x="481" />
<glyph unicode="&#xfb01;" horiz-adv-x="1259" d="M96 797l37 211h150l39 221q36 193 143 276q106 82 276 82q72 0 164 -20l-39 -219q-65 24 -131 24t-108 -33q-47 -40 -64 -145l-33 -186h242l-37 -211h-241l-142 -797h-248l142 797h-150zM778 0l178 1008h248l-178 -1008h-248zM987 1343q0 67 47.5 113.5t114.5 46.5 q64 0 106.5 -42t42.5 -103q0 -67 -48.5 -113.5t-116.5 -46.5q-62 0 -104 42t-42 103z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1259" d="M96 797l37 211h150l39 221q36 193 143 276q106 82 276 82q72 0 164 -20l-39 -219q-65 24 -131 24t-108 -33q-47 -40 -64 -145l-33 -186h242l-37 -211h-241l-142 -797h-248l142 797h-150zM778 0l277 1565h248l-277 -1565h-248z" />
<glyph horiz-adv-x="2" />
<glyph horiz-adv-x="2" />
<glyph horiz-adv-x="682" />
<glyph unicode="&#xd;" horiz-adv-x="710" />
<glyph unicode=" "  horiz-adv-x="481" />
<glyph unicode="&#x09;" horiz-adv-x="481" />
<glyph unicode="&#xa0;" horiz-adv-x="481" />
<glyph unicode="!" horiz-adv-x="606" d="M82 123q0 67 48 113.5t116 46.5q62 0 104.5 -42.5t42.5 -103.5q0 -67 -48.5 -113.5t-115.5 -46.5q-64 0 -105.5 42.5t-41.5 103.5zM217 475l113 1008h291l-242 -1008h-162z" />
<glyph unicode="&#x22;" horiz-adv-x="753" d="M203 956l65 564h217l-135 -564h-147zM559 956l66 564h217l-135 -564h-148z" />
<glyph unicode="#" horiz-adv-x="1261" d="M6 438l74 158h235l136 291h-236l74 157h235l205 439h168l-205 -439h242l205 439h168l-205 -439h235l-73 -157h-236l-135 -291h235l-73 -158h-236l-205 -438h-168l205 438h-241l-205 -438h-168l205 438h-236zM483 596h242l135 291h-242z" />
<glyph unicode="$" horiz-adv-x="1177" d="M111 215l45 252q100 -70 214 -112t214 -42q90 0 150.5 37.5t60.5 112.5q0 15 -3.5 29t-7.5 25t-15 23t-18.5 19.5t-26 18.5t-28.5 17t-34.5 17t-37.5 16.5t-43.5 18.5t-45.5 19q-61 26 -101 46.5t-82.5 51t-67.5 62.5t-41 77.5t-16 100.5q0 161 119 263.5t303 116.5 l45 254h195l-47 -266q138 -23 266 -86l-43 -246q-95 59 -198 94t-187 35q-87 0 -143 -36t-56 -105q0 -21 5 -38t19.5 -32t27 -25.5t41 -24t46 -21t58 -24t63.5 -25.5q76 -32 127 -62t95.5 -71.5t66 -96.5t21.5 -125q0 -171 -121 -272t-314 -113l-45 -254h-194l47 262 q-173 29 -313 109z" />
<glyph unicode="%" horiz-adv-x="1902" d="M193 1077q0 78 26.5 153.5t74.5 137t123 99.5t163 38q150 0 244 -94t94 -244q0 -105 -45 -201.5t-135.5 -161.5t-207.5 -65q-150 0 -243.5 94t-93.5 244zM254 0l1288 1483h193l-1289 -1483h-192zM365 1090q0 -78 45 -135.5t125 -57.5q91 0 150.5 79t59.5 179 q0 78 -45 135.5t-125 57.5q-91 0 -150.5 -79t-59.5 -179zM1071 315q0 78 26.5 153.5t74.5 137t123 99.5t163 38q150 0 244 -93.5t94 -243.5q0 -62 -16.5 -123.5t-49.5 -116.5t-79 -97t-108.5 -67t-133.5 -25q-150 0 -244 94t-94 244zM1243 328q0 -78 45 -135.5t125 -57.5 q91 0 151 79t60 179q0 78 -45 135.5t-125 57.5q-91 0 -151 -79t-60 -179z" />
<glyph unicode="&#x26;" horiz-adv-x="1445" d="M94 365q0 147 95 260.5t247 175.5l17 8l-29 39q-98 131 -98 260q0 111 61 202.5t163 143t220 51.5q156 0 262.5 -88t106.5 -233q0 -120 -74.5 -218t-194.5 -163l-79 -43l204 -273l219 402h250l-329 -586l227 -299l-2 -4h-293l-96 127q-153 -150 -396 -150 q-214 0 -347.5 107.5t-133.5 280.5zM342 393q0 -88 68.5 -143t183.5 -55q158 0 244 106l-265 348l-51 -24q-77 -35 -128.5 -93.5t-51.5 -138.5zM569 1114q0 -66 60 -141l43 -58l78 48q143 86 143 192q0 59 -40 97t-103 38q-74 0 -127.5 -51t-53.5 -125z" />
<glyph unicode="'" horiz-adv-x="397" d="M203 956l65 564h217l-135 -564h-147z" />
<glyph unicode="(" horiz-adv-x="704" d="M123 250q0 384 147 749q128 310 344 523h248q-238 -227 -377 -572q-135 -338 -135 -704q0 -389 154 -682h-213q-168 291 -168 686z" />
<glyph unicode=")" horiz-adv-x="704" d="M-143 -436q237 226 376 571q136 340 136 705q0 389 -154 682h213q168 -291 168 -686q0 -385 -147 -750q-127 -307 -345 -522h-247z" />
<glyph unicode="*" horiz-adv-x="915" d="M162 1137l78 153l258 -116l-49 -97zM205 819l246 225l73 -63l-205 -262zM526 1188l21 301h166l-86 -301h-101zM555 981l94 59l160 -229l-150 -92zM649 1167l295 123l21 -157l-301 -64z" />
<glyph unicode="+" horiz-adv-x="1202" d="M133 649l33 187h395l72 409h196l-71 -409h395l-33 -187h-395l-72 -411h-196l71 411h-395z" />
<glyph unicode="," horiz-adv-x="612" d="M-35 -301l219 598h244l-285 -598h-178z" />
<glyph unicode="-" horiz-adv-x="688" d="M94 463l37 209h487l-36 -209h-488z" />
<glyph unicode="." horiz-adv-x="612" d="M78 129q0 70 50 119t120 49q66 0 109.5 -44.5t43.5 -107.5q0 -70 -50 -119t-120 -49q-66 0 -109.5 44.5t-43.5 107.5z" />
<glyph unicode="/" horiz-adv-x="923" d="M-100 -78l1001 1639h205l-1002 -1639h-204z" />
<glyph unicode="0" horiz-adv-x="1214" d="M115 500q0 152 39 339.5t112 334.5q163 331 479 331q211 0 325 -135.5t114 -386.5q0 -151 -39.5 -339t-112.5 -335q-163 -332 -479 -332q-211 0 -324.5 136t-113.5 387zM375 516q0 -303 194 -303q157 0 248 219q46 111 76.5 260t30.5 275q0 303 -195 303 q-157 0 -248 -219q-45 -110 -75.5 -259.5t-30.5 -275.5z" />
<glyph unicode="1" horiz-adv-x="1009" d="M219 1057l43 248l621 200l-267 -1505h-260l207 1169z" />
<glyph unicode="2" horiz-adv-x="1153" d="M-47 4l563 582q88 91 137.5 147t96 119.5t65.5 116t19 106.5q0 94 -62 144.5t-168 50.5q-163 0 -342 -113l47 266q143 82 328 82q128 0 230 -43t166.5 -136.5t64.5 -225.5q0 -148 -82 -283.5t-258 -316.5l-256 -264l2 -5h504l-41 -231h-1012z" />
<glyph unicode="3" horiz-adv-x="1120" d="M27 109l47 264q92 -74 197 -118t204 -44q127 0 205 63q80 65 80 166q0 92 -75.5 144.5t-199.5 52.5h-143l37 209h153q76 0 138.5 23t99.5 63q57 60 57 147q0 93 -67 143t-172 50q-76 0 -163 -25.5t-153 -64.5l45 252q142 69 304 69q87 0 167.5 -23t148.5 -68t109 -120.5 t41 -171.5q0 -142 -81 -243q-66 -81 -193 -129v-5q96 -41 151.5 -117.5t55.5 -189.5q0 -104 -46 -191.5t-126 -148.5q-154 -119 -385 -119q-123 0 -236 36.5t-200 95.5z" />
<glyph unicode="4" horiz-adv-x="1304" d="M-6 342l948 1141h244l-164 -934h246l-37 -211h-246l-59 -338h-260l59 338h-729zM422 553l2 -4h338l43 246q20 120 55 286l-4 2q-154 -194 -211 -262z" />
<glyph unicode="5" horiz-adv-x="1136" d="M35 92l59 248q203 -129 395 -129q142 0 216 65q73 61 73 160q0 153 -223 226q-144 45 -362 49l188 772h747l-57 -232h-487l-88 -358q202 -23 327 -92q215 -118 215 -352q0 -105 -44 -196.5t-128 -156.5q-150 -119 -389 -119q-235 0 -442 115z" />
<glyph unicode="6" horiz-adv-x="1226" d="M88 440q0 78 20.5 152.5t62.5 146.5t83.5 128t105.5 132l404 484h317l-477 -555q-13 -15 -26 -33l2 -2q55 20 125 20q119 0 218.5 -50t160.5 -148t61 -226q0 -111 -45 -208.5t-125 -165.5q-159 -138 -391 -138q-99 0 -186 28t-157.5 83t-111.5 145.5t-41 206.5zM352 436 q0 -100 68 -167.5t180 -67.5q122 0 207 82q80 77 80 192q0 103 -70 167.5t-180 64.5q-117 0 -201 -78q-84 -81 -84 -193z" />
<glyph unicode="7" horiz-adv-x="1103" d="M94 0l727 1251h-628l40 232h998l2 -4l-858 -1479h-281z" />
<glyph unicode="8" horiz-adv-x="1282" d="M82 391q0 146 90 253t236 155v6q-73 45 -113.5 115.5t-40.5 158.5q0 94 46 177.5t128 142.5q148 106 348 106q126 0 229.5 -43.5t167.5 -131t64 -202.5q0 -119 -68 -213t-194 -141v-4q93 -47 142.5 -131t49.5 -190q0 -105 -48 -194.5t-134 -154.5q-163 -123 -391 -123 q-139 0 -254 47t-186.5 142.5t-71.5 224.5zM346 416q0 -97 72 -157t188 -60q129 0 211 71q86 73 86 181q0 97 -75 161t-185 64q-131 0 -221 -86q-76 -73 -76 -174zM504 1065q0 -88 66 -142.5t163 -54.5q114 0 187 70q67 60 67 154q0 89 -64.5 141.5t-160.5 52.5 q-104 0 -180 -63q-78 -65 -78 -158z" />
<glyph unicode="9" horiz-adv-x="1208" d="M154 995q0 107 45.5 205t128.5 168q75 67 174 102t205 35q97 0 186.5 -30t159.5 -85t111.5 -139.5t41.5 -185.5q0 -77 -20.5 -150.5t-62.5 -147t-82 -130t-103 -133.5l-416 -504h-319l491 584l-2 4q-59 -19 -114 -19q-82 0 -158 30t-135.5 84t-95 135.5t-35.5 176.5z M412 1012q0 -104 68.5 -168t174.5 -64q119 0 203 84q82 79 82 189q0 98 -68.5 163.5t-177.5 65.5q-116 0 -200 -78q-82 -79 -82 -192z" />
<glyph unicode=":" horiz-adv-x="612" d="M84 125q0 65 49.5 112.5t116.5 47.5q62 0 104.5 -43.5t42.5 -104.5q0 -67 -49.5 -113.5t-116.5 -46.5q-63 0 -105 43t-42 105zM215 870q0 65 49.5 112.5t116.5 47.5q62 0 104.5 -43t42.5 -104q0 -67 -49.5 -113.5t-116.5 -46.5q-63 0 -105 42.5t-42 104.5z" />
<glyph unicode=";" horiz-adv-x="612" d="M-35 -301l219 598h244l-285 -598h-178zM215 870q0 65 49.5 112.5t116.5 47.5q62 0 104.5 -43t42.5 -104q0 -67 -49.5 -113.5t-116.5 -46.5q-63 0 -105 42.5t-42 104.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1417" d="M197 674l24 135l1159 438l-34 -198l-807 -308v-4l698 -307l-35 -194z" />
<glyph unicode="=" horiz-adv-x="1202" d="M94 432l33 186h987l-33 -186h-987zM170 866l33 187h987l-33 -187h-987z" />
<glyph unicode="&#x3e;" horiz-adv-x="1417" d="M121 236l35 198l807 307v4l-699 308l35 194l1006 -438l-25 -135z" />
<glyph unicode="?" horiz-adv-x="978" d="M158 123q0 67 48 113.5t116 46.5q62 0 104.5 -42.5t42.5 -103.5q0 -67 -48.5 -113.5t-115.5 -46.5q-64 0 -105.5 42.5t-41.5 103.5zM238 1212l45 252q100 41 221 41q136 0 249 -52.5t182 -153.5t69 -230q0 -186 -133 -314t-345 -151l-51 -180h-225l61 362q89 0 165 15 t137.5 46t96.5 83t35 121q0 106 -75 167.5t-189 61.5q-123 0 -243 -68z" />
<glyph unicode="@" horiz-adv-x="1630" d="M59 428q0 163 61.5 316t167.5 268.5t259.5 185.5t326.5 70q197 0 357.5 -87t252 -243t91.5 -350q0 -93 -22 -177t-64.5 -152t-112.5 -108t-159 -40q-163 0 -228 108h-4q-113 -110 -266 -110q-135 0 -217 90t-82 237q0 117 51 221.5t151.5 173t229.5 68.5q148 0 332 -108 l-62 -347q-10 -58 -10 -82q0 -61 31.5 -96t93.5 -35q74 0 124.5 53t70.5 126.5t20 160.5q0 118 -41 224t-114.5 185.5t-183.5 126t-239 46.5q-144 0 -273.5 -57.5t-220.5 -154t-144.5 -227.5t-53.5 -274q0 -254 160 -419.5t420 -165.5q212 0 391 118l47 -104 q-190 -129 -438 -129q-200 0 -360 85.5t-251.5 243.5t-91.5 359zM590 440q0 -84 43 -136t123 -52q112 0 194 94q0 59 11 127l43 240q-79 43 -152 43q-80 0 -141.5 -50.5t-91 -121t-29.5 -144.5z" />
<glyph unicode="A" horiz-adv-x="1447" d="M-90 0l856 1483h256l336 -1483h-268l-78 362h-621l-205 -362h-276zM512 575h453l-52 236q-35 161 -69 358h-4q-83 -159 -199 -364z" />
<glyph unicode="B" horiz-adv-x="1290" d="M59 0l263 1483h423q242 0 369 -80q69 -44 108.5 -113.5t39.5 -156.5q0 -123 -75.5 -219t-207.5 -138l-2 -4q107 -39 166 -117t59 -190q0 -102 -49.5 -194.5t-138.5 -155.5q-163 -115 -457 -115h-498zM360 227h250q179 0 258 70q74 67 74 168q0 98 -73.5 149.5 t-202.5 51.5h-228zM471 852h252q52 0 98.5 11.5t87.5 35t65.5 67.5t24.5 103q0 95 -69 143q-64 43 -207 43h-180z" />
<glyph unicode="C" horiz-adv-x="1363" d="M117 631q0 172 67.5 341.5t194.5 295.5q237 237 602 237q223 0 422 -94l-47 -262q-185 119 -393 119q-120 0 -224 -42t-180 -118q-89 -89 -135.5 -210.5t-46.5 -242.5q0 -206 131 -323t356 -117q204 0 379 90l-47 -268q-155 -60 -354 -60q-211 0 -374 75.5t-257 225 t-94 353.5z" />
<glyph unicode="D" horiz-adv-x="1499" d="M59 0l263 1483h368q350 0 553 -166q108 -88 166.5 -215t58.5 -283q0 -163 -64.5 -320t-191.5 -274q-246 -225 -651 -225h-502zM360 231h222q309 0 471 172q75 78 115 184.5t40 215.5q0 212 -143 332q-139 116 -389 116h-135z" />
<glyph unicode="E" horiz-adv-x="1126" d="M59 0l263 1483h874l-41 -232h-614l-66 -368h529l-39 -221h-529l-76 -431h635l-41 -231h-895z" />
<glyph unicode="F" horiz-adv-x="1134" d="M59 0l263 1483h890l-41 -232h-630l-70 -397h551l-39 -221h-553l-111 -633h-260z" />
<glyph unicode="G" horiz-adv-x="1480" d="M115 629q0 178 70.5 351t203.5 300q239 225 584 225q239 0 469 -116l-47 -263q-212 144 -437 144q-234 0 -393 -156q-91 -88 -140.5 -210.5t-49.5 -250.5q0 -202 123 -323t340 -121q100 0 196 29l53 307h-272l39 221h530l-122 -700q-203 -89 -441 -89q-205 0 -363.5 75 t-250.5 224.5t-92 352.5z" />
<glyph unicode="H" horiz-adv-x="1523" d="M59 0l263 1483h260l-105 -598h707l104 598h260l-262 -1483h-260l115 653h-707l-115 -653h-260z" />
<glyph unicode="I" horiz-adv-x="557" d="M59 0l263 1483h260l-263 -1483h-260z" />
<glyph unicode="J" horiz-adv-x="811" d="M-43 25l41 237q108 -55 201 -55q90 0 130.5 55.5t61.5 169.5l184 1051h261l-191 -1082q-37 -199 -127 -299q-114 -125 -321 -125q-121 0 -240 48z" />
<glyph unicode="K" horiz-adv-x="1331" d="M59 0l263 1483h260l-115 -645l4 -2l680 647h322l-728 -688l492 -795h-307l-469 772h-6l-136 -772h-260z" />
<glyph unicode="L" horiz-adv-x="1120" d="M59 0l263 1483h260l-222 -1252h672l-41 -231h-932z" />
<glyph unicode="M" horiz-adv-x="1738" d="M57 0l262 1483h240l350 -692l594 692h260l-262 -1483h-260l125 707q55 300 70 374h-4q-132 -159 -244 -290l-344 -402l-182 361q-105 210 -162 331h-4q-28 -184 -66 -399l-121 -682h-252z" />
<glyph unicode="N" horiz-adv-x="1583" d="M59 0l263 1483h241l402 -727q70 -128 198 -369l4 2q41 252 72 432l117 662h252l-262 -1483h-242l-402 727q-70 128 -198 369l-4 -2q-41 -252 -72 -432l-117 -662h-252z" />
<glyph unicode="O" horiz-adv-x="1642" d="M121 664q0 154 52 303t153 268q108 128 258.5 199t328.5 71q144 0 270.5 -48t220.5 -135t148 -217.5t54 -285.5q0 -154 -52 -303t-153 -268q-109 -128 -260 -199.5t-328 -71.5q-144 0 -270.5 48.5t-220 135.5t-147.5 217.5t-54 285.5zM381 684q0 -132 52.5 -237.5 t154 -168.5t235.5 -63q232 0 379 180q67 81 105.5 187t38.5 217q0 131 -53 237t-154.5 169t-235.5 63q-231 0 -379 -181q-67 -81 -105 -186.5t-38 -216.5z" />
<glyph unicode="P" horiz-adv-x="1280" d="M59 0l263 1483h407q291 0 432 -117q144 -118 144 -301q0 -98 -43 -199t-127 -172q-162 -137 -471 -137h-246l-99 -557h-260zM457 774h241q182 0 267 78q79 72 79 186q0 98 -69 152q-79 61 -244 61h-190z" />
<glyph unicode="Q" horiz-adv-x="1642" d="M121 664q0 154 52 303t153 268q108 128 258.5 199t328.5 71q144 0 270.5 -48t220.5 -135t148 -217.5t54 -285.5q0 -154 -52 -303t-153 -268q-141 -166 -336 -230v-4q207 -132 432 -217l-246 -141q-274 115 -508 326q-132 11 -245.5 63.5t-197.5 138.5t-131.5 210 t-47.5 270zM381 684q0 -132 52.5 -237.5t154 -168.5t235.5 -63q232 0 379 180q67 81 105.5 187t38.5 217q0 131 -53 237t-154.5 169t-235.5 63q-231 0 -379 -181q-67 -81 -105 -186.5t-38 -216.5z" />
<glyph unicode="R" horiz-adv-x="1298" d="M59 0l263 1483h419q270 0 406 -107q135 -109 135 -291q0 -90 -37.5 -183t-118.5 -161q-93 -78 -231 -108l301 -633h-287l-274 612h-207l-109 -612h-260zM463 813h246q161 0 241 74q72 68 72 162q0 95 -68 147q-72 55 -229 55h-184z" />
<glyph unicode="S" horiz-adv-x="1148" d="M53 131l49 277q98 -87 222 -143t239 -56q112 0 180 57q66 56 66 142q0 19 -4 36.5t-9 32t-17 29t-21.5 24.5t-28.5 23t-31.5 20.5t-38.5 21t-40.5 20.5t-46 21.5t-48.5 22.5q-63 30 -105.5 54t-88.5 59.5t-73 73t-44.5 89.5t-17.5 114q0 211 167 344q139 112 347 112 q113 0 229.5 -34t214.5 -97l-47 -264q-94 74 -207.5 119t-212.5 45q-104 0 -164 -49q-67 -55 -67 -146q0 -23 6 -42.5t13 -34.5t25 -31.5t30 -26t42 -25.5t45.5 -23.5t55.5 -26t59 -27.5q65 -31 107.5 -54.5t90.5 -60t75.5 -74.5t46 -93t18.5 -120q0 -102 -41 -191 t-119 -153q-142 -119 -364 -119q-130 0 -259.5 41t-232.5 113z" />
<glyph unicode="T" horiz-adv-x="1343" d="M193 1251l40 232h1223l-41 -232h-481l-221 -1251h-260l221 1251h-481z" />
<glyph unicode="U" horiz-adv-x="1492" d="M145 473q0 84 25 240l135 770h260l-135 -770q-20 -117 -20 -197q0 -301 307 -301q200 0 303 131q80 100 123 344l139 793h252l-141 -805q-32 -181 -76 -293t-111 -192q-88 -103 -217.5 -159.5t-292.5 -56.5q-254 0 -402.5 132.5t-148.5 363.5z" />
<glyph unicode="V" horiz-adv-x="1386" d="M180 1483h273l141 -723q28 -140 74 -402h4q113 218 211 396l397 729h272l-817 -1483h-256z" />
<glyph unicode="W" horiz-adv-x="2117" d="M193 1483h268l90 -721q25 -205 43 -391h4q85 186 186 393l351 719h190l96 -721q28 -211 47 -391h5q68 151 182 393l344 719h272l-719 -1483h-276l-84 629q-31 237 -43 364h-4q-44 -96 -168 -354l-311 -639h-277z" />
<glyph unicode="X" horiz-adv-x="1363" d="M-82 0l645 764l-350 719h291l117 -244q62 -129 131 -281h4q166 207 227 281l203 244h309l-604 -719l379 -764h-297l-131 268q-89 186 -142 301h-4q-132 -167 -241 -297l-228 -272h-309z" />
<glyph unicode="Y" horiz-adv-x="1363" d="M166 1483h291l135 -271q63 -123 153 -311h5q127 157 249 299l242 283h303l-700 -811l-119 -672h-260l119 670z" />
<glyph unicode="Z" horiz-adv-x="1241" d="M-59 4l950 1243l-2 4h-678l41 232h1122l2 -4l-950 -1243l2 -5h715l-41 -231h-1159z" />
<glyph unicode="[" horiz-adv-x="694" d="M-10 -436l346 1958h471l-33 -185h-244l-280 -1589h244l-33 -184h-471z" />
<glyph unicode="\" horiz-adv-x="923" d="M215 1561h188l388 -1639h-189z" />
<glyph unicode="]" horiz-adv-x="694" d="M-100 -436l32 184h244l281 1589h-246l33 185h473l-346 -1958h-471z" />
<glyph unicode="^" horiz-adv-x="1142" d="M162 741l502 764h172l231 -764h-199l-159 508h-4l-343 -508h-200z" />
<glyph unicode="_" horiz-adv-x="980" d="M-141 -299l20 121h981l-20 -121h-981z" />
<glyph unicode="`" horiz-adv-x="1046" d="M457 1489h235l96 -336h-170z" />
<glyph unicode="a" horiz-adv-x="1206" d="M78 412q0 121 46.5 240t135.5 208q170 170 428 170q201 0 445 -119l-160 -911h-246l14 80l-4 2q-121 -105 -270 -105q-167 0 -278 120.5t-111 314.5zM328 434q0 -117 60 -183.5t155 -66.5q121 0 233 109l86 483q-88 45 -194 45q-151 0 -244 -112q-48 -56 -72 -129 t-24 -146z" />
<glyph unicode="b" horiz-adv-x="1202" d="M61 33l271 1532h248l-109 -611l4 -2q111 78 250 78q172 0 284.5 -121.5t112.5 -312.5q0 -117 -45 -236t-135 -208q-178 -175 -489 -175q-197 0 -392 56zM338 203q74 -19 149 -19q184 0 287 115q44 51 71 124t27 146q0 114 -62 183t-165 69q-108 0 -211 -73z" />
<glyph unicode="c" horiz-adv-x="1032" d="M76 428q0 122 51 246.5t154 212.5q80 68 186.5 105.5t222.5 37.5q161 0 305 -59l-41 -236q-143 80 -274 80q-141 0 -234 -86q-56 -51 -88 -124t-32 -152q0 -118 73.5 -188t210.5 -70q142 0 283 63l-41 -235q-129 -46 -272 -46q-79 0 -151.5 17.5t-137 54t-112.5 88.5 t-75.5 127t-27.5 164z" />
<glyph unicode="d" horiz-adv-x="1204" d="M76 412q0 121 45.5 239.5t132.5 208.5q167 170 422 170q112 0 221 -41l102 576h248l-276 -1565h-246l14 78l-4 2q-115 -103 -266 -103q-171 0 -282 121t-111 314zM328 438q0 -117 59.5 -185.5t157.5 -68.5q118 0 229 107l84 481q-93 51 -194 51q-149 0 -244 -116 q-44 -55 -68 -126.5t-24 -142.5z" />
<glyph unicode="e" horiz-adv-x="1132" d="M76 434q0 116 42.5 226.5t119.5 193.5q78 87 181.5 131.5t217.5 44.5q185 0 307.5 -112t122.5 -302q0 -93 -23 -180h-722q6 -123 85.5 -188.5t208.5 -65.5q156 0 328 82l-41 -227q-129 -60 -313 -60q-106 0 -198 28.5t-163 83.5t-112 143.5t-41 201.5zM348 608h490 q0 105 -55 163t-154 58q-101 0 -174 -59t-107 -162z" />
<glyph unicode="f" horiz-adv-x="722" d="M96 797l37 211h150l39 221q36 193 143 276q106 82 276 82q72 0 164 -20l-39 -219q-65 24 -131 24t-108 -33q-47 -40 -64 -145l-33 -186h242l-37 -211h-241l-142 -797h-248l142 797h-150z" />
<glyph unicode="g" horiz-adv-x="1187" d="M20 -387l43 242q75 -50 163.5 -81.5t172.5 -31.5q119 0 200 65.5t106 198.5l14 76l-4 2q-111 -76 -234 -76q-173 0 -288 118t-115 308q0 116 46.5 232.5t129.5 199.5q167 164 422 164q217 0 438 -108l-158 -895q-45 -253 -191 -376.5t-380 -123.5q-197 0 -365 86z M324 457q0 -110 63 -177t166 -67q105 0 201 70l88 499q-83 37 -187 37q-144 0 -237 -104q-45 -53 -69.5 -122t-24.5 -136z" />
<glyph unicode="h" d="M55 0l277 1565h248l-117 -664l4 -2q172 131 334 131q139 0 223 -84.5t84 -226.5q0 -54 -14 -131l-105 -588h-248l101 567q10 54 10 97q0 147 -145 147q-125 0 -283 -127l-121 -684h-248z" />
<glyph unicode="i" horiz-adv-x="536" d="M55 0l178 1008h248l-178 -1008h-248zM264 1343q0 67 47.5 113.5t114.5 46.5q64 0 106.5 -42t42.5 -103q0 -67 -48.5 -113.5t-116.5 -46.5q-62 0 -104 42t-42 103z" />
<glyph unicode="j" horiz-adv-x="542" d="M-260 -451l37 213q81 -22 121 -22q70 0 102 42t47 130l193 1096h247l-198 -1125q-30 -170 -111 -254q-97 -102 -278 -102q-90 0 -160 22zM270 1343q0 67 47.5 113.5t114.5 46.5q64 0 107 -42t43 -103q0 -67 -49 -113.5t-117 -46.5q-62 0 -104 42t-42 103z" />
<glyph unicode="k" horiz-adv-x="1134" d="M55 0l277 1565h248l-170 -965h4l485 408h305l-538 -451l348 -557h-283l-330 535h-4l-94 -535h-248z" />
<glyph unicode="l" horiz-adv-x="536" d="M55 0l277 1565h248l-277 -1565h-248z" />
<glyph unicode="m" horiz-adv-x="1873" d="M55 0l178 1008h246l-20 -111l4 -2q158 135 328 135q95 0 168.5 -47t107.5 -127q190 174 387 174q138 0 222.5 -83.5t84.5 -219.5q0 -64 -14 -135l-105 -592h-247l102 575q12 65 12 97q0 66 -36.5 102.5t-106.5 36.5q-129 0 -276 -131q-3 -38 -13 -88l-104 -592h-248 l102 575q11 59 11 97q0 66 -36.5 102.5t-107.5 36.5q-126 0 -270 -127l-121 -684h-248z" />
<glyph unicode="n" d="M55 0l178 1008h246l-20 -111l4 -2q172 135 338 135q139 0 223 -84.5t84 -226.5q0 -54 -14 -131l-105 -588h-248l101 567q10 54 10 99q0 68 -34.5 106.5t-106.5 38.5q-128 0 -287 -127l-121 -684h-248z" />
<glyph unicode="o" d="M76 444q0 110 41.5 221t126.5 199q160 166 405 166q216 0 355 -127.5t139 -339.5q0 -110 -41.5 -221t-126.5 -199q-160 -166 -406 -166q-216 0 -354.5 127.5t-138.5 339.5zM324 461q0 -117 69.5 -191.5t190.5 -74.5q130 0 213 92q98 107 98 260q0 117 -69.5 191.5 t-190.5 74.5q-130 0 -213 -92q-98 -107 -98 -260z" />
<glyph unicode="p" horiz-adv-x="1200" d="M-25 -451l258 1459h246l-10 -60l4 -2q108 84 246 84q176 0 289.5 -126t113.5 -326q0 -121 -44.5 -237t-125.5 -198q-160 -166 -415 -166q-126 0 -228 41l-4 -2l-82 -467h-248zM344 229q88 -45 199 -45q145 0 237 105q92 112 92 264q0 123 -63 194.5t-168 71.5 q-112 0 -205 -69z" />
<glyph unicode="q" horiz-adv-x="1210" d="M76 414q0 122 48 242.5t136 205.5q174 168 459 168q200 0 416 -86l-246 -1395h-250l88 502l-4 2q-107 -76 -242 -76q-175 0 -290 122t-115 315zM326 440q0 -118 62.5 -187t162.5 -69q109 0 213 80l94 529q-82 30 -170 30q-166 0 -262 -110q-47 -54 -73.5 -126.5 t-26.5 -146.5z" />
<glyph unicode="r" horiz-adv-x="839" d="M55 0l178 1008h246l-26 -154l4 -2q62 74 155 120t183 46q35 0 77 -10l-41 -234q-38 10 -79 10q-94 0 -183.5 -46.5t-156.5 -127.5l-109 -610h-248z" />
<glyph unicode="s" horiz-adv-x="1028" d="M47 88l43 244q84 -67 192.5 -106.5t211.5 -39.5q89 0 127 35q32 28 32 74q0 25 -12 42.5t-39.5 29t-52.5 18.5t-71 17t-75 18q-135 37 -202.5 99.5t-67.5 164.5q0 69 29.5 133t83.5 111q114 102 311 102q94 0 197.5 -24t191.5 -68l-41 -231q-75 50 -176 81t-188 31 q-74 0 -113 -26q-43 -29 -43 -78q0 -25 12.5 -42t39.5 -28t52 -17t70 -15.5t72 -16.5q136 -35 204 -100t68 -172q0 -136 -98 -234q-113 -113 -334 -113q-224 0 -424 111z" />
<glyph unicode="t" horiz-adv-x="806" d="M96 797l37 211h174l49 280l250 15l-51 -295h281l-37 -211h-281l-67 -381q-11 -68 -11 -99q0 -127 131 -127q52 0 123 23l-39 -215q-68 -21 -139 -21q-143 0 -235.5 73.5t-92.5 221.5q0 58 15 138l67 387h-174z" />
<glyph unicode="u" horiz-adv-x="1212" d="M106 285q0 44 15 143l102 580h248l-98 -562q-13 -76 -13 -110q0 -65 36.5 -103t109.5 -38q133 0 280 131l121 682h248l-178 -1008h-246l19 104l-5 2q-165 -129 -323 -129q-138 0 -227 84.5t-89 223.5z" />
<glyph unicode="v" horiz-adv-x="1116" d="M113 1008h256l77 -351q27 -120 72 -340h4q52 94 193 336l207 355h258l-596 -1008h-240z" />
<glyph unicode="w" horiz-adv-x="1662" d="M111 1008h258l59 -385q33 -228 41 -301h4q66 132 156 301l202 385h181l69 -385q29 -167 49 -301h5q74 158 147 301l195 385h254l-525 -1008h-243l-64 350q-31 178 -45 281h-4q-70 -143 -143 -281l-185 -350h-243z" />
<glyph unicode="x" horiz-adv-x="1044" d="M-109 0l488 537l-268 471h264l80 -146q25 -45 94 -176h4q70 84 156 180l127 142h274l-434 -478l299 -530h-270l-109 199q-35 61 -94 178h-4q-58 -72 -154 -178l-178 -199h-275z" />
<glyph unicode="y" horiz-adv-x="1116" d="M-12 -451l374 568l-256 891h263l84 -308q41 -151 82 -317h4q134 221 196 319l193 306h260l-926 -1459h-274z" />
<glyph unicode="z" horiz-adv-x="993" d="M-49 4l637 789l-2 4h-443l37 211h860l2 -4l-636 -789l2 -4h477l-37 -211h-895z" />
<glyph unicode="{" horiz-adv-x="757" d="M55 451l33 184h68q82 0 117 35.5t49 120.5l77 442q54 311 353 311q78 0 153 -20l-31 -178q-54 12 -100 12q-66 0 -101 -34t-52 -124l-70 -395q-21 -118 -59.5 -176.5t-116.5 -81.5v-4q96 -40 96 -154q0 -40 -14 -119l-62 -348q-10 -58 -10 -92q0 -57 33 -80.5t94 -23.5 q28 0 78 4l-33 -183q-37 -6 -90 -6q-147 0 -231 63.5t-84 186.5q0 39 12 109l67 383q9 52 9 73q0 50 -28.5 72.5t-96.5 22.5h-60z" />
<glyph unicode="|" horiz-adv-x="649" d="M57 -451l369 2089h197l-369 -2089h-197z" />
<glyph unicode="}" horiz-adv-x="757" d="M-133 -438l31 178q54 -12 100 -12q66 0 101.5 33.5t52.5 123.5l69 396q21 118 59.5 176.5t116.5 81.5v4q-96 40 -96 153q0 40 14 119l62 348q10 58 10 92q0 105 -127 105q-28 0 -78 -4l33 182q38 6 90 6q147 0 231.5 -63.5t84.5 -186.5q0 -32 -13 -108l-67 -383 q-9 -52 -9 -74q0 -50 28.5 -72t96.5 -22h60l-33 -184h-68q-82 0 -116.5 -35.5t-48.5 -120.5l-78 -442q-54 -312 -353 -312q-74 0 -153 21z" />
<glyph unicode="~" horiz-adv-x="1134" d="M170 596l39 225q88 88 217 88q55 0 105.5 -17t117.5 -52q5 -3 21 -11.5t21.5 -11.5t19.5 -9.5t20.5 -9.5t18.5 -8t20.5 -7t20 -4.5t22 -3.5t21.5 -1q44 0 86 19.5t66 40t61 57.5l4 -2l-39 -225q-89 -89 -217 -89q-55 0 -106 17.5t-118 52.5q-6 3 -23.5 12.5t-23.5 12.5 t-21.5 11t-23 10.5t-21 7.5t-23.5 7t-23 3.5t-25 1.5q-44 0 -86 -19.5t-66 -40t-61 -57.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="606" d="M-14 -475l241 1007h162l-113 -1007h-290zM213 870q0 67 48.5 113.5t115.5 46.5q64 0 105.5 -42t41.5 -103q0 -67 -48 -113.5t-116 -46.5q-62 0 -104.5 42.5t-42.5 102.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1054" d="M115 666q0 122 50.5 246t153.5 212q124 105 297 133l48 263h194l-45 -258q116 -12 221 -54l-41 -235q-143 80 -274 80q-141 0 -234 -86q-56 -51 -88 -124.5t-32 -152.5q0 -118 73.5 -188t210.5 -70q139 0 283 64l-41 -236q-123 -45 -264 -45l-45 -252h-195l49 277 q-144 44 -232.5 152.5t-88.5 273.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1169" d="M57 0l33 190q116 67 174 178.5t58 249.5h-156l35 197h98q-18 106 -18 189q0 238 147.5 369.5t390.5 131.5q186 0 336 -82l-45 -249q-142 94 -303 94q-266 0 -266 -267q0 -54 16 -186h344l-35 -197h-295q-7 -250 -165 -382l2 -5h626l-41 -231h-936z" />
<glyph unicode="&#xa4;" horiz-adv-x="1185" d="M33 338l194 166q-30 74 -30 164q0 153 90 282l-119 148l176 153l119 -147q93 49 203 49q136 0 239 -70l197 168l123 -155l-195 -166q31 -76 31 -164q0 -154 -90 -283l119 -147l-177 -154l-118 148q-93 -49 -203 -49q-139 0 -240 69l-196 -168zM385 682q0 -95 61 -158 t162 -63q111 0 187.5 86t76.5 205q0 95 -61 158t-162 63q-111 0 -187.5 -86t-76.5 -205z" />
<glyph unicode="&#xa5;" horiz-adv-x="1382" d="M174 434l33 180h375l20 115l-43 84h-317l32 180h193l-252 490h289l106 -211q80 -157 154 -311h4q110 136 250 299l190 223h303l-424 -490h197l-33 -180h-319l-70 -82l-20 -117h375l-33 -180h-375l-76 -434h-260l76 434h-375z" />
<glyph unicode="&#xa6;" horiz-adv-x="649" d="M57 -451l142 803h196l-141 -803h-197zM285 836l141 802h197l-142 -802h-196z" />
<glyph unicode="&#xa7;" horiz-adv-x="1161" d="M98 -227l41 229q67 -51 150.5 -82t159.5 -31q87 0 142.5 40t55.5 112q0 22 -5 41t-20.5 37.5t-27 31t-41.5 31.5t-46.5 29t-59.5 34t-64 36q-65 37 -105 64t-82.5 67.5t-62.5 90t-20 109.5q0 114 71 199t185 123v4q-76 83 -76 199q0 157 126.5 259.5t311.5 102.5 q167 0 316 -78l-39 -217q-138 88 -271 88q-82 0 -139 -38t-57 -107q0 -15 3.5 -29t7.5 -25.5t15 -24.5t18.5 -21.5t25.5 -21.5t28 -20t34.5 -21.5t36.5 -21t42.5 -23t44.5 -24.5q69 -38 114 -69t86 -72t60.5 -90.5t19.5 -110.5q0 -117 -69.5 -204.5t-178.5 -120.5v-4 q66 -76 66 -193q0 -165 -129 -266.5t-320 -101.5q-91 0 -184 24t-164 66zM356 637q0 -48 36.5 -89.5t75.5 -65t142 -77.5q29 -16 45 -24q74 12 126.5 55t52.5 111q0 21 -6.5 40t-14 34t-26.5 32.5t-32 28t-43 28.5t-46.5 27t-56 30.5t-58.5 31.5q-83 -12 -139 -52t-56 -110z " />
<glyph unicode="&#xa8;" horiz-adv-x="1046" d="M326 1319q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5zM741 1319q0 58 42.5 99.5t101.5 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1652" d="M104 741q0 159 58.5 300.5t159.5 243t243 161t303 59.5t303 -59.5t243 -161t159.5 -243t58.5 -300.5t-58.5 -300.5t-159.5 -243t-243 -161t-303 -59.5t-303 59.5t-243 161t-159.5 243t-58.5 300.5zM219 741q0 -137 47.5 -258t132 -208.5t206 -138t263.5 -50.5 q189 0 338 86.5t230.5 236t81.5 332.5t-81.5 332.5t-230.5 236.5t-338 87t-338 -87t-230 -236.5t-81 -332.5zM467 750q0 181 124 300t312 119q138 0 238 -59v-166q-111 76 -230 76q-120 0 -197 -76.5t-77 -191.5q0 -116 77 -191.5t201 -75.5q130 0 236 82v-168 q-101 -65 -252 -65q-188 0 -310 118.5t-122 297.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="794" d="M170 1114q0 103 47.5 192t137 144t202.5 55q159 0 309 -104l-100 -565h-174l8 43l-4 2q-67 -60 -162 -60q-114 0 -189 81t-75 212zM352 1128q0 -69 38.5 -110t105.5 -41q71 0 127 47l51 289q-61 37 -129 37q-85 0 -139 -67t-54 -155z" />
<glyph unicode="&#xab;" horiz-adv-x="1138" d="M74 528l399 424h217l-414 -440l250 -457h-194zM516 528l399 424h218l-414 -440l250 -457h-195z" />
<glyph unicode="&#xac;" horiz-adv-x="1200" d="M133 649l33 187h977l-105 -598h-196l71 411h-780z" />
<glyph unicode="&#xad;" horiz-adv-x="688" d="M94 463l37 209h487l-36 -209h-488z" />
<glyph unicode="&#xae;" horiz-adv-x="929" d="M190 1124q0 164 109.5 272.5t275.5 108.5t276 -108.5t110 -272.5t-110 -272.5t-276 -108.5t-275.5 108.5t-109.5 272.5zM260 1124q0 -136 88.5 -225.5t226.5 -89.5q139 0 227.5 89.5t88.5 225.5t-89 226t-227 90t-226.5 -90t-88.5 -226zM430 918v413h154q67 0 112 -35.5 t45 -97.5q0 -43 -23 -74t-61 -45l99 -161h-101l-84 147h-53v-147h-88zM518 1135h57q76 0 76 59t-76 59h-57v-118z" />
<glyph unicode="&#xaf;" horiz-adv-x="1046" d="M358 1235l31 172h588l-31 -172h-588z" />
<glyph unicode="&#xb0;" horiz-adv-x="698" d="M164 1204q0 127 88 214t221 87t221 -87t88 -214t-88 -214t-221 -87t-221 87t-88 214zM281 1204q0 -78 55 -134t137 -56t137.5 56t55.5 134t-55.5 134.5t-137.5 56.5t-137 -56.5t-55 -134.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="1202" d="M74 145l32 187h926l-33 -187h-925zM197 838l32 186h365l61 348h197l-61 -348h364l-33 -186h-364l-62 -351h-196l61 351h-364z" />
<glyph unicode="&#xb2;" horiz-adv-x="743" d="M92 868l348 355q107 110 146 164t39 104q0 98 -133 98q-52 0 -111 -20t-105 -53l33 198q89 53 215 53q128 0 214.5 -66t86.5 -185q0 -87 -47.5 -166t-140.5 -174l-129 -132l2 -4h268l-30 -176h-654z" />
<glyph unicode="&#xb3;" horiz-adv-x="716" d="M143 936l35 194q120 -110 252 -110q69 0 111 33q41 30 41 82q0 47 -42.5 73.5t-113.5 26.5h-88l27 147h94q90 0 133 43q31 31 31 72q0 50 -38.5 75t-99.5 25q-97 0 -194 -51l31 178q80 41 192 41q54 0 106.5 -13t99 -39t75 -72t28.5 -105q0 -85 -51 -143 q-47 -51 -123 -76v-4q133 -56 133 -187q0 -125 -114 -204q-101 -72 -246 -72q-159 0 -279 86z" />
<glyph unicode="&#xb4;" horiz-adv-x="1046" d="M487 1153l224 336h250l-291 -336h-183z" />
<glyph unicode="&#xb5;" horiz-adv-x="1247" d="M174 -451v1459h248v-578q0 -127 45 -178q47 -55 133 -55q71 0 140 36t122 95v680h248v-1008h-246v111l-4 2q-146 -136 -319 -136q-72 0 -131 27l-4 -2l26 -453h-258z" />
<glyph unicode="&#xb6;" horiz-adv-x="1191" d="M197 1065q0 104 56.5 201t154 157t206.5 60h566l-340 -1934h-195l311 1762h-157l-312 -1762h-194l211 1192q-134 7 -220.5 99.5t-86.5 224.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="612" d="M164 610q0 70 50 119t120 49q66 0 109.5 -44t43.5 -107q0 -70 -50 -119t-120 -49q-66 0 -109.5 44t-43.5 107z" />
<glyph unicode="&#xb8;" horiz-adv-x="1046" d="M182 -395l56 108q71 -51 137 -51q35 0 60.5 17t25.5 49q0 23 -16.5 42t-43.5 31t-52 19.5t-52 11.5l123 244l96 -35l-63 -127q145 -50 145 -172q0 -85 -63 -140t-160 -55q-105 0 -193 58z" />
<glyph unicode="&#xb9;" horiz-adv-x="641" d="M250 1470l31 179l409 118l-160 -903h-192l117 666z" />
<glyph unicode="&#xba;" horiz-adv-x="827" d="M180 1124q0 160 105 270.5t268 110.5q142 0 236 -82.5t94 -220.5q0 -160 -105 -270.5t-268 -110.5q-142 0 -236 82.5t-94 220.5zM360 1137q0 -69 41 -113.5t115 -44.5q81 0 133.5 60.5t52.5 150.5q0 69 -40.5 113.5t-114.5 44.5q-81 0 -134 -60.5t-53 -150.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1138" d="M6 55l414 441l-250 456h195l258 -473l-400 -424h-217zM449 55l413 441l-250 456h195l258 -473l-399 -424h-217z" />
<glyph unicode="&#xbc;" horiz-adv-x="1943" d="M227 1208l31 178l410 119l-160 -903h-193l117 666zM250 -27l1229 1510h227l-1229 -1510h-227zM1014 195l590 694h182l-96 -549h139l-27 -150h-139l-33 -190h-192l32 190h-454zM1317 344l2 -4h178l19 106q16 95 36 179l-4 2q-69 -91 -129 -162z" />
<glyph unicode="&#xbd;" horiz-adv-x="1994" d="M227 1208l31 178l410 119l-160 -903h-193l117 666zM250 -27l1229 1510h227l-1229 -1510h-227zM1145 4l348 354q105 108 144.5 163.5t39.5 105.5q0 98 -133 98q-51 0 -109.5 -20.5t-105.5 -53.5l33 199q89 53 215 53q128 0 214.5 -66.5t86.5 -185.5q0 -87 -47.5 -166 t-140.5 -174l-129 -131l2 -4h268l-31 -176h-653z" />
<glyph unicode="&#xbe;" horiz-adv-x="2041" d="M133 674l35 194q120 -110 252 -110q68 0 110 33q41 30 41 81q0 47 -42 74t-113 27h-88l26 147h95q90 0 133 43q30 30 30 72q0 50 -38 75t-99 25q-97 0 -194 -51l30 178q80 41 193 41q54 0 106.5 -13t99 -39t75 -72t28.5 -105q0 -86 -51 -144q-46 -50 -123 -75v-4 q133 -56 133 -187q0 -60 -30 -113.5t-85 -91.5q-99 -71 -245 -71q-159 0 -279 86zM348 -27l1229 1510h227l-1229 -1510h-227zM1112 195l590 694h182l-96 -549h139l-26 -150h-140l-32 -190h-193l33 190h-455zM1415 344l2 -4h178l19 106q14 81 37 179l-4 2q-69 -91 -129 -162z " />
<glyph unicode="&#xbf;" horiz-adv-x="978" d="M-35 -61q0 186 132.5 313.5t344.5 150.5l52 181h225l-62 -363q-89 0 -165 -15t-137.5 -45.5t-96.5 -82.5t-35 -121q0 -106 75 -167.5t189 -61.5q126 0 244 67l-45 -252q-100 -41 -221 -41q-136 0 -249 53t-182 154t-69 230zM500 870q0 67 48.5 113.5t115.5 46.5 q64 0 105.5 -42t41.5 -103q0 -67 -48 -113.5t-116 -46.5q-63 0 -105 42.5t-42 102.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1447" d="M-90 0l856 1483h256l336 -1483h-268l-78 362h-621l-205 -362h-276zM512 575h453l-52 236q-35 161 -69 358h-4q-83 -159 -199 -364zM741 1964h236l96 -336h-170z" />
<glyph unicode="&#xc1;" horiz-adv-x="1447" d="M-90 0l856 1483h256l336 -1483h-268l-78 362h-621l-205 -362h-276zM512 575h453l-52 236q-35 161 -69 358h-4q-83 -159 -199 -364zM772 1628l223 336h250l-291 -336h-182z" />
<glyph unicode="&#xc2;" horiz-adv-x="1447" d="M-90 0l856 1483h256l336 -1483h-268l-78 362h-621l-205 -362h-276zM512 575h453l-52 236q-35 161 -69 358h-4q-83 -159 -199 -364zM573 1628l301 336h220l172 -336h-174l-129 203l-199 -203h-191z" />
<glyph unicode="&#xc3;" horiz-adv-x="1447" d="M-90 0l856 1483h256l336 -1483h-268l-78 362h-621l-205 -362h-276zM512 575h453l-52 236q-35 161 -69 358h-4q-83 -159 -199 -364zM606 1661l35 201q71 75 176 75q60 0 168 -53q34 -17 51.5 -24.5t42 -14t47.5 -6.5q90 0 170 100l4 -2l-34 -200q-72 -76 -176 -76 q-60 0 -168 53q-34 17 -52 24.5t-42.5 14t-47.5 6.5q-90 0 -170 -100z" />
<glyph unicode="&#xc4;" horiz-adv-x="1447" d="M-90 0l856 1483h256l336 -1483h-268l-78 362h-621l-205 -362h-276zM512 575h453l-52 236q-35 161 -69 358h-4q-83 -159 -199 -364zM610 1794q0 58 42.5 99.5t101.5 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5q-55 0 -91 36.5t-36 90.5zM1026 1794 q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1447" d="M-90 0l856 1483h256l336 -1483h-268l-78 362h-621l-205 -362h-276zM512 575h453l-52 236q-35 161 -69 358h-4q-83 -159 -199 -364zM709 1817q0 105 76.5 184.5t187.5 79.5q98 0 166.5 -61.5t68.5 -161.5q0 -105 -76.5 -185t-187.5 -80q-98 0 -166.5 62t-68.5 162z M838 1823q0 -46 28.5 -79.5t81.5 -33.5q54 0 92.5 40.5t38.5 100.5q0 46 -28.5 79.5t-81.5 33.5q-54 0 -92.5 -40.5t-38.5 -100.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1847" d="M-68 0l986 1483h999l-41 -232h-576l-65 -368h489l-38 -221h-490l-76 -431h596l-41 -231h-856l64 362h-434l-240 -362h-277zM588 575h334l53 306q52 286 72 387l-5 2q-90 -144 -221 -342z" />
<glyph unicode="&#xc7;" horiz-adv-x="1363" d="M117 631q0 172 67.5 341.5t194.5 295.5q237 237 602 237q223 0 422 -94l-47 -262q-185 119 -393 119q-120 0 -224 -42t-180 -118q-89 -89 -135.5 -210.5t-46.5 -242.5q0 -206 131 -323t356 -117q204 0 379 90l-47 -268q-155 -60 -354 -60q-32 0 -62 3l-32 -66 q145 -50 145 -172q0 -85 -63 -140t-160 -55q-105 0 -193 58l55 108q71 -51 138 -51q35 0 60.5 17t25.5 49q0 23 -16.5 42t-43.5 31t-52 19.5t-52 11.5l80 160q-254 42 -404.5 209t-150.5 430z" />
<glyph unicode="&#xc8;" horiz-adv-x="1126" d="M59 0l263 1483h874l-41 -232h-614l-66 -368h529l-39 -221h-529l-76 -431h635l-41 -231h-895zM608 1964h236l96 -336h-170z" />
<glyph unicode="&#xc9;" horiz-adv-x="1126" d="M59 0l263 1483h874l-41 -232h-614l-66 -368h529l-39 -221h-529l-76 -431h635l-41 -231h-895zM639 1628l223 336h250l-291 -336h-182z" />
<glyph unicode="&#xca;" horiz-adv-x="1126" d="M59 0l263 1483h874l-41 -232h-614l-66 -368h529l-39 -221h-529l-76 -431h635l-41 -231h-895zM440 1628l301 336h220l172 -336h-175l-129 203l-198 -203h-191z" />
<glyph unicode="&#xcb;" horiz-adv-x="1126" d="M59 0l263 1483h874l-41 -232h-614l-66 -368h529l-39 -221h-529l-76 -431h635l-41 -231h-895zM477 1794q0 58 42.5 99.5t101.5 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5q-55 0 -91 36.5t-36 90.5zM893 1794q0 58 42 99.5t101 41.5q55 0 91 -36.5 t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="557" d="M59 0l263 1483h260l-263 -1483h-260zM295 1964h235l97 -336h-170z" />
<glyph unicode="&#xcd;" horiz-adv-x="557" d="M59 0l263 1483h260l-263 -1483h-260zM326 1628l223 336h250l-291 -336h-182z" />
<glyph unicode="&#xce;" horiz-adv-x="557" d="M59 0l263 1483h260l-263 -1483h-260zM127 1628l301 336h219l172 -336h-174l-129 203l-199 -203h-190z" />
<glyph unicode="&#xcf;" horiz-adv-x="557" d="M59 0l263 1483h260l-263 -1483h-260zM164 1794q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5zM580 1794q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1515" d="M37 659l35 193h155l111 631h369q350 0 553 -166q108 -88 166.5 -215t58.5 -283q0 -163 -64.5 -320t-191.5 -274q-246 -225 -651 -225h-502l117 659h-156zM377 231h221q309 0 471 172q75 78 115.5 185t40.5 215q0 98 -36 184.5t-108 147.5q-139 116 -389 116h-135 l-70 -399h304l-35 -193h-303z" />
<glyph unicode="&#xd1;" horiz-adv-x="1583" d="M59 0l263 1483h241l402 -727q70 -128 198 -369l4 2q41 252 72 432l117 662h252l-262 -1483h-242l-402 727q-70 128 -198 369l-4 -2q-41 -252 -72 -432l-117 -662h-252zM674 1661l35 201q71 75 176 75q60 0 168 -53q34 -17 51.5 -24.5t42 -14t47.5 -6.5q90 0 170 100l4 -2 l-35 -200q-72 -76 -176 -76q-60 0 -168 53q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-90 0 -170 -100z" />
<glyph unicode="&#xd2;" horiz-adv-x="1642" d="M121 664q0 154 52 303t153 268q108 128 258.5 199t328.5 71q144 0 270.5 -48t220.5 -135t148 -217.5t54 -285.5q0 -154 -52 -303t-153 -268q-109 -128 -260 -199.5t-328 -71.5q-144 0 -270.5 48.5t-220 135.5t-147.5 217.5t-54 285.5zM381 684q0 -132 52.5 -237.5 t154 -168.5t235.5 -63q232 0 379 180q67 81 105.5 187t38.5 217q0 131 -53 237t-154.5 169t-235.5 63q-231 0 -379 -181q-67 -81 -105 -186.5t-38 -216.5zM838 1964h235l96 -336h-170z" />
<glyph unicode="&#xd3;" horiz-adv-x="1642" d="M121 664q0 154 52 303t153 268q108 128 258.5 199t328.5 71q144 0 270.5 -48t220.5 -135t148 -217.5t54 -285.5q0 -154 -52 -303t-153 -268q-109 -128 -260 -199.5t-328 -71.5q-144 0 -270.5 48.5t-220 135.5t-147.5 217.5t-54 285.5zM381 684q0 -132 52.5 -237.5 t154 -168.5t235.5 -63q232 0 379 180q67 81 105.5 187t38.5 217q0 131 -53 237t-154.5 169t-235.5 63q-231 0 -379 -181q-67 -81 -105 -186.5t-38 -216.5zM868 1628l224 336h249l-290 -336h-183z" />
<glyph unicode="&#xd4;" horiz-adv-x="1642" d="M121 664q0 154 52 303t153 268q108 128 258.5 199t328.5 71q144 0 270.5 -48t220.5 -135t148 -217.5t54 -285.5q0 -154 -52 -303t-153 -268q-109 -128 -260 -199.5t-328 -71.5q-144 0 -270.5 48.5t-220 135.5t-147.5 217.5t-54 285.5zM381 684q0 -132 52.5 -237.5 t154 -168.5t235.5 -63q232 0 379 180q67 81 105.5 187t38.5 217q0 131 -53 237t-154.5 169t-235.5 63q-231 0 -379 -181q-67 -81 -105 -186.5t-38 -216.5zM670 1628l301 336h219l172 -336h-174l-129 203l-199 -203h-190z" />
<glyph unicode="&#xd5;" horiz-adv-x="1642" d="M121 664q0 154 52 303t153 268q108 128 258.5 199t328.5 71q144 0 270.5 -48t220.5 -135t148 -217.5t54 -285.5q0 -154 -52 -303t-153 -268q-109 -128 -260 -199.5t-328 -71.5q-144 0 -270.5 48.5t-220 135.5t-147.5 217.5t-54 285.5zM381 684q0 -132 52.5 -237.5 t154 -168.5t235.5 -63q232 0 379 180q67 81 105.5 187t38.5 217q0 131 -53 237t-154.5 169t-235.5 63q-231 0 -379 -181q-67 -81 -105 -186.5t-38 -216.5zM702 1661l35 201q71 75 176 75q60 0 168 -53q34 -17 52 -24.5t42.5 -14t47.5 -6.5q90 0 170 100l4 -2l-35 -200 q-72 -76 -176 -76q-60 0 -168 53q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-90 0 -170 -100z" />
<glyph unicode="&#xd6;" horiz-adv-x="1642" d="M121 664q0 154 52 303t153 268q108 128 258.5 199t328.5 71q144 0 270.5 -48t220.5 -135t148 -217.5t54 -285.5q0 -154 -52 -303t-153 -268q-109 -128 -260 -199.5t-328 -71.5q-144 0 -270.5 48.5t-220 135.5t-147.5 217.5t-54 285.5zM381 684q0 -132 52.5 -237.5 t154 -168.5t235.5 -63q232 0 379 180q67 81 105.5 187t38.5 217q0 131 -53 237t-154.5 169t-235.5 63q-231 0 -379 -181q-67 -81 -105 -186.5t-38 -216.5zM707 1794q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5z M1122 1794q0 58 42.5 99.5t101.5 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1202" d="M156 438l354 305l-246 304l156 135l246 -305l352 305l112 -138l-354 -305l246 -303l-156 -135l-245 305l-353 -305z" />
<glyph unicode="&#xd8;" horiz-adv-x="1642" d="M59 78l183 176q-121 174 -121 410q0 154 52 303t153 268q108 128 258.5 199t328.5 71q268 0 459 -155l178 172l117 -117l-182 -176q121 -174 121 -410q0 -154 -52 -303t-153 -268q-109 -128 -260 -199.5t-328 -71.5q-267 0 -459 156l-178 -172zM381 684q0 -138 55 -242 l752 727q-120 99 -285 99q-231 0 -379 -181q-67 -81 -105 -186.5t-38 -216.5zM539 313q119 -98 284 -98q232 0 379 180q67 81 105.5 187t38.5 217q0 135 -56 241z" />
<glyph unicode="&#xd9;" horiz-adv-x="1492" d="M145 473q0 84 25 240l135 770h260l-135 -770q-20 -117 -20 -197q0 -301 307 -301q200 0 303 131q80 100 123 344l139 793h252l-141 -805q-32 -181 -76 -293t-111 -192q-88 -103 -217.5 -159.5t-292.5 -56.5q-254 0 -402.5 132.5t-148.5 363.5zM768 1964h236l96 -336h-170 z" />
<glyph unicode="&#xda;" horiz-adv-x="1492" d="M145 473q0 84 25 240l135 770h260l-135 -770q-20 -117 -20 -197q0 -301 307 -301q200 0 303 131q80 100 123 344l139 793h252l-141 -805q-32 -181 -76 -293t-111 -192q-88 -103 -217.5 -159.5t-292.5 -56.5q-254 0 -402.5 132.5t-148.5 363.5zM799 1628l223 336h250 l-291 -336h-182z" />
<glyph unicode="&#xdb;" horiz-adv-x="1492" d="M145 473q0 84 25 240l135 770h260l-135 -770q-20 -117 -20 -197q0 -301 307 -301q200 0 303 131q80 100 123 344l139 793h252l-141 -805q-32 -181 -76 -293t-111 -192q-88 -103 -217.5 -159.5t-292.5 -56.5q-254 0 -402.5 132.5t-148.5 363.5zM600 1628l301 336h219 l172 -336h-174l-129 203l-198 -203h-191z" />
<glyph unicode="&#xdc;" horiz-adv-x="1492" d="M145 473q0 84 25 240l135 770h260l-135 -770q-20 -117 -20 -197q0 -301 307 -301q200 0 303 131q80 100 123 344l139 793h252l-141 -805q-32 -181 -76 -293t-111 -192q-88 -103 -217.5 -159.5t-292.5 -56.5q-254 0 -402.5 132.5t-148.5 363.5zM637 1794q0 58 42 99.5 t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5zM1053 1794q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1363" d="M166 1483h291l135 -271q63 -123 153 -311h5q127 157 249 299l242 283h303l-700 -811l-119 -672h-260l119 670zM735 1628l223 336h250l-290 -336h-183z" />
<glyph unicode="&#xde;" horiz-adv-x="1280" d="M59 0l263 1483h260l-50 -275h148q294 0 434 -118q141 -115 141 -302q0 -98 -43 -194t-127 -168q-166 -143 -469 -143h-247l-50 -283h-260zM408 500h241q181 0 266 78q80 73 80 186q0 97 -69 151q-80 62 -244 62h-190z" />
<glyph unicode="&#xdf;" horiz-adv-x="1329" d="M55 0l207 1169q41 226 182 326q131 92 351 92q225 0 340 -96q98 -85 98 -238q0 -175 -117 -352q-50 4 -88 4q-35 0 -68 -5t-68.5 -17t-57 -38t-21.5 -63q0 -16 3.5 -29t13.5 -25.5t18 -20.5t28.5 -20t32 -18.5t42 -22t46.5 -23.5q118 -63 179 -130.5t61 -168.5 q0 -65 -25.5 -127t-72.5 -107q-116 -113 -328 -113q-178 0 -358 89l41 231q165 -111 333 -111q87 0 125 35q33 29 33 78q0 21 -7.5 38t-15.5 28.5t-31 27t-36.5 23t-51.5 26.5t-57 29q-119 63 -176 128t-57 165q0 76 32.5 137.5t88 102t126 64.5t150.5 30q31 64 31 129 q0 68 -43 106q-50 41 -158 41q-117 0 -182 -51q-71 -56 -92 -174l-203 -1149h-248z" />
<glyph unicode="&#xe0;" horiz-adv-x="1206" d="M78 412q0 121 46.5 240t135.5 208q170 170 428 170q201 0 445 -119l-160 -911h-246l14 80l-4 2q-121 -105 -270 -105q-167 0 -278 120.5t-111 314.5zM328 434q0 -117 60 -183.5t155 -66.5q121 0 233 109l86 483q-88 45 -194 45q-151 0 -244 -112q-48 -56 -72 -129 t-24 -146zM555 1489h236l96 -336h-170z" />
<glyph unicode="&#xe1;" horiz-adv-x="1206" d="M78 412q0 121 46.5 240t135.5 208q170 170 428 170q201 0 445 -119l-160 -911h-246l14 80l-4 2q-121 -105 -270 -105q-167 0 -278 120.5t-111 314.5zM328 434q0 -117 60 -183.5t155 -66.5q121 0 233 109l86 483q-88 45 -194 45q-151 0 -244 -112q-48 -56 -72 -129 t-24 -146zM586 1153l223 336h250l-291 -336h-182z" />
<glyph unicode="&#xe2;" horiz-adv-x="1206" d="M78 412q0 121 46.5 240t135.5 208q170 170 428 170q201 0 445 -119l-160 -911h-246l14 80l-4 2q-121 -105 -270 -105q-167 0 -278 120.5t-111 314.5zM328 434q0 -117 60 -183.5t155 -66.5q121 0 233 109l86 483q-88 45 -194 45q-151 0 -244 -112q-48 -56 -72 -129 t-24 -146zM387 1153l301 336h219l172 -336h-174l-129 203l-198 -203h-191z" />
<glyph unicode="&#xe3;" horiz-adv-x="1206" d="M78 412q0 121 46.5 240t135.5 208q170 170 428 170q201 0 445 -119l-160 -911h-246l14 80l-4 2q-121 -105 -270 -105q-167 0 -278 120.5t-111 314.5zM328 434q0 -117 60 -183.5t155 -66.5q121 0 233 109l86 483q-88 45 -194 45q-151 0 -244 -112q-48 -56 -72 -129 t-24 -146zM420 1186l35 200q72 76 176 76q60 0 168 -53q34 -17 51.5 -24.5t42 -14t47.5 -6.5q90 0 170 100l4 -2l-35 -200q-72 -76 -176 -76q-60 0 -168 53q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-90 0 -170 -100z" />
<glyph unicode="&#xe4;" horiz-adv-x="1206" d="M78 412q0 121 46.5 240t135.5 208q170 170 428 170q201 0 445 -119l-160 -911h-246l14 80l-4 2q-121 -105 -270 -105q-167 0 -278 120.5t-111 314.5zM328 434q0 -117 60 -183.5t155 -66.5q121 0 233 109l86 483q-88 45 -194 45q-151 0 -244 -112q-48 -56 -72 -129 t-24 -146zM424 1319q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5zM840 1319q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1206" d="M78 412q0 121 46.5 240t135.5 208q170 170 428 170q201 0 445 -119l-160 -911h-246l14 80l-4 2q-121 -105 -270 -105q-167 0 -278 120.5t-111 314.5zM328 434q0 -117 60 -183.5t155 -66.5q121 0 233 109l86 483q-88 45 -194 45q-151 0 -244 -112q-48 -56 -72 -129 t-24 -146zM522 1341q0 105 76.5 185t187.5 80q98 0 167 -62t69 -162q0 -105 -76.5 -184.5t-187.5 -79.5q-98 0 -167 61.5t-69 161.5zM651 1348q0 -46 29 -79.5t82 -33.5q54 0 92.5 40.5t38.5 100.5q0 46 -29 79.5t-82 33.5q-54 0 -92.5 -40.5t-38.5 -100.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1804" d="M78 414q0 119 46 237.5t136 206.5q172 172 465 172q157 0 334 -47l-8 -47l4 -2q131 96 274 96q112 0 204 -48.5t149 -144.5t57 -223q0 -88 -23 -178h-710q7 -124 85 -189t203 -65q156 0 320 82l-39 -229q-60 -27 -142.5 -42.5t-158.5 -15.5q-126 0 -236 44t-180 129 q-156 -173 -364 -173q-182 0 -299 120.5t-117 316.5zM328 440q0 -116 62.5 -186t166.5 -70q132 0 219 121q-14 65 -14 123q0 55 10 113l47 268q-65 14 -121 14q-176 0 -274 -112q-44 -53 -70 -125.5t-26 -145.5zM1032 608h479q0 100 -53.5 160.5t-154.5 60.5q-99 0 -168 -60 t-103 -161z" />
<glyph unicode="&#xe7;" horiz-adv-x="1032" d="M76 428q0 122 51 246.5t154 212.5q80 68 186.5 105.5t222.5 37.5q161 0 305 -59l-41 -236q-143 80 -274 80q-141 0 -234 -86q-56 -51 -88 -124t-32 -152q0 -118 73.5 -188t210.5 -70q142 0 283 63l-41 -235q-129 -46 -274 -46l-33 -65q145 -50 145 -172q0 -85 -63 -140 t-160 -55q-105 0 -193 58l56 108q71 -51 137 -51q35 0 60.5 17t25.5 49q0 23 -16.5 42t-43.5 31t-52 19.5t-52 11.5l78 158q-172 30 -281.5 142t-109.5 298z" />
<glyph unicode="&#xe8;" horiz-adv-x="1132" d="M76 434q0 116 42.5 226.5t119.5 193.5q78 87 181.5 131.5t217.5 44.5q185 0 307.5 -112t122.5 -302q0 -93 -23 -180h-722q6 -123 85.5 -188.5t208.5 -65.5q156 0 328 82l-41 -227q-129 -60 -313 -60q-106 0 -198 28.5t-163 83.5t-112 143.5t-41 201.5zM348 608h490 q0 105 -55 163t-154 58q-101 0 -174 -59t-107 -162zM504 1489h235l97 -336h-170z" />
<glyph unicode="&#xe9;" horiz-adv-x="1132" d="M76 434q0 116 42.5 226.5t119.5 193.5q78 87 181.5 131.5t217.5 44.5q185 0 307.5 -112t122.5 -302q0 -93 -23 -180h-722q6 -123 85.5 -188.5t208.5 -65.5q156 0 328 82l-41 -227q-129 -60 -313 -60q-106 0 -198 28.5t-163 83.5t-112 143.5t-41 201.5zM348 608h490 q0 105 -55 163t-154 58q-101 0 -174 -59t-107 -162zM535 1153l223 336h250l-291 -336h-182z" />
<glyph unicode="&#xea;" horiz-adv-x="1132" d="M76 434q0 116 42.5 226.5t119.5 193.5q78 87 181.5 131.5t217.5 44.5q185 0 307.5 -112t122.5 -302q0 -93 -23 -180h-722q6 -123 85.5 -188.5t208.5 -65.5q156 0 328 82l-41 -227q-129 -60 -313 -60q-106 0 -198 28.5t-163 83.5t-112 143.5t-41 201.5zM336 1153l301 336 h219l172 -336h-174l-129 203l-199 -203h-190zM348 608h490q0 105 -55 163t-154 58q-101 0 -174 -59t-107 -162z" />
<glyph unicode="&#xeb;" horiz-adv-x="1132" d="M76 434q0 116 42.5 226.5t119.5 193.5q78 87 181.5 131.5t217.5 44.5q185 0 307.5 -112t122.5 -302q0 -93 -23 -180h-722q6 -123 85.5 -188.5t208.5 -65.5q156 0 328 82l-41 -227q-129 -60 -313 -60q-106 0 -198 28.5t-163 83.5t-112 143.5t-41 201.5zM348 608h490 q0 105 -55 163t-154 58q-101 0 -174 -59t-107 -162zM373 1319q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5zM788 1319q0 58 42.5 99.5t101.5 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5 q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#xec;" horiz-adv-x="536" d="M55 0l178 1008h248l-178 -1008h-248zM201 1489h235l96 -336h-170z" />
<glyph unicode="&#xed;" horiz-adv-x="536" d="M55 0l178 1008h248l-178 -1008h-248zM231 1153l224 336h250l-291 -336h-183z" />
<glyph unicode="&#xee;" horiz-adv-x="536" d="M33 1153l301 336h219l172 -336h-174l-129 203l-199 -203h-190zM55 0l178 1008h248l-178 -1008h-248z" />
<glyph unicode="&#xef;" horiz-adv-x="536" d="M55 0l178 1008h248l-178 -1008h-248zM70 1319q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5zM485 1319q0 58 42.5 99.5t101.5 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5q-55 0 -91 36.5 t-36 90.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1236" d="M86 432q0 113 44.5 223.5t131.5 190.5q71 66 163 102.5t189 36.5q71 0 134 -23t98 -67l4 4q-40 130 -135 258l-332 -86l-2 160l215 55q-66 61 -160 125l176 139q132 -92 228 -198l229 59l2 -160l-125 -32q195 -283 195 -605q0 -296 -164 -469q-75 -79 -180.5 -123.5 t-225.5 -44.5q-209 0 -347 125.5t-138 329.5zM334 455q0 -113 66.5 -187.5t185.5 -74.5t202 83q47 47 74 113t27 135q0 116 -68 188t-186 72q-122 0 -201 -82q-100 -103 -100 -247z" />
<glyph unicode="&#xf1;" d="M55 0l178 1008h246l-20 -111l4 -2q172 135 338 135q139 0 223 -84.5t84 -226.5q0 -54 -14 -131l-105 -588h-248l101 567q10 54 10 99q0 68 -34.5 106.5t-106.5 38.5q-128 0 -287 -127l-121 -684h-248zM408 1186l34 200q72 76 176 76q60 0 168 -53q34 -17 52 -24.5 t42.5 -14t47.5 -6.5q90 0 170 100l4 -2l-35 -200q-72 -76 -176 -76q-60 0 -168 53q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-90 0 -170 -100z" />
<glyph unicode="&#xf2;" d="M76 444q0 110 41.5 221t126.5 199q160 166 405 166q216 0 355 -127.5t139 -339.5q0 -110 -41.5 -221t-126.5 -199q-160 -166 -406 -166q-216 0 -354.5 127.5t-138.5 339.5zM324 461q0 -117 69.5 -191.5t190.5 -74.5q130 0 213 92q98 107 98 260q0 117 -69.5 191.5 t-190.5 74.5q-130 0 -213 -92q-98 -107 -98 -260zM543 1489h235l96 -336h-169z" />
<glyph unicode="&#xf3;" d="M76 444q0 110 41.5 221t126.5 199q160 166 405 166q216 0 355 -127.5t139 -339.5q0 -110 -41.5 -221t-126.5 -199q-160 -166 -406 -166q-216 0 -354.5 127.5t-138.5 339.5zM324 461q0 -117 69.5 -191.5t190.5 -74.5q130 0 213 92q98 107 98 260q0 117 -69.5 191.5 t-190.5 74.5q-130 0 -213 -92q-98 -107 -98 -260zM573 1153l224 336h250l-291 -336h-183z" />
<glyph unicode="&#xf4;" d="M76 444q0 110 41.5 221t126.5 199q160 166 405 166q216 0 355 -127.5t139 -339.5q0 -110 -41.5 -221t-126.5 -199q-160 -166 -406 -166q-216 0 -354.5 127.5t-138.5 339.5zM324 461q0 -117 69.5 -191.5t190.5 -74.5q130 0 213 92q98 107 98 260q0 117 -69.5 191.5 t-190.5 74.5q-130 0 -213 -92q-98 -107 -98 -260zM375 1153l301 336h219l172 -336h-174l-129 203l-199 -203h-190z" />
<glyph unicode="&#xf5;" d="M76 444q0 110 41.5 221t126.5 199q160 166 405 166q216 0 355 -127.5t139 -339.5q0 -110 -41.5 -221t-126.5 -199q-160 -166 -406 -166q-216 0 -354.5 127.5t-138.5 339.5zM324 461q0 -117 69.5 -191.5t190.5 -74.5q130 0 213 92q98 107 98 260q0 117 -69.5 191.5 t-190.5 74.5q-130 0 -213 -92q-98 -107 -98 -260zM408 1186l34 200q72 76 176 76q60 0 168 -53q34 -17 52 -24.5t42.5 -14t47.5 -6.5q90 0 170 100l4 -2l-35 -200q-72 -76 -176 -76q-60 0 -168 53q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-90 0 -170 -100z" />
<glyph unicode="&#xf6;" d="M76 444q0 110 41.5 221t126.5 199q160 166 405 166q216 0 355 -127.5t139 -339.5q0 -110 -41.5 -221t-126.5 -199q-160 -166 -406 -166q-216 0 -354.5 127.5t-138.5 339.5zM324 461q0 -117 69.5 -191.5t190.5 -74.5q130 0 213 92q98 107 98 260q0 117 -69.5 191.5 t-190.5 74.5q-130 0 -213 -92q-98 -107 -98 -260zM412 1319q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5zM827 1319q0 58 42.5 99.5t101.5 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5 q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1202" d="M133 649l33 187h987l-33 -187h-987zM430 309q0 60 42.5 102t102.5 42q56 0 93 -38t37 -93q0 -60 -43 -102t-103 -42q-56 0 -92.5 38t-36.5 93zM580 1163q0 60 42.5 102t102.5 42q56 0 92.5 -38t36.5 -93q0 -60 -42.5 -102t-102.5 -42q-56 0 -92.5 38t-36.5 93z" />
<glyph unicode="&#xf8;" d="M29 41l131 125q-84 120 -84 278q0 110 41.5 221t126.5 199q160 166 405 166q185 0 316 -94l133 127l92 -96l-131 -125q84 -120 84 -279q0 -110 -41.5 -221t-126.5 -199q-160 -166 -406 -166q-183 0 -315 95l-133 -127zM324 461q0 -68 22 -117l445 424q-64 45 -156 45 q-130 0 -213 -92q-98 -107 -98 -260zM428 240q64 -45 156 -45q130 0 213 92q98 107 98 260q0 66 -23 117z" />
<glyph unicode="&#xf9;" horiz-adv-x="1212" d="M106 285q0 44 15 143l102 580h248l-98 -562q-13 -76 -13 -110q0 -65 36.5 -103t109.5 -38q133 0 280 131l121 682h248l-178 -1008h-246l19 104l-5 2q-165 -129 -323 -129q-138 0 -227 84.5t-89 223.5zM539 1489h235l96 -336h-170z" />
<glyph unicode="&#xfa;" horiz-adv-x="1212" d="M106 285q0 44 15 143l102 580h248l-98 -562q-13 -76 -13 -110q0 -65 36.5 -103t109.5 -38q133 0 280 131l121 682h248l-178 -1008h-246l19 104l-5 2q-165 -129 -323 -129q-138 0 -227 84.5t-89 223.5zM569 1153l224 336h249l-290 -336h-183z" />
<glyph unicode="&#xfb;" horiz-adv-x="1212" d="M106 285q0 44 15 143l102 580h248l-98 -562q-13 -76 -13 -110q0 -65 36.5 -103t109.5 -38q133 0 280 131l121 682h248l-178 -1008h-246l19 104l-5 2q-165 -129 -323 -129q-138 0 -227 84.5t-89 223.5zM371 1153l301 336h219l172 -336h-174l-129 203l-199 -203h-190z" />
<glyph unicode="&#xfc;" horiz-adv-x="1212" d="M106 285q0 44 15 143l102 580h248l-98 -562q-13 -76 -13 -110q0 -65 36.5 -103t109.5 -38q133 0 280 131l121 682h248l-178 -1008h-246l19 104l-5 2q-165 -129 -323 -129q-138 0 -227 84.5t-89 223.5zM408 1319q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5 q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5zM823 1319q0 58 42.5 99.5t101.5 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1116" d="M-12 -451l374 568l-256 891h263l84 -308q41 -151 82 -317h4q134 221 196 319l193 306h260l-926 -1459h-274zM528 1153l224 336h249l-290 -336h-183z" />
<glyph unicode="&#xfe;" horiz-adv-x="1198" d="M-25 -451l355 2016h248l-109 -613l4 -2q108 80 246 80q175 0 288 -123.5t113 -320.5q0 -120 -45 -237.5t-129 -203.5q-168 -168 -440 -168q-92 0 -199 31l-4 -2l-80 -457h-248zM340 219q86 -35 180 -35q161 0 258 109q44 52 69 123t25 143q0 119 -63 189.5t-166 70.5 q-114 0 -209 -69z" />
<glyph unicode="&#xff;" horiz-adv-x="1116" d="M-12 -451l374 568l-256 891h263l84 -308q41 -151 82 -317h4q134 221 196 319l193 306h260l-926 -1459h-274zM367 1319q0 58 42 99.5t101 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42 -99.5t-101 -41.5q-55 0 -91 36.5t-36 90.5zM782 1319q0 58 42.5 99.5t101.5 41.5 q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1857" d="M121 662q0 164 65 320t193 275q116 108 275.5 167t349.5 59h923l-41 -232h-575l-66 -368h490l-39 -221h-490l-76 -431h596l-40 -231h-799q-224 0 -396 76t-271 227.5t-99 358.5zM381 678q0 -201 129.5 -322t359.5 -125l181 1020h-70q-286 0 -444 -172 q-75 -82 -115.5 -186.5t-40.5 -214.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1892" d="M76 438q0 104 34 207.5t99 185.5q161 199 418 199q133 0 235.5 -54t147.5 -132h4q154 186 401 186q112 0 204.5 -48.5t150 -144.5t57.5 -223q0 -88 -23 -178h-710q7 -124 85 -189t203 -65q156 0 320 82l-39 -229q-60 -27 -143.5 -42.5t-159.5 -15.5q-125 0 -234.5 46 t-179.5 137h-4q-148 -183 -399 -183q-203 0 -335 125t-132 336zM324 459q0 -121 66.5 -199t184.5 -78q162 0 244 129q64 103 64 238q0 121 -67 198.5t-185 77.5q-162 0 -244 -129q-63 -101 -63 -237zM1120 608h479q0 100 -53.5 160.5t-154.5 60.5q-99 0 -168 -60t-103 -161z " />
<glyph unicode="&#x178;" horiz-adv-x="1363" d="M166 1483h291l135 -271q63 -123 153 -311h5q127 157 249 299l242 283h303l-700 -811l-119 -672h-260l119 670zM573 1794q0 58 42.5 99.5t101.5 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5q-55 0 -91 36.5t-36 90.5zM989 1794q0 58 42.5 99.5 t101.5 41.5q55 0 91 -36.5t36 -90.5q0 -58 -42.5 -99.5t-101.5 -41.5q-55 0 -91 36.5t-36 90.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1046" d="M289 1153l301 336h219l172 -336h-174l-129 203l-199 -203h-190z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1046" d="M322 1186l34 200q72 76 176 76q60 0 168 -53q34 -17 52 -24.5t42.5 -14t47.5 -6.5q90 0 170 100l4 -2l-35 -200q-72 -76 -176 -76q-60 0 -168 53q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-90 0 -170 -100z" />
<glyph unicode="&#x2000;" horiz-adv-x="1040" />
<glyph unicode="&#x2001;" horiz-adv-x="2081" />
<glyph unicode="&#x2002;" horiz-adv-x="1040" />
<glyph unicode="&#x2003;" horiz-adv-x="2081" />
<glyph unicode="&#x2004;" horiz-adv-x="693" />
<glyph unicode="&#x2005;" horiz-adv-x="520" />
<glyph unicode="&#x2006;" horiz-adv-x="346" />
<glyph unicode="&#x2007;" horiz-adv-x="346" />
<glyph unicode="&#x2008;" horiz-adv-x="260" />
<glyph unicode="&#x2009;" horiz-adv-x="416" />
<glyph unicode="&#x200a;" horiz-adv-x="115" />
<glyph unicode="&#x2010;" horiz-adv-x="688" d="M94 463l37 209h487l-36 -209h-488z" />
<glyph unicode="&#x2011;" horiz-adv-x="688" d="M94 463l37 209h487l-36 -209h-488z" />
<glyph unicode="&#x2012;" horiz-adv-x="688" d="M94 463l37 209h487l-36 -209h-488z" />
<glyph unicode="&#x2013;" horiz-adv-x="991" d="M96 475l33 187h791l-33 -187h-791z" />
<glyph unicode="&#x2014;" horiz-adv-x="1581" d="M68 489l28 158h1442l-29 -158h-1441z" />
<glyph unicode="&#x2018;" horiz-adv-x="540" d="M162 956l280 564h162l-215 -564h-227z" />
<glyph unicode="&#x2019;" horiz-adv-x="540" d="M195 956l215 564h227l-281 -564h-161z" />
<glyph unicode="&#x201a;" horiz-adv-x="540" d="M-25 -227l215 563h228l-281 -563h-162z" />
<glyph unicode="&#x201c;" horiz-adv-x="901" d="M162 956l280 564h162l-215 -564h-227zM522 956l281 564h162l-215 -564h-228z" />
<glyph unicode="&#x201d;" horiz-adv-x="901" d="M195 956l215 564h227l-281 -564h-161zM555 956l215 564h227l-280 -564h-162z" />
<glyph unicode="&#x201e;" horiz-adv-x="901" d="M-25 -227l215 563h228l-281 -563h-162zM336 -227l215 563h227l-280 -563h-162z" />
<glyph unicode="&#x2022;" horiz-adv-x="765" d="M162 608q0 98 70.5 169t168.5 71t169 -71t71 -169t-71 -168.5t-169 -70.5t-168.5 70.5t-70.5 168.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1705" d="M78 129q0 70 50 119t120 49q66 0 109.5 -44.5t43.5 -107.5q0 -70 -50 -119t-120 -49q-66 0 -109.5 44.5t-43.5 107.5zM625 129q0 70 50 119t120 49q66 0 109.5 -44.5t43.5 -107.5q0 -70 -50 -119t-120 -49q-66 0 -109.5 44.5t-43.5 107.5zM1171 129q0 70 50 119t120 49 q66 0 110 -44.5t44 -107.5q0 -70 -50 -119t-120 -49q-66 0 -110 44.5t-44 107.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="416" />
<glyph unicode="&#x2039;" horiz-adv-x="696" d="M74 528l399 424h217l-414 -440l250 -457h-194z" />
<glyph unicode="&#x203a;" horiz-adv-x="696" d="M6 55l414 441l-250 456h195l258 -473l-400 -424h-217z" />
<glyph unicode="&#x205f;" horiz-adv-x="520" />
<glyph unicode="&#x20ac;" d="M12 487l33 181h158q3 64 20 157h-149l32 181h175q99 229 291 364t440 135q163 0 293 -53l-43 -248q-119 64 -258 64t-253 -68.5t-188 -193.5h363l-33 -181h-406q-21 -79 -24 -157h401l-33 -181h-348q40 -131 147 -201.5t265 -70.5q132 0 229 39l-45 -250 q-103 -27 -227 -27q-255 0 -426 138t-211 372h-203z" />
<glyph unicode="&#x2122;" horiz-adv-x="1314" d="M164 1362v121h481v-121h-174v-473h-133v473h-174zM731 889v594h125l174 -242l174 242h127v-594h-131v248q0 65 4 131l-4 2q-34 -56 -78 -117l-92 -127l-92 127q-44 61 -78 117l-4 -2q4 -66 4 -131v-248h-129z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x2a;" u2="&#x2026;" k="246" />
<hkern u1="&#x2a;" u2="&#x2e;" k="246" />
<hkern u1="&#x2a;" u2="&#x2c;" k="246" />
<hkern u1="&#x2c;" u2="V" k="238" />
<hkern u1="&#x2c;" u2="&#x39;" k="84" />
<hkern u1="&#x2c;" u2="&#x38;" k="70" />
<hkern u1="&#x2c;" u2="&#x37;" k="33" />
<hkern u1="&#x2c;" u2="&#x2a;" k="246" />
<hkern u1="&#x2d;" u2="x" k="117" />
<hkern u1="&#x2d;" u2="X" k="61" />
<hkern u1="&#x2d;" u2="V" k="115" />
<hkern u1="&#x2d;" u2="&#x32;" k="92" />
<hkern u1="&#x2d;" u2="&#x31;" k="102" />
<hkern u1="&#x2e;" u2="V" k="238" />
<hkern u1="&#x2e;" u2="&#x39;" k="84" />
<hkern u1="&#x2e;" u2="&#x38;" k="70" />
<hkern u1="&#x2e;" u2="&#x37;" k="33" />
<hkern u1="&#x2e;" u2="&#x2a;" k="246" />
<hkern u1="&#x2f;" u2="&#x153;" k="117" />
<hkern u1="&#x2f;" u2="&#xf8;" k="117" />
<hkern u1="&#x2f;" u2="&#xf6;" k="117" />
<hkern u1="&#x2f;" u2="&#xf5;" k="117" />
<hkern u1="&#x2f;" u2="&#xf4;" k="117" />
<hkern u1="&#x2f;" u2="&#xf3;" k="117" />
<hkern u1="&#x2f;" u2="&#xf2;" k="117" />
<hkern u1="&#x2f;" u2="&#xf0;" k="117" />
<hkern u1="&#x2f;" u2="&#xeb;" k="117" />
<hkern u1="&#x2f;" u2="&#xea;" k="117" />
<hkern u1="&#x2f;" u2="&#xe9;" k="117" />
<hkern u1="&#x2f;" u2="&#xe8;" k="117" />
<hkern u1="&#x2f;" u2="&#xe7;" k="117" />
<hkern u1="&#x2f;" u2="&#xe6;" k="117" />
<hkern u1="&#x2f;" u2="&#xe5;" k="117" />
<hkern u1="&#x2f;" u2="&#xe4;" k="117" />
<hkern u1="&#x2f;" u2="&#xe3;" k="117" />
<hkern u1="&#x2f;" u2="&#xe2;" k="117" />
<hkern u1="&#x2f;" u2="&#xe1;" k="117" />
<hkern u1="&#x2f;" u2="&#xe0;" k="117" />
<hkern u1="&#x2f;" u2="&#xc5;" k="195" />
<hkern u1="&#x2f;" u2="&#xc4;" k="195" />
<hkern u1="&#x2f;" u2="&#xc3;" k="195" />
<hkern u1="&#x2f;" u2="&#xc2;" k="195" />
<hkern u1="&#x2f;" u2="&#xc1;" k="195" />
<hkern u1="&#x2f;" u2="&#xc0;" k="195" />
<hkern u1="&#x2f;" u2="q" k="117" />
<hkern u1="&#x2f;" u2="o" k="117" />
<hkern u1="&#x2f;" u2="g" k="117" />
<hkern u1="&#x2f;" u2="e" k="117" />
<hkern u1="&#x2f;" u2="d" k="117" />
<hkern u1="&#x2f;" u2="c" k="117" />
<hkern u1="&#x2f;" u2="a" k="117" />
<hkern u1="&#x2f;" u2="A" k="195" />
<hkern u1="&#x2f;" u2="&#x34;" k="168" />
<hkern u1="&#x30;" u2="_" k="90" />
<hkern u1="&#x36;" u2="&#x37;" k="37" />
<hkern u1="&#x36;" u2="&#x31;" k="74" />
<hkern u1="&#x37;" u2="&#x2026;" k="188" />
<hkern u1="&#x37;" u2="&#x34;" k="135" />
<hkern u1="&#x37;" u2="&#x2f;" k="207" />
<hkern u1="&#x37;" u2="&#x2e;" k="188" />
<hkern u1="&#x37;" u2="&#x2c;" k="188" />
<hkern u1="&#x38;" u2="&#x2026;" k="16" />
<hkern u1="&#x38;" u2="&#x2e;" k="16" />
<hkern u1="&#x38;" u2="&#x2c;" k="16" />
<hkern u1="&#x39;" u2="&#x2026;" k="178" />
<hkern u1="&#x39;" u2="&#x2e;" k="178" />
<hkern u1="&#x39;" u2="&#x2c;" k="178" />
<hkern u1="A" u2="x" k="-47" />
<hkern u1="A" u2="\" k="195" />
<hkern u1="A" u2="V" k="174" />
<hkern u1="B" u2="&#x203a;" k="18" />
<hkern u1="B" u2="&#x201c;" k="39" />
<hkern u1="B" u2="&#x2018;" k="39" />
<hkern u1="B" u2="&#x178;" k="117" />
<hkern u1="B" u2="&#xff;" k="35" />
<hkern u1="B" u2="&#xfd;" k="35" />
<hkern u1="B" u2="&#xdd;" k="117" />
<hkern u1="B" u2="&#xc6;" k="20" />
<hkern u1="B" u2="&#xc5;" k="6" />
<hkern u1="B" u2="&#xc4;" k="6" />
<hkern u1="B" u2="&#xc3;" k="6" />
<hkern u1="B" u2="&#xc2;" k="6" />
<hkern u1="B" u2="&#xc1;" k="6" />
<hkern u1="B" u2="&#xc0;" k="6" />
<hkern u1="B" u2="&#xbb;" k="18" />
<hkern u1="B" u2="z" k="27" />
<hkern u1="B" u2="y" k="35" />
<hkern u1="B" u2="w" k="27" />
<hkern u1="B" u2="v" k="35" />
<hkern u1="B" u2="Y" k="117" />
<hkern u1="B" u2="W" k="49" />
<hkern u1="B" u2="V" k="59" />
<hkern u1="B" u2="A" k="6" />
<hkern u1="D" u2="x" k="18" />
<hkern u1="D" u2="_" k="135" />
<hkern u1="D" u2="X" k="49" />
<hkern u1="D" u2="V" k="92" />
<hkern u1="E" u2="x" k="-6" />
<hkern u1="F" u2="&#x203a;" k="47" />
<hkern u1="F" u2="&#x2039;" k="47" />
<hkern u1="F" u2="&#x2026;" k="217" />
<hkern u1="F" u2="&#x201e;" k="150" />
<hkern u1="F" u2="&#x201a;" k="150" />
<hkern u1="F" u2="&#x153;" k="41" />
<hkern u1="F" u2="&#xff;" k="49" />
<hkern u1="F" u2="&#xfd;" k="49" />
<hkern u1="F" u2="&#xfc;" k="39" />
<hkern u1="F" u2="&#xfb;" k="39" />
<hkern u1="F" u2="&#xfa;" k="39" />
<hkern u1="F" u2="&#xf9;" k="39" />
<hkern u1="F" u2="&#xf8;" k="41" />
<hkern u1="F" u2="&#xf6;" k="41" />
<hkern u1="F" u2="&#xf5;" k="41" />
<hkern u1="F" u2="&#xf4;" k="41" />
<hkern u1="F" u2="&#xf3;" k="41" />
<hkern u1="F" u2="&#xf2;" k="41" />
<hkern u1="F" u2="&#xf1;" k="53" />
<hkern u1="F" u2="&#xf0;" k="41" />
<hkern u1="F" u2="&#xef;" k="-53" />
<hkern u1="F" u2="&#xee;" k="-29" />
<hkern u1="F" u2="&#xeb;" k="41" />
<hkern u1="F" u2="&#xea;" k="41" />
<hkern u1="F" u2="&#xe9;" k="41" />
<hkern u1="F" u2="&#xe8;" k="41" />
<hkern u1="F" u2="&#xe7;" k="41" />
<hkern u1="F" u2="&#xe6;" k="41" />
<hkern u1="F" u2="&#xe5;" k="41" />
<hkern u1="F" u2="&#xe4;" k="41" />
<hkern u1="F" u2="&#xe3;" k="41" />
<hkern u1="F" u2="&#xe2;" k="41" />
<hkern u1="F" u2="&#xe1;" k="41" />
<hkern u1="F" u2="&#xe0;" k="41" />
<hkern u1="F" u2="&#xc5;" k="80" />
<hkern u1="F" u2="&#xc4;" k="80" />
<hkern u1="F" u2="&#xc3;" k="80" />
<hkern u1="F" u2="&#xc2;" k="80" />
<hkern u1="F" u2="&#xc1;" k="80" />
<hkern u1="F" u2="&#xc0;" k="80" />
<hkern u1="F" u2="&#xbb;" k="47" />
<hkern u1="F" u2="&#xab;" k="47" />
<hkern u1="F" u2="z" k="25" />
<hkern u1="F" u2="y" k="49" />
<hkern u1="F" u2="x" k="61" />
<hkern u1="F" u2="w" k="33" />
<hkern u1="F" u2="v" k="49" />
<hkern u1="F" u2="u" k="39" />
<hkern u1="F" u2="t" k="35" />
<hkern u1="F" u2="s" k="43" />
<hkern u1="F" u2="r" k="53" />
<hkern u1="F" u2="q" k="41" />
<hkern u1="F" u2="p" k="53" />
<hkern u1="F" u2="o" k="41" />
<hkern u1="F" u2="n" k="53" />
<hkern u1="F" u2="m" k="53" />
<hkern u1="F" u2="g" k="41" />
<hkern u1="F" u2="e" k="41" />
<hkern u1="F" u2="d" k="41" />
<hkern u1="F" u2="c" k="41" />
<hkern u1="F" u2="a" k="41" />
<hkern u1="F" u2="A" k="80" />
<hkern u1="F" u2="&#x2e;" k="217" />
<hkern u1="F" u2="&#x2c;" k="217" />
<hkern u1="K" u2="&#xf8;" k="72" />
<hkern u1="K" u2="x" k="-53" />
<hkern u1="L" u2="&#x2019;" k="233" />
<hkern u1="L" u2="x" k="-23" />
<hkern u1="L" u2="V" k="207" />
<hkern u1="O" u2="x" k="18" />
<hkern u1="O" u2="_" k="135" />
<hkern u1="O" u2="X" k="49" />
<hkern u1="O" u2="V" k="92" />
<hkern u1="P" u2="&#x203a;" k="8" />
<hkern u1="P" u2="&#x2039;" k="43" />
<hkern u1="P" u2="&#x2026;" k="262" />
<hkern u1="P" u2="&#x201e;" k="160" />
<hkern u1="P" u2="&#x201a;" k="160" />
<hkern u1="P" u2="&#x153;" k="29" />
<hkern u1="P" u2="&#xff;" k="-6" />
<hkern u1="P" u2="&#xfd;" k="-6" />
<hkern u1="P" u2="&#xfc;" k="14" />
<hkern u1="P" u2="&#xfb;" k="14" />
<hkern u1="P" u2="&#xfa;" k="14" />
<hkern u1="P" u2="&#xf9;" k="14" />
<hkern u1="P" u2="&#xf8;" k="29" />
<hkern u1="P" u2="&#xf6;" k="29" />
<hkern u1="P" u2="&#xf5;" k="29" />
<hkern u1="P" u2="&#xf4;" k="29" />
<hkern u1="P" u2="&#xf3;" k="29" />
<hkern u1="P" u2="&#xf2;" k="29" />
<hkern u1="P" u2="&#xf1;" k="16" />
<hkern u1="P" u2="&#xf0;" k="29" />
<hkern u1="P" u2="&#xef;" k="-31" />
<hkern u1="P" u2="&#xee;" k="-66" />
<hkern u1="P" u2="&#xeb;" k="29" />
<hkern u1="P" u2="&#xea;" k="29" />
<hkern u1="P" u2="&#xe9;" k="29" />
<hkern u1="P" u2="&#xe8;" k="29" />
<hkern u1="P" u2="&#xe7;" k="29" />
<hkern u1="P" u2="&#xe6;" k="29" />
<hkern u1="P" u2="&#xe5;" k="29" />
<hkern u1="P" u2="&#xe4;" k="29" />
<hkern u1="P" u2="&#xe3;" k="29" />
<hkern u1="P" u2="&#xe2;" k="29" />
<hkern u1="P" u2="&#xe1;" k="29" />
<hkern u1="P" u2="&#xe0;" k="29" />
<hkern u1="P" u2="&#xc5;" k="63" />
<hkern u1="P" u2="&#xc4;" k="63" />
<hkern u1="P" u2="&#xc3;" k="63" />
<hkern u1="P" u2="&#xc2;" k="63" />
<hkern u1="P" u2="&#xc1;" k="63" />
<hkern u1="P" u2="&#xc0;" k="63" />
<hkern u1="P" u2="&#xbb;" k="8" />
<hkern u1="P" u2="&#xab;" k="43" />
<hkern u1="P" u2="z" k="8" />
<hkern u1="P" u2="y" k="-6" />
<hkern u1="P" u2="x" k="-2" />
<hkern u1="P" u2="w" k="-8" />
<hkern u1="P" u2="v" k="-6" />
<hkern u1="P" u2="u" k="14" />
<hkern u1="P" u2="s" k="14" />
<hkern u1="P" u2="r" k="16" />
<hkern u1="P" u2="q" k="29" />
<hkern u1="P" u2="p" k="16" />
<hkern u1="P" u2="o" k="29" />
<hkern u1="P" u2="n" k="16" />
<hkern u1="P" u2="m" k="16" />
<hkern u1="P" u2="g" k="29" />
<hkern u1="P" u2="e" k="29" />
<hkern u1="P" u2="d" k="29" />
<hkern u1="P" u2="c" k="29" />
<hkern u1="P" u2="a" k="18" />
<hkern u1="P" u2="W" k="23" />
<hkern u1="P" u2="V" k="29" />
<hkern u1="P" u2="J" k="139" />
<hkern u1="P" u2="A" k="63" />
<hkern u1="P" u2="&#x2e;" k="262" />
<hkern u1="P" u2="&#x2c;" k="262" />
<hkern u1="Q" u2="x" k="18" />
<hkern u1="Q" u2="_" k="135" />
<hkern u1="Q" u2="X" k="49" />
<hkern u1="Q" u2="V" k="92" />
<hkern u1="R" u2="&#xf8;" k="25" />
<hkern u1="R" u2="V" k="43" />
<hkern u1="S" u2="_" k="27" />
<hkern u1="S" u2="V" k="41" />
<hkern u1="T" u2="&#xff;" k="74" />
<hkern u1="T" u2="&#xfc;" k="135" />
<hkern u1="T" u2="&#xf6;" k="152" />
<hkern u1="T" u2="&#xf1;" k="207" />
<hkern u1="T" u2="&#xee;" k="-37" />
<hkern u1="T" u2="&#xec;" k="-12" />
<hkern u1="T" u2="&#xeb;" k="119" />
<hkern u1="T" u2="&#xea;" k="178" />
<hkern u1="T" u2="&#xe4;" k="180" />
<hkern u1="T" u2="&#xe3;" k="170" />
<hkern u1="T" u2="&#xe2;" k="184" />
<hkern u1="T" u2="x" k="168" />
<hkern u1="T" u2="_" k="139" />
<hkern u1="T" u2="&#x2f;" k="180" />
<hkern u1="U" u2="x" k="20" />
<hkern u1="U" u2="a" k="10" />
<hkern u1="V" u2="&#x203a;" k="55" />
<hkern u1="V" u2="&#x2039;" k="117" />
<hkern u1="V" u2="&#x2026;" k="213" />
<hkern u1="V" u2="&#x2014;" k="100" />
<hkern u1="V" u2="&#x2013;" k="100" />
<hkern u1="V" u2="&#x178;" k="-20" />
<hkern u1="V" u2="&#x153;" k="129" />
<hkern u1="V" u2="&#x152;" k="25" />
<hkern u1="V" u2="&#xff;" k="66" />
<hkern u1="V" u2="&#xfd;" k="66" />
<hkern u1="V" u2="&#xfc;" k="92" />
<hkern u1="V" u2="&#xfb;" k="92" />
<hkern u1="V" u2="&#xfa;" k="92" />
<hkern u1="V" u2="&#xf9;" k="92" />
<hkern u1="V" u2="&#xf8;" k="129" />
<hkern u1="V" u2="&#xf6;" k="129" />
<hkern u1="V" u2="&#xf5;" k="129" />
<hkern u1="V" u2="&#xf4;" k="129" />
<hkern u1="V" u2="&#xf3;" k="129" />
<hkern u1="V" u2="&#xf2;" k="129" />
<hkern u1="V" u2="&#xf1;" k="106" />
<hkern u1="V" u2="&#xf0;" k="129" />
<hkern u1="V" u2="&#xeb;" k="129" />
<hkern u1="V" u2="&#xea;" k="129" />
<hkern u1="V" u2="&#xe9;" k="129" />
<hkern u1="V" u2="&#xe8;" k="129" />
<hkern u1="V" u2="&#xe7;" k="129" />
<hkern u1="V" u2="&#xe6;" k="129" />
<hkern u1="V" u2="&#xe5;" k="129" />
<hkern u1="V" u2="&#xe4;" k="129" />
<hkern u1="V" u2="&#xe3;" k="129" />
<hkern u1="V" u2="&#xe2;" k="129" />
<hkern u1="V" u2="&#xe1;" k="129" />
<hkern u1="V" u2="&#xe0;" k="129" />
<hkern u1="V" u2="&#xdd;" k="-20" />
<hkern u1="V" u2="&#xd8;" k="25" />
<hkern u1="V" u2="&#xd6;" k="25" />
<hkern u1="V" u2="&#xd5;" k="25" />
<hkern u1="V" u2="&#xd4;" k="25" />
<hkern u1="V" u2="&#xd3;" k="25" />
<hkern u1="V" u2="&#xd2;" k="25" />
<hkern u1="V" u2="&#xc7;" k="25" />
<hkern u1="V" u2="&#xc6;" k="193" />
<hkern u1="V" u2="&#xc5;" k="135" />
<hkern u1="V" u2="&#xc4;" k="135" />
<hkern u1="V" u2="&#xc3;" k="135" />
<hkern u1="V" u2="&#xc2;" k="135" />
<hkern u1="V" u2="&#xc1;" k="135" />
<hkern u1="V" u2="&#xc0;" k="135" />
<hkern u1="V" u2="&#xbb;" k="55" />
<hkern u1="V" u2="&#xad;" k="100" />
<hkern u1="V" u2="&#xab;" k="117" />
<hkern u1="V" u2="y" k="66" />
<hkern u1="V" u2="x" k="63" />
<hkern u1="V" u2="w" k="59" />
<hkern u1="V" u2="v" k="66" />
<hkern u1="V" u2="u" k="92" />
<hkern u1="V" u2="t" k="45" />
<hkern u1="V" u2="s" k="123" />
<hkern u1="V" u2="r" k="106" />
<hkern u1="V" u2="q" k="129" />
<hkern u1="V" u2="p" k="106" />
<hkern u1="V" u2="o" k="129" />
<hkern u1="V" u2="n" k="106" />
<hkern u1="V" u2="m" k="106" />
<hkern u1="V" u2="g" k="129" />
<hkern u1="V" u2="e" k="129" />
<hkern u1="V" u2="d" k="129" />
<hkern u1="V" u2="c" k="129" />
<hkern u1="V" u2="a" k="129" />
<hkern u1="V" u2="_" k="152" />
<hkern u1="V" u2="Y" k="-20" />
<hkern u1="V" u2="V" k="-10" />
<hkern u1="V" u2="S" k="27" />
<hkern u1="V" u2="Q" k="25" />
<hkern u1="V" u2="O" k="25" />
<hkern u1="V" u2="J" k="102" />
<hkern u1="V" u2="G" k="25" />
<hkern u1="V" u2="C" k="25" />
<hkern u1="V" u2="A" k="135" />
<hkern u1="V" u2="&#x2f;" k="162" />
<hkern u1="V" u2="&#x2e;" k="213" />
<hkern u1="V" u2="&#x2d;" k="100" />
<hkern u1="V" u2="&#x2c;" k="213" />
<hkern u1="W" u2="&#xe4;" k="86" />
<hkern u1="W" u2="x" k="53" />
<hkern u1="W" u2="_" k="111" />
<hkern u1="W" u2="&#x2f;" k="119" />
<hkern u1="X" u2="&#x203a;" k="-2" />
<hkern u1="X" u2="&#x2039;" k="78" />
<hkern u1="X" u2="&#x201d;" k="12" />
<hkern u1="X" u2="&#x201c;" k="51" />
<hkern u1="X" u2="&#x2019;" k="12" />
<hkern u1="X" u2="&#x2018;" k="51" />
<hkern u1="X" u2="&#x2014;" k="74" />
<hkern u1="X" u2="&#x2013;" k="74" />
<hkern u1="X" u2="&#x153;" k="59" />
<hkern u1="X" u2="&#x152;" k="53" />
<hkern u1="X" u2="&#xff;" k="137" />
<hkern u1="X" u2="&#xfd;" k="137" />
<hkern u1="X" u2="&#xf8;" k="59" />
<hkern u1="X" u2="&#xf6;" k="59" />
<hkern u1="X" u2="&#xf5;" k="59" />
<hkern u1="X" u2="&#xf4;" k="59" />
<hkern u1="X" u2="&#xf3;" k="59" />
<hkern u1="X" u2="&#xf2;" k="59" />
<hkern u1="X" u2="&#xf0;" k="59" />
<hkern u1="X" u2="&#xeb;" k="59" />
<hkern u1="X" u2="&#xea;" k="59" />
<hkern u1="X" u2="&#xe9;" k="59" />
<hkern u1="X" u2="&#xe8;" k="59" />
<hkern u1="X" u2="&#xe7;" k="59" />
<hkern u1="X" u2="&#xe6;" k="59" />
<hkern u1="X" u2="&#xe5;" k="59" />
<hkern u1="X" u2="&#xe4;" k="59" />
<hkern u1="X" u2="&#xe3;" k="59" />
<hkern u1="X" u2="&#xe2;" k="59" />
<hkern u1="X" u2="&#xe1;" k="59" />
<hkern u1="X" u2="&#xe0;" k="59" />
<hkern u1="X" u2="&#xd8;" k="53" />
<hkern u1="X" u2="&#xd6;" k="53" />
<hkern u1="X" u2="&#xd5;" k="53" />
<hkern u1="X" u2="&#xd4;" k="53" />
<hkern u1="X" u2="&#xd3;" k="53" />
<hkern u1="X" u2="&#xd2;" k="53" />
<hkern u1="X" u2="&#xc7;" k="53" />
<hkern u1="X" u2="&#xbb;" k="-2" />
<hkern u1="X" u2="&#xad;" k="74" />
<hkern u1="X" u2="&#xab;" k="78" />
<hkern u1="X" u2="y" k="137" />
<hkern u1="X" u2="w" k="100" />
<hkern u1="X" u2="v" k="137" />
<hkern u1="X" u2="q" k="59" />
<hkern u1="X" u2="o" k="59" />
<hkern u1="X" u2="g" k="59" />
<hkern u1="X" u2="e" k="59" />
<hkern u1="X" u2="d" k="59" />
<hkern u1="X" u2="c" k="59" />
<hkern u1="X" u2="a" k="59" />
<hkern u1="X" u2="Q" k="53" />
<hkern u1="X" u2="O" k="53" />
<hkern u1="X" u2="G" k="53" />
<hkern u1="X" u2="C" k="53" />
<hkern u1="X" u2="&#x2d;" k="74" />
<hkern u1="Y" u2="&#xff;" k="90" />
<hkern u1="Y" u2="&#xf6;" k="156" />
<hkern u1="Y" u2="&#xf5;" k="178" />
<hkern u1="Y" u2="&#xf4;" k="207" />
<hkern u1="Y" u2="&#xee;" k="-35" />
<hkern u1="Y" u2="&#xec;" k="-37" />
<hkern u1="Y" u2="&#xeb;" k="119" />
<hkern u1="Y" u2="&#xea;" k="174" />
<hkern u1="Y" u2="&#xe8;" k="176" />
<hkern u1="Y" u2="&#xe4;" k="156" />
<hkern u1="Y" u2="&#xe3;" k="182" />
<hkern u1="Y" u2="&#xe2;" k="229" />
<hkern u1="Y" u2="&#xe0;" k="203" />
<hkern u1="Y" u2="x" k="109" />
<hkern u1="Y" u2="_" k="180" />
<hkern u1="Y" u2="V" k="-20" />
<hkern u1="Y" u2="&#x34;" k="131" />
<hkern u1="Y" u2="&#x2f;" k="244" />
<hkern u1="Z" u2="x" k="-23" />
<hkern u1="\" u2="&#x178;" k="244" />
<hkern u1="\" u2="&#xdd;" k="244" />
<hkern u1="\" u2="Y" k="244" />
<hkern u1="\" u2="W" k="150" />
<hkern u1="\" u2="V" k="190" />
<hkern u1="\" u2="T" k="180" />
<hkern u1="\" u2="&#x37;" k="29" />
<hkern u1="_" u2="&#x178;" k="180" />
<hkern u1="_" u2="&#x153;" k="115" />
<hkern u1="_" u2="&#x152;" k="135" />
<hkern u1="_" u2="&#xff;" k="102" />
<hkern u1="_" u2="&#xfd;" k="102" />
<hkern u1="_" u2="&#xf8;" k="115" />
<hkern u1="_" u2="&#xf6;" k="115" />
<hkern u1="_" u2="&#xf5;" k="115" />
<hkern u1="_" u2="&#xf4;" k="115" />
<hkern u1="_" u2="&#xf3;" k="115" />
<hkern u1="_" u2="&#xf2;" k="115" />
<hkern u1="_" u2="&#xf0;" k="115" />
<hkern u1="_" u2="&#xeb;" k="115" />
<hkern u1="_" u2="&#xea;" k="115" />
<hkern u1="_" u2="&#xe9;" k="115" />
<hkern u1="_" u2="&#xe8;" k="115" />
<hkern u1="_" u2="&#xe7;" k="115" />
<hkern u1="_" u2="&#xe6;" k="115" />
<hkern u1="_" u2="&#xe5;" k="115" />
<hkern u1="_" u2="&#xe4;" k="115" />
<hkern u1="_" u2="&#xe3;" k="115" />
<hkern u1="_" u2="&#xe2;" k="115" />
<hkern u1="_" u2="&#xe1;" k="115" />
<hkern u1="_" u2="&#xe0;" k="115" />
<hkern u1="_" u2="&#xdd;" k="180" />
<hkern u1="_" u2="&#xd8;" k="135" />
<hkern u1="_" u2="&#xd6;" k="135" />
<hkern u1="_" u2="&#xd5;" k="135" />
<hkern u1="_" u2="&#xd4;" k="135" />
<hkern u1="_" u2="&#xd3;" k="135" />
<hkern u1="_" u2="&#xd2;" k="135" />
<hkern u1="_" u2="&#xc7;" k="135" />
<hkern u1="_" u2="y" k="102" />
<hkern u1="_" u2="w" k="106" />
<hkern u1="_" u2="v" k="102" />
<hkern u1="_" u2="q" k="115" />
<hkern u1="_" u2="o" k="115" />
<hkern u1="_" u2="g" k="115" />
<hkern u1="_" u2="e" k="115" />
<hkern u1="_" u2="d" k="115" />
<hkern u1="_" u2="c" k="115" />
<hkern u1="_" u2="a" k="115" />
<hkern u1="_" u2="Y" k="180" />
<hkern u1="_" u2="W" k="117" />
<hkern u1="_" u2="V" k="160" />
<hkern u1="_" u2="T" k="139" />
<hkern u1="_" u2="Q" k="135" />
<hkern u1="_" u2="O" k="135" />
<hkern u1="_" u2="G" k="135" />
<hkern u1="_" u2="C" k="135" />
<hkern u1="_" u2="&#x30;" k="90" />
<hkern u1="b" u2="x" k="43" />
<hkern u1="b" u2="_" k="70" />
<hkern u1="b" u2="X" k="59" />
<hkern u1="b" u2="V" k="170" />
<hkern u1="c" u2="V" k="61" />
<hkern u1="e" u2="x" k="18" />
<hkern u1="e" u2="V" k="121" />
<hkern u1="f" u2="_" k="27" />
<hkern u1="f" u2="&#x3f;" k="-16" />
<hkern u1="f" u2="&#x2a;" k="-57" />
<hkern u1="f" u2="&#x21;" k="-20" />
<hkern u1="g" u2="&#x201c;" k="51" />
<hkern u1="g" u2="&#x2018;" k="51" />
<hkern u1="g" u2="j" k="-12" />
<hkern u1="h" u2="V" k="106" />
<hkern u1="j" u2="j" k="-27" />
<hkern u1="k" u2="&#xf8;" k="47" />
<hkern u1="m" u2="V" k="106" />
<hkern u1="n" u2="&#x201c;" k="61" />
<hkern u1="n" u2="&#x2018;" k="61" />
<hkern u1="n" u2="V" k="106" />
<hkern u1="o" u2="x" k="43" />
<hkern u1="o" u2="_" k="70" />
<hkern u1="o" u2="X" k="59" />
<hkern u1="o" u2="V" k="170" />
<hkern u1="p" u2="x" k="43" />
<hkern u1="p" u2="_" k="70" />
<hkern u1="p" u2="X" k="59" />
<hkern u1="p" u2="V" k="170" />
<hkern u1="r" u2="x" k="12" />
<hkern u1="s" u2="x" k="23" />
<hkern u1="s" u2="V" k="76" />
<hkern u1="u" u2="V" k="109" />
<hkern u1="v" u2="x" k="-2" />
<hkern u1="v" u2="_" k="117" />
<hkern u1="v" u2="X" k="106" />
<hkern u1="v" u2="V" k="45" />
<hkern u1="w" u2="x" k="-4" />
<hkern u1="w" u2="_" k="43" />
<hkern u1="w" u2="X" k="55" />
<hkern u1="w" u2="V" k="31" />
<hkern u1="x" u2="&#x203a;" k="2" />
<hkern u1="x" u2="&#x2039;" k="94" />
<hkern u1="x" u2="&#x201d;" k="-6" />
<hkern u1="x" u2="&#x201c;" k="-20" />
<hkern u1="x" u2="&#x2019;" k="-6" />
<hkern u1="x" u2="&#x2018;" k="-20" />
<hkern u1="x" u2="&#x2014;" k="117" />
<hkern u1="x" u2="&#x2013;" k="117" />
<hkern u1="x" u2="&#x153;" k="43" />
<hkern u1="x" u2="&#xff;" k="-2" />
<hkern u1="x" u2="&#xfd;" k="-2" />
<hkern u1="x" u2="&#xf8;" k="43" />
<hkern u1="x" u2="&#xf6;" k="43" />
<hkern u1="x" u2="&#xf5;" k="43" />
<hkern u1="x" u2="&#xf4;" k="43" />
<hkern u1="x" u2="&#xf3;" k="43" />
<hkern u1="x" u2="&#xf2;" k="43" />
<hkern u1="x" u2="&#xf0;" k="43" />
<hkern u1="x" u2="&#xeb;" k="43" />
<hkern u1="x" u2="&#xea;" k="43" />
<hkern u1="x" u2="&#xe9;" k="43" />
<hkern u1="x" u2="&#xe8;" k="43" />
<hkern u1="x" u2="&#xe7;" k="43" />
<hkern u1="x" u2="&#xe6;" k="43" />
<hkern u1="x" u2="&#xe5;" k="43" />
<hkern u1="x" u2="&#xe4;" k="43" />
<hkern u1="x" u2="&#xe3;" k="43" />
<hkern u1="x" u2="&#xe2;" k="43" />
<hkern u1="x" u2="&#xe1;" k="43" />
<hkern u1="x" u2="&#xe0;" k="43" />
<hkern u1="x" u2="&#xbb;" k="2" />
<hkern u1="x" u2="&#xad;" k="117" />
<hkern u1="x" u2="&#xab;" k="94" />
<hkern u1="x" u2="y" k="-2" />
<hkern u1="x" u2="w" k="-4" />
<hkern u1="x" u2="v" k="-2" />
<hkern u1="x" u2="q" k="43" />
<hkern u1="x" u2="o" k="43" />
<hkern u1="x" u2="g" k="43" />
<hkern u1="x" u2="e" k="43" />
<hkern u1="x" u2="d" k="43" />
<hkern u1="x" u2="c" k="43" />
<hkern u1="x" u2="a" k="43" />
<hkern u1="x" u2="W" k="45" />
<hkern u1="x" u2="V" k="53" />
<hkern u1="x" u2="T" k="109" />
<hkern u1="x" u2="&#x32;" k="-12" />
<hkern u1="x" u2="&#x2d;" k="117" />
<hkern u1="y" u2="&#x153;" k="18" />
<hkern u1="y" u2="&#xf8;" k="18" />
<hkern u1="y" u2="&#xf6;" k="18" />
<hkern u1="y" u2="&#xf5;" k="18" />
<hkern u1="y" u2="&#xf4;" k="18" />
<hkern u1="y" u2="&#xf3;" k="18" />
<hkern u1="y" u2="&#xf2;" k="18" />
<hkern u1="y" u2="&#xf0;" k="18" />
<hkern u1="y" u2="&#xeb;" k="18" />
<hkern u1="y" u2="&#xea;" k="18" />
<hkern u1="y" u2="&#xe9;" k="18" />
<hkern u1="y" u2="&#xe8;" k="18" />
<hkern u1="y" u2="&#xe7;" k="18" />
<hkern u1="y" u2="&#xe6;" k="18" />
<hkern u1="y" u2="&#xe5;" k="18" />
<hkern u1="y" u2="&#xe4;" k="18" />
<hkern u1="y" u2="&#xe3;" k="18" />
<hkern u1="y" u2="&#xe2;" k="18" />
<hkern u1="y" u2="&#xe1;" k="18" />
<hkern u1="y" u2="&#xe0;" k="18" />
<hkern u1="y" u2="x" k="-2" />
<hkern u1="y" u2="q" k="18" />
<hkern u1="y" u2="o" k="18" />
<hkern u1="y" u2="g" k="18" />
<hkern u1="y" u2="e" k="18" />
<hkern u1="y" u2="d" k="18" />
<hkern u1="y" u2="c" k="18" />
<hkern u1="y" u2="a" k="18" />
<hkern u1="y" u2="_" k="117" />
<hkern u1="y" u2="X" k="106" />
<hkern u1="y" u2="V" k="45" />
<hkern u1="&#xab;" u2="x" k="8" />
<hkern u1="&#xab;" u2="V" k="55" />
<hkern u1="&#xad;" u2="x" k="117" />
<hkern u1="&#xad;" u2="X" k="61" />
<hkern u1="&#xad;" u2="V" k="115" />
<hkern u1="&#xad;" u2="&#x32;" k="92" />
<hkern u1="&#xad;" u2="&#x31;" k="102" />
<hkern u1="&#xbb;" u2="x" k="94" />
<hkern u1="&#xbb;" u2="a" k="10" />
<hkern u1="&#xbb;" u2="X" k="90" />
<hkern u1="&#xbb;" u2="V" k="139" />
<hkern u1="&#xbb;" u2="J" k="37" />
<hkern u1="&#xc0;" u2="x" k="-47" />
<hkern u1="&#xc0;" u2="\" k="195" />
<hkern u1="&#xc0;" u2="V" k="174" />
<hkern u1="&#xc1;" u2="x" k="-47" />
<hkern u1="&#xc1;" u2="\" k="195" />
<hkern u1="&#xc1;" u2="V" k="174" />
<hkern u1="&#xc2;" u2="x" k="-47" />
<hkern u1="&#xc2;" u2="\" k="195" />
<hkern u1="&#xc2;" u2="V" k="174" />
<hkern u1="&#xc3;" u2="x" k="-47" />
<hkern u1="&#xc3;" u2="\" k="195" />
<hkern u1="&#xc3;" u2="V" k="174" />
<hkern u1="&#xc4;" u2="x" k="-47" />
<hkern u1="&#xc4;" u2="\" k="195" />
<hkern u1="&#xc4;" u2="V" k="174" />
<hkern u1="&#xc5;" u2="x" k="-47" />
<hkern u1="&#xc5;" u2="\" k="195" />
<hkern u1="&#xc5;" u2="V" k="174" />
<hkern u1="&#xc6;" u2="x" k="-6" />
<hkern u1="&#xc8;" u2="x" k="-6" />
<hkern u1="&#xc9;" u2="x" k="-6" />
<hkern u1="&#xca;" u2="x" k="-6" />
<hkern u1="&#xcb;" u2="x" k="-6" />
<hkern u1="&#xd2;" u2="x" k="18" />
<hkern u1="&#xd2;" u2="_" k="135" />
<hkern u1="&#xd2;" u2="X" k="49" />
<hkern u1="&#xd2;" u2="V" k="92" />
<hkern u1="&#xd3;" u2="x" k="18" />
<hkern u1="&#xd3;" u2="_" k="135" />
<hkern u1="&#xd3;" u2="X" k="49" />
<hkern u1="&#xd3;" u2="V" k="92" />
<hkern u1="&#xd4;" u2="x" k="18" />
<hkern u1="&#xd4;" u2="_" k="135" />
<hkern u1="&#xd4;" u2="X" k="49" />
<hkern u1="&#xd4;" u2="V" k="92" />
<hkern u1="&#xd5;" u2="x" k="18" />
<hkern u1="&#xd5;" u2="_" k="135" />
<hkern u1="&#xd5;" u2="X" k="49" />
<hkern u1="&#xd5;" u2="V" k="92" />
<hkern u1="&#xd6;" u2="x" k="18" />
<hkern u1="&#xd6;" u2="_" k="135" />
<hkern u1="&#xd6;" u2="X" k="49" />
<hkern u1="&#xd6;" u2="V" k="92" />
<hkern u1="&#xd8;" u2="x" k="18" />
<hkern u1="&#xd8;" u2="_" k="135" />
<hkern u1="&#xd8;" u2="X" k="49" />
<hkern u1="&#xd8;" u2="V" k="92" />
<hkern u1="&#xd9;" u2="x" k="20" />
<hkern u1="&#xd9;" u2="a" k="10" />
<hkern u1="&#xda;" u2="x" k="20" />
<hkern u1="&#xda;" u2="a" k="10" />
<hkern u1="&#xdb;" u2="x" k="20" />
<hkern u1="&#xdb;" u2="a" k="10" />
<hkern u1="&#xdc;" u2="x" k="20" />
<hkern u1="&#xdc;" u2="a" k="10" />
<hkern u1="&#xdd;" u2="&#xff;" k="90" />
<hkern u1="&#xdd;" u2="&#xf6;" k="156" />
<hkern u1="&#xdd;" u2="&#xf5;" k="178" />
<hkern u1="&#xdd;" u2="&#xf4;" k="207" />
<hkern u1="&#xdd;" u2="&#xee;" k="-35" />
<hkern u1="&#xdd;" u2="&#xec;" k="-37" />
<hkern u1="&#xdd;" u2="&#xeb;" k="119" />
<hkern u1="&#xdd;" u2="&#xea;" k="174" />
<hkern u1="&#xdd;" u2="&#xe8;" k="176" />
<hkern u1="&#xdd;" u2="&#xe4;" k="156" />
<hkern u1="&#xdd;" u2="&#xe3;" k="182" />
<hkern u1="&#xdd;" u2="&#xe2;" k="229" />
<hkern u1="&#xdd;" u2="&#xe0;" k="203" />
<hkern u1="&#xdd;" u2="x" k="109" />
<hkern u1="&#xdd;" u2="_" k="180" />
<hkern u1="&#xdd;" u2="V" k="-20" />
<hkern u1="&#xdd;" u2="&#x34;" k="131" />
<hkern u1="&#xdd;" u2="&#x2f;" k="244" />
<hkern u1="&#xde;" u2="&#x178;" k="147" />
<hkern u1="&#xde;" u2="&#xdd;" k="147" />
<hkern u1="&#xde;" u2="&#xc5;" k="51" />
<hkern u1="&#xde;" u2="&#xc4;" k="51" />
<hkern u1="&#xde;" u2="&#xc3;" k="51" />
<hkern u1="&#xde;" u2="&#xc2;" k="51" />
<hkern u1="&#xde;" u2="&#xc1;" k="51" />
<hkern u1="&#xde;" u2="&#xc0;" k="51" />
<hkern u1="&#xde;" u2="_" k="104" />
<hkern u1="&#xde;" u2="Y" k="147" />
<hkern u1="&#xde;" u2="W" k="43" />
<hkern u1="&#xde;" u2="V" k="51" />
<hkern u1="&#xde;" u2="T" k="59" />
<hkern u1="&#xde;" u2="A" k="51" />
<hkern u1="&#xdf;" u2="&#xff;" k="100" />
<hkern u1="&#xdf;" u2="&#xfd;" k="100" />
<hkern u1="&#xdf;" u2="y" k="100" />
<hkern u1="&#xdf;" u2="w" k="88" />
<hkern u1="&#xdf;" u2="v" k="100" />
<hkern u1="&#xe6;" u2="x" k="18" />
<hkern u1="&#xe6;" u2="V" k="121" />
<hkern u1="&#xe7;" u2="V" k="61" />
<hkern u1="&#xe8;" u2="x" k="18" />
<hkern u1="&#xe8;" u2="V" k="121" />
<hkern u1="&#xe9;" u2="x" k="18" />
<hkern u1="&#xe9;" u2="V" k="121" />
<hkern u1="&#xea;" u2="x" k="18" />
<hkern u1="&#xea;" u2="V" k="121" />
<hkern u1="&#xeb;" u2="x" k="18" />
<hkern u1="&#xeb;" u2="V" k="121" />
<hkern u1="&#xf2;" u2="x" k="43" />
<hkern u1="&#xf2;" u2="_" k="70" />
<hkern u1="&#xf2;" u2="X" k="59" />
<hkern u1="&#xf2;" u2="V" k="170" />
<hkern u1="&#xf3;" u2="x" k="43" />
<hkern u1="&#xf3;" u2="_" k="70" />
<hkern u1="&#xf3;" u2="X" k="59" />
<hkern u1="&#xf3;" u2="V" k="170" />
<hkern u1="&#xf4;" u2="x" k="43" />
<hkern u1="&#xf4;" u2="_" k="70" />
<hkern u1="&#xf4;" u2="X" k="59" />
<hkern u1="&#xf4;" u2="V" k="170" />
<hkern u1="&#xf5;" u2="x" k="43" />
<hkern u1="&#xf5;" u2="_" k="70" />
<hkern u1="&#xf5;" u2="X" k="59" />
<hkern u1="&#xf5;" u2="V" k="170" />
<hkern u1="&#xf6;" u2="x" k="43" />
<hkern u1="&#xf6;" u2="_" k="70" />
<hkern u1="&#xf6;" u2="X" k="59" />
<hkern u1="&#xf6;" u2="V" k="170" />
<hkern u1="&#xf8;" u2="x" k="43" />
<hkern u1="&#xf8;" u2="_" k="70" />
<hkern u1="&#xf8;" u2="X" k="59" />
<hkern u1="&#xf8;" u2="V" k="170" />
<hkern u1="&#xf9;" u2="V" k="109" />
<hkern u1="&#xfa;" u2="V" k="109" />
<hkern u1="&#xfb;" u2="V" k="109" />
<hkern u1="&#xfc;" u2="V" k="109" />
<hkern u1="&#xfd;" u2="x" k="-2" />
<hkern u1="&#xfd;" u2="_" k="117" />
<hkern u1="&#xfd;" u2="X" k="106" />
<hkern u1="&#xfd;" u2="V" k="45" />
<hkern u1="&#xfe;" u2="x" k="43" />
<hkern u1="&#xfe;" u2="_" k="70" />
<hkern u1="&#xfe;" u2="X" k="59" />
<hkern u1="&#xfe;" u2="V" k="170" />
<hkern u1="&#xff;" u2="x" k="-2" />
<hkern u1="&#xff;" u2="_" k="117" />
<hkern u1="&#xff;" u2="X" k="106" />
<hkern u1="&#xff;" u2="V" k="45" />
<hkern u1="&#x152;" u2="x" k="-6" />
<hkern u1="&#x153;" u2="x" k="18" />
<hkern u1="&#x153;" u2="V" k="121" />
<hkern u1="&#x178;" u2="&#xff;" k="90" />
<hkern u1="&#x178;" u2="&#xf6;" k="156" />
<hkern u1="&#x178;" u2="&#xf5;" k="178" />
<hkern u1="&#x178;" u2="&#xf4;" k="207" />
<hkern u1="&#x178;" u2="&#xee;" k="-35" />
<hkern u1="&#x178;" u2="&#xec;" k="-37" />
<hkern u1="&#x178;" u2="&#xeb;" k="119" />
<hkern u1="&#x178;" u2="&#xea;" k="174" />
<hkern u1="&#x178;" u2="&#xe8;" k="176" />
<hkern u1="&#x178;" u2="&#xe4;" k="156" />
<hkern u1="&#x178;" u2="&#xe3;" k="182" />
<hkern u1="&#x178;" u2="&#xe2;" k="229" />
<hkern u1="&#x178;" u2="&#xe0;" k="203" />
<hkern u1="&#x178;" u2="x" k="109" />
<hkern u1="&#x178;" u2="_" k="180" />
<hkern u1="&#x178;" u2="V" k="-20" />
<hkern u1="&#x178;" u2="&#x34;" k="131" />
<hkern u1="&#x178;" u2="&#x2f;" k="244" />
<hkern u1="&#x2013;" u2="x" k="117" />
<hkern u1="&#x2013;" u2="X" k="61" />
<hkern u1="&#x2013;" u2="V" k="115" />
<hkern u1="&#x2013;" u2="&#x32;" k="92" />
<hkern u1="&#x2013;" u2="&#x31;" k="109" />
<hkern u1="&#x2014;" u2="x" k="117" />
<hkern u1="&#x2014;" u2="X" k="61" />
<hkern u1="&#x2014;" u2="V" k="115" />
<hkern u1="&#x2014;" u2="&#x32;" k="92" />
<hkern u1="&#x2014;" u2="&#x31;" k="102" />
<hkern u1="&#x2018;" u2="x" k="37" />
<hkern u1="&#x2018;" u2="j" k="41" />
<hkern u1="&#x2019;" u2="x" k="49" />
<hkern u1="&#x2019;" u2="j" k="39" />
<hkern u1="&#x2019;" u2="a" k="104" />
<hkern u1="&#x2019;" u2="V" k="-4" />
<hkern u1="&#x201a;" u2="V" k="106" />
<hkern u1="&#x201c;" u2="x" k="37" />
<hkern u1="&#x201c;" u2="j" k="41" />
<hkern u1="&#x201d;" u2="x" k="49" />
<hkern u1="&#x201d;" u2="j" k="39" />
<hkern u1="&#x201d;" u2="a" k="104" />
<hkern u1="&#x201d;" u2="V" k="-4" />
<hkern u1="&#x201e;" u2="V" k="106" />
<hkern u1="&#x2026;" u2="V" k="238" />
<hkern u1="&#x2026;" u2="&#x39;" k="84" />
<hkern u1="&#x2026;" u2="&#x38;" k="70" />
<hkern u1="&#x2026;" u2="&#x37;" k="33" />
<hkern u1="&#x2026;" u2="&#x2a;" k="246" />
<hkern u1="&#x2039;" u2="x" k="8" />
<hkern u1="&#x2039;" u2="V" k="55" />
<hkern u1="&#x203a;" u2="x" k="94" />
<hkern u1="&#x203a;" u2="a" k="10" />
<hkern u1="&#x203a;" u2="X" k="90" />
<hkern u1="&#x203a;" u2="V" k="139" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="12" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="53" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="74" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,y,yacute,ydieresis" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="47" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="G" 	g2="T" 	k="76" />
<hkern g1="G" 	g2="w" 	k="33" />
<hkern g1="G" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="G" 	g2="t" 	k="18" />
<hkern g1="G" 	g2="z" 	k="20" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="23" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="88" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="49" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="88" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="57" />
<hkern g1="K" 	g2="w" 	k="129" />
<hkern g1="K" 	g2="v,y,yacute,ydieresis" 	k="139" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="104" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="94" />
<hkern g1="K" 	g2="t" 	k="72" />
<hkern g1="K" 	g2="s" 	k="29" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-18" />
<hkern g1="K" 	g2="z" 	k="-20" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="53" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="309" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="289" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="182" />
<hkern g1="L" 	g2="T" 	k="207" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="166" />
<hkern g1="L" 	g2="w" 	k="137" />
<hkern g1="L" 	g2="v,y,yacute,ydieresis" 	k="172" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="L" 	g2="t" 	k="78" />
<hkern g1="L" 	g2="W" 	k="141" />
<hkern g1="L" 	g2="z" 	k="6" />
<hkern g1="L" 	g2="J" 	k="-10" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="29" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="137" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="78" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="63" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="6" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="102" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="41" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="84" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="117" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="R" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="57" />
<hkern g1="R" 	g2="W" 	k="41" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-23" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="S" 	g2="T" 	k="35" />
<hkern g1="S" 	g2="w" 	k="33" />
<hkern g1="S" 	g2="v,y,yacute,ydieresis" 	k="49" />
<hkern g1="S" 	g2="t" 	k="14" />
<hkern g1="S" 	g2="W" 	k="41" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="2" />
<hkern g1="S" 	g2="z" 	k="25" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="268" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="195" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-33" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="221" />
<hkern g1="T" 	g2="w" 	k="217" />
<hkern g1="T" 	g2="v,y,yacute,ydieresis" 	k="227" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="209" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="211" />
<hkern g1="T" 	g2="s" 	k="184" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="188" />
<hkern g1="T" 	g2="z" 	k="154" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="193" />
<hkern g1="T" 	g2="J" 	k="119" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="205" />
<hkern g1="T" 	g2="AE" 	k="211" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="242" />
<hkern g1="T" 	g2="idieresis" 	k="-98" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="35" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="35" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="123" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="70" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-10" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="70" />
<hkern g1="W" 	g2="w" 	k="61" />
<hkern g1="W" 	g2="v,y,yacute,ydieresis" 	k="43" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="59" />
<hkern g1="W" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="86" />
<hkern g1="W" 	g2="t" 	k="35" />
<hkern g1="W" 	g2="s" 	k="94" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="111" />
<hkern g1="W" 	g2="J" 	k="84" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="125" />
<hkern g1="W" 	g2="AE" 	k="190" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="80" />
<hkern g1="W" 	g2="S" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="172" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-33" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="188" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="129" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute,ydieresis" 	k="139" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="188" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="233" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="170" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="199" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="135" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="238" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="170" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="idieresis" 	k="-106" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="33" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="63" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="18" />
<hkern g1="Z" 	g2="w" 	k="68" />
<hkern g1="Z" 	g2="v,y,yacute,ydieresis" 	k="96" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="96" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="Z" 	g2="idieresis" 	k="-82" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="47" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="23" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v,y,yacute,ydieresis" 	k="25" />
<hkern g1="comma,period,ellipsis" 	g2="quoteleft,quotedblleft" 	k="219" />
<hkern g1="comma,period,ellipsis" 	g2="quoteright,quotedblright" 	k="229" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="236" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="205" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="35" />
<hkern g1="comma,period,ellipsis" 	g2="quotedbl,quotesingle" 	k="96" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="145" />
<hkern g1="comma,period,ellipsis" 	g2="v,y,yacute,ydieresis" 	k="178" />
<hkern g1="comma,period,ellipsis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="152" />
<hkern g1="comma,period,ellipsis" 	g2="comma,period,ellipsis" 	k="-25" />
<hkern g1="comma,period,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="14" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="66" />
<hkern g1="comma,period,ellipsis" 	g2="parenright,bracketright,braceright" 	k="43" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="145" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="119" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="213" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="201" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="47" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v,y,yacute,ydieresis" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="74" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="18" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f,uniFB01,uniFB02" 	k="6" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-16" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="f" 	g2="w" 	k="-18" />
<hkern g1="f" 	g2="v,y,yacute,ydieresis" 	k="-14" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="f" 	g2="t" 	k="6" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="55" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="59" />
<hkern g1="f" 	g2="parenright,bracketright,braceright" 	k="-70" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="172" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="195" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="w" 	k="10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v,y,yacute,ydieresis" 	k="18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="43" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="s" 	k="66" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="z" 	k="12" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Z" 	k="18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="S" 	k="-6" />
<hkern g1="guillemotright,guilsinglright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="195" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="268" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="v,y,yacute,ydieresis" 	k="66" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="100" />
<hkern g1="guillemotright,guilsinglright" 	g2="s" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="43" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="47" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="188" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="209" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="w" 	k="31" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="v,y,yacute,ydieresis" 	k="47" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="61" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="51" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="121" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="33" />
<hkern g1="idieresis" 	g2="Y,Yacute,Ydieresis" 	k="-49" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-12" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="4" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="k" 	g2="w" 	k="-2" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="139" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="92" />
<hkern g1="k" 	g2="s" 	k="47" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="29" />
<hkern g1="h,m,n" 	g2="quoteleft,quotedblleft" 	k="104" />
<hkern g1="h,m,n" 	g2="quoteright,quotedblright" 	k="158" />
<hkern g1="h,m,n" 	g2="Y,Yacute,Ydieresis" 	k="172" />
<hkern g1="h,m,n" 	g2="T" 	k="190" />
<hkern g1="h,m,n" 	g2="quotedbl,quotesingle" 	k="84" />
<hkern g1="h,m,n" 	g2="w" 	k="70" />
<hkern g1="h,m,n" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="h,m,n" 	g2="t" 	k="20" />
<hkern g1="h,m,n" 	g2="W" 	k="70" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="guillemotright,guilsinglright" 	k="66" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="86" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="139" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="233" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="203" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="115" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="63" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v,y,yacute,ydieresis" 	k="80" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="W" 	k="129" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="18" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotesinglbase,quotedblbase" 	k="29" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,ellipsis" 	k="37" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="parenright,bracketright,braceright" 	k="27" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="57" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="88" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-12" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="76" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="49" />
<hkern g1="quoteleft,quotedblleft" 	g2="v,y,yacute,ydieresis" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="205" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="152" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="233" />
<hkern g1="quoteleft,quotedblleft" 	g2="z" 	k="68" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,ellipsis" 	k="289" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde" 	k="102" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="94" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-4" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="84" />
<hkern g1="quoteright,quotedblright" 	g2="w" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="v,y,yacute,ydieresis" 	k="98" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="266" />
<hkern g1="quoteright,quotedblright" 	g2="t" 	k="68" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="244" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="217" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="53" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,ellipsis" 	k="297" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="135" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="125" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="139" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="53" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v,y,yacute,ydieresis" 	k="98" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="115" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="170" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,ellipsis" 	k="96" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="49" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="59" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="174" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="207" />
<hkern g1="s" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="s" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="68" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="104" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="217" />
<hkern g1="s" 	g2="T" 	k="213" />
<hkern g1="s" 	g2="w" 	k="49" />
<hkern g1="s" 	g2="v,y,yacute,ydieresis" 	k="70" />
<hkern g1="s" 	g2="hyphen,uni00AD,endash,emdash" 	k="14" />
<hkern g1="s" 	g2="t" 	k="31" />
<hkern g1="s" 	g2="W" 	k="106" />
<hkern g1="s" 	g2="z" 	k="14" />
<hkern g1="s" 	g2="Z" 	k="20" />
<hkern g1="s" 	g2="quotesinglbase,quotedblbase" 	k="18" />
<hkern g1="s" 	g2="f,uniFB01,uniFB02" 	k="27" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="t" 	g2="quotedbl,quotesingle" 	k="6" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="80" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="w" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="w" 	g2="T" 	k="94" />
<hkern g1="w" 	g2="w" 	k="-10" />
<hkern g1="w" 	g2="v,y,yacute,ydieresis" 	k="-6" />
<hkern g1="w" 	g2="hyphen,uni00AD,endash,emdash" 	k="49" />
<hkern g1="w" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="w" 	g2="t" 	k="-2" />
<hkern g1="w" 	g2="W" 	k="25" />
<hkern g1="w" 	g2="s" 	k="33" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="68" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="111" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="127" />
<hkern g1="w" 	g2="f,uniFB01,uniFB02" 	k="-2" />
<hkern g1="zero" 	g2="comma,period,ellipsis" 	k="66" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="v,y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="137" />
<hkern g1="v,y,yacute,ydieresis" 	g2="T" 	k="205" />
<hkern g1="v,y,yacute,ydieresis" 	g2="w" 	k="-6" />
<hkern g1="v,y,yacute,ydieresis" 	g2="v,y,yacute,ydieresis" 	k="-8" />
<hkern g1="v,y,yacute,ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="49" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="v,y,yacute,ydieresis" 	g2="t" 	k="-4" />
<hkern g1="v,y,yacute,ydieresis" 	g2="W" 	k="45" />
<hkern g1="v,y,yacute,ydieresis" 	g2="s" 	k="33" />
<hkern g1="v,y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="v,y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="113" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="188" />
<hkern g1="v,y,yacute,ydieresis" 	g2="f,uniFB01,uniFB02" 	k="-2" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="z" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="z" 	g2="z" 	k="6" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="c,ccedilla" 	g2="T" 	k="139" />
<hkern g1="c,ccedilla" 	g2="w" 	k="12" />
<hkern g1="c,ccedilla" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="c,ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="c,ccedilla" 	g2="W" 	k="35" />
</font>
</defs></svg> 