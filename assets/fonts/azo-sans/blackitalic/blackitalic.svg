<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="azo_sansblack_italic" horiz-adv-x="653" >
<font-face units-per-em="2048" ascent="1581" descent="-467" />
<missing-glyph horiz-adv-x="473" />
<glyph unicode="&#xfb01;" horiz-adv-x="1439" d="M61 688l58 320h133l29 168q37 215 182 317q130 94 348 94q89 0 174 -18l-57 -328q-86 19 -127 19q-49 0 -76 -21q-20 -16 -29 -42t-18 -77l-21 -112h220l-58 -320h-217l-121 -688h-405l121 688h-136zM834 0l178 1008h405l-178 -1008h-405zM1061 1333q0 93 66 157t159 64 q87 0 145 -58t58 -142q0 -93 -66 -157t-159 -64q-87 0 -145 58t-58 142z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1439" d="M61 688l58 320h133l29 168q37 215 182 317q130 94 348 94q89 0 174 -18l-57 -328q-86 19 -127 19q-49 0 -76 -21q-20 -16 -29 -42t-18 -77l-21 -112h220l-58 -320h-217l-121 -688h-405l121 688h-136zM834 0l276 1565h406l-277 -1565h-405z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode="&#xd;" horiz-adv-x="735" />
<glyph unicode=" "  horiz-adv-x="473" />
<glyph unicode="&#x09;" horiz-adv-x="473" />
<glyph unicode="&#xa0;" horiz-adv-x="473" />
<glyph unicode="!" horiz-adv-x="579" d="M18 178q0 93 66.5 157t159.5 64q87 0 144.5 -58t57.5 -142q0 -93 -66 -157.5t-159 -64.5q-87 0 -145 58.5t-58 142.5zM150 520l73 963h475l-266 -963h-282z" />
<glyph unicode="&#x22;" horiz-adv-x="860" d="M176 926l57 594h328l-155 -594h-230zM596 926l57 594h328l-156 -594h-229z" />
<glyph unicode="#" horiz-adv-x="1308" d="M-10 397l108 236h197l102 217h-196l108 235h197l184 398h254l-184 -398h188l185 398h253l-184 -398h197l-109 -235h-196l-103 -217h197l-109 -236h-196l-185 -397h-254l185 397h-189l-184 -397h-254l184 397h-196zM549 633h188l103 217h-189z" />
<glyph unicode="$" horiz-adv-x="1259" d="M127 213l63 358q98 -68 225 -112.5t214 -44.5q104 0 104 67q0 20 -9.5 34.5t-33 27.5t-47.5 22.5t-70.5 28t-83.5 35.5q-137 62 -201.5 134.5t-64.5 192.5q0 168 119 279.5t305 140.5l47 262h307l-47 -272q136 -28 238 -78l-62 -342q-104 56 -214 88.5t-179 32.5 q-94 0 -94 -63q0 -15 6.5 -27t16.5 -20.5t31.5 -19t41 -18.5t57 -23.5t68.5 -29.5q146 -65 216.5 -142t70.5 -208q0 -171 -116 -277t-304 -135l-45 -260h-307l47 269q-156 25 -299 100z" />
<glyph unicode="%" horiz-adv-x="1955" d="M170 1073q0 80 26 156t74.5 138t126 100t172.5 38q162 0 260.5 -89t98.5 -245q0 -80 -26.5 -156t-75 -138t-126 -100t-172.5 -38q-162 0 -260 89t-98 245zM223 0l1309 1483h283l-1309 -1483h-283zM436 1094q0 -52 24 -87.5t72 -35.5q59 0 94.5 55t35.5 125 q0 52 -24.5 87.5t-72.5 35.5q-59 0 -94 -55t-35 -125zM1110 311q0 80 26 156t74.5 138t126 100t172.5 38q162 0 260.5 -88.5t98.5 -244.5q0 -80 -26.5 -156t-75 -138.5t-126 -100.5t-172.5 -38q-162 0 -260 89t-98 245zM1376 332q0 -52 24.5 -87.5t72.5 -35.5q59 0 94 55 t35 125q0 52 -24.5 87.5t-72.5 35.5q-59 0 -94 -55t-35 -125z" />
<glyph unicode="&#x26;" horiz-adv-x="1492" d="M39 385q0 139 86 251t217 167q-96 129 -96 268q0 95 44 177.5t117 138.5t167 88t194 32q185 0 314.5 -100t129.5 -260q0 -126 -80.5 -228t-203.5 -171l-47 -27l108 -143l205 311h403l-399 -590l225 -295l-2 -4h-471l-47 59q-125 -82 -325 -82q-237 0 -388 111.5 t-151 296.5zM453 434q0 -57 40.5 -93t116.5 -36q55 0 95 19l-179 235q-73 -45 -73 -125zM641 1077q0 -42 37 -90l18 -24l35 22q80 54 80 117q0 34 -21 55t-55 21q-40 0 -67 -29t-27 -72z" />
<glyph unicode="'" horiz-adv-x="440" d="M176 926l57 594h328l-155 -594h-230z" />
<glyph unicode="(" horiz-adv-x="765" d="M66 279q0 397 159 753q128 289 355 490h370q-112 -105 -210.5 -249t-166.5 -310q-141 -343 -141 -719q0 -378 156 -680h-320q-202 299 -202 715z" />
<glyph unicode=")" horiz-adv-x="765" d="M-174 -436q112 105 210.5 249t166.5 310q141 343 141 719q0 378 -156 680h320q203 -301 203 -715q0 -396 -160 -754q-128 -288 -354 -489h-371z" />
<glyph unicode="*" horiz-adv-x="903" d="M139 1106l111 211l229 -123l-69 -133zM180 838l230 198l106 -82l-172 -247zM502 1208l6 275h229l-90 -275h-145zM541 954l133 82l162 -198l-209 -131zM666 1194l274 123l31 -211l-285 -45z" />
<glyph unicode="+" horiz-adv-x="1239" d="M125 604l49 275h356l66 366h307l-65 -366h356l-49 -275h-357l-65 -366h-307l65 366h-356z" />
<glyph unicode="," horiz-adv-x="661" d="M-70 -301l226 637h366l-315 -637h-277z" />
<glyph unicode="-" d="M51 412l55 311h515l-56 -311h-514z" />
<glyph unicode="." horiz-adv-x="661" d="M53 186q0 96 69 163t165 67q90 0 150.5 -61t60.5 -148q0 -96 -69 -163t-165 -67q-91 0 -151 61t-60 148z" />
<glyph unicode="/" horiz-adv-x="974" d="M-135 -78l1012 1639h313l-1012 -1639h-313z" />
<glyph unicode="0" horiz-adv-x="1339" d="M111 532q0 162 42 346.5t123 323.5q90 156 218 229.5t303 73.5q252 0 381 -143.5t129 -413.5q0 -160 -42.5 -344t-123.5 -323q-90 -156 -218.5 -230t-304.5 -74q-250 0 -378.5 142.5t-128.5 412.5zM532 539q0 -195 115 -195q57 0 96.5 47.5t69.5 136.5q30 90 51 208.5 t21 207.5q0 195 -115 195q-57 0 -96.5 -47.5t-69.5 -137.5t-51 -208.5t-21 -206.5z" />
<glyph unicode="1" horiz-adv-x="997" d="M156 911l65 371l742 223l-269 -1505h-420l175 999z" />
<glyph unicode="2" horiz-adv-x="1198" d="M-88 4l557 612q154 169 208 247t54 145q0 66 -45 98.5t-127 32.5q-164 0 -344 -117l70 405q65 35 158.5 56.5t193.5 21.5q111 0 206.5 -28t168 -81t114 -136.5t41.5 -188.5q0 -138 -67.5 -262t-198.5 -270l-160 -179l2 -4h355l-64 -356h-1120z" />
<glyph unicode="3" horiz-adv-x="1175" d="M31 82l67 393q198 -139 389 -139q89 0 138 37q49 40 49 94q0 55 -48.5 89t-142.5 34h-145l51 295h164q109 0 158 49q37 37 37 92q0 64 -51 94.5t-128 30.5q-75 0 -163.5 -26t-163.5 -68l67 385q133 63 320 63q100 0 194 -23t176 -69t131.5 -125.5t49.5 -181.5 q0 -136 -80 -232q-71 -84 -182 -124v-5q182 -92 182 -286q0 -102 -51 -199.5t-148 -165.5q-168 -117 -424 -117q-241 0 -446 105z" />
<glyph unicode="4" horiz-adv-x="1347" d="M-43 295l946 1188h383l-158 -893h191l-53 -299h-191l-51 -291h-422l51 291h-694zM551 594l2 -4h154l14 82q16 91 37 180l-4 2q-52 -72 -123 -160z" />
<glyph unicode="5" horiz-adv-x="1179" d="M10 80l90 375q219 -119 394 -119q99 0 149 37q45 32 45 82q0 105 -227 157q-119 28 -314 35l203 836h871l-86 -357h-453l-55 -223q165 -22 272 -80q96 -51 155.5 -138t59.5 -210q0 -115 -55 -212.5t-150 -164.5q-172 -121 -432 -121q-232 0 -467 103z" />
<glyph unicode="6" horiz-adv-x="1292" d="M78 455q0 85 21.5 163.5t66 154t88 133t111.5 136.5l376 441h510l-481 -525l2 -4q20 4 43 4q81 0 158.5 -29t141 -83t102 -140t38.5 -190q0 -122 -51.5 -226t-144.5 -177q-172 -136 -426 -136q-87 0 -166.5 17t-151 54.5t-124 92.5t-83 135.5t-30.5 178.5zM502 467 q0 -65 44 -107.5t111 -42.5q75 0 127 52t52 127q0 68 -43 109.5t-113 41.5q-74 0 -125 -51q-53 -53 -53 -129z" />
<glyph unicode="7" horiz-adv-x="1136" d="M43 0l623 1126h-549l63 357h1125l2 -4l-818 -1479h-446z" />
<glyph unicode="8" horiz-adv-x="1353" d="M59 410q0 134 81 242t220 153v6q-137 91 -137 258q0 101 54.5 188.5t154.5 149.5q159 98 369 98q144 0 263.5 -49.5t190 -141t70.5 -206.5q0 -110 -64.5 -201t-173.5 -135l-2 -6q181 -103 181 -305q0 -220 -205 -361q-183 -123 -428 -123q-152 0 -281 49.5t-211 150 t-82 233.5zM496 457q0 -64 44.5 -104t114.5 -40q77 0 127 47q52 45 52 115q0 63 -44.5 105.5t-113.5 42.5q-76 0 -129 -50q-51 -47 -51 -116zM627 1034q0 -53 38 -89t97 -36q70 0 115 45q41 41 41 97q0 54 -37 89.5t-99 35.5q-66 0 -112 -43q-43 -43 -43 -99z" />
<glyph unicode="9" horiz-adv-x="1290" d="M131 963q0 119 53.5 224.5t147.5 178.5q175 137 418 137q150 0 276.5 -54.5t206.5 -164.5t80 -256q0 -79 -21.5 -157.5t-63.5 -155.5t-85 -139t-105 -140l-348 -436h-516l438 528l-2 4q-16 -4 -41 -4q-112 0 -211.5 53t-163 154.5t-63.5 227.5zM549 987q0 -63 41.5 -105 t111.5 -42q79 0 132 59q45 51 45 119q0 61 -41.5 104t-108.5 43q-79 0 -131 -55q-49 -49 -49 -123z" />
<glyph unicode=":" horiz-adv-x="661" d="M59 178q0 93 66.5 157t159.5 64q87 0 144.5 -58t57.5 -142q0 -93 -66 -157.5t-159 -64.5q-87 0 -145 58.5t-58 142.5zM170 809q0 93 66 157t159 64q87 0 145 -58.5t58 -142.5q0 -93 -66 -157t-159 -64q-87 0 -145 58.5t-58 142.5z" />
<glyph unicode=";" horiz-adv-x="661" d="M-70 -301l226 637h366l-315 -637h-277zM170 809q0 93 66 157t159 64q87 0 145 -58.5t58 -142.5q0 -93 -66 -157t-159 -64q-87 0 -145 58.5t-58 142.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1503" d="M168 641l35 201l1286 428l-51 -289l-719 -238v-4l635 -237l-51 -289z" />
<glyph unicode="=" horiz-adv-x="1239" d="M84 371l49 274h1020l-49 -274h-1020zM166 838l49 274h1020l-49 -274h-1020z" />
<glyph unicode="&#x3e;" horiz-adv-x="1503" d="M92 213l51 289l719 237v4l-635 238l52 289l1134 -428l-35 -201z" />
<glyph unicode="?" horiz-adv-x="1001" d="M104 180q0 92 66.5 155.5t159.5 63.5q87 0 144.5 -58.5t57.5 -143.5q0 -92 -66 -156t-159 -64q-87 0 -145 59t-58 144zM213 483l63 359q369 0 369 182q0 69 -48 108t-128 39q-100 0 -240 -63l64 360q101 37 225 37q149 0 272.5 -55.5t197 -159.5t73.5 -237 q0 -83 -29.5 -159.5t-83.5 -139t-137.5 -105t-185.5 -53.5l-47 -113h-365z" />
<glyph unicode="@" horiz-adv-x="1650" d="M47 424q0 131 40 256.5t115.5 231t177 186t233.5 125.5t276 45q203 0 366.5 -86t257 -242t93.5 -352q0 -74 -13.5 -142t-43.5 -130t-73.5 -107.5t-108.5 -72.5t-144 -27q-158 0 -244 92h-4q-99 -92 -248 -92q-144 0 -229.5 93.5t-85.5 241.5q0 86 30 167t84 145.5 t137 103.5t181 39q93 0 190 -34t178 -97l-47 -272q-10 -62 -10 -101q0 -52 24.5 -86.5t80.5 -34.5q49 0 85 28t53.5 74.5t25.5 93.5t8 97q0 112 -37.5 210.5t-105.5 173t-171.5 118t-228.5 43.5q-140 0 -264.5 -53.5t-212.5 -144t-139.5 -214t-51.5 -258.5q0 -154 64 -276.5 t190.5 -196t294.5 -73.5q212 0 391 116l66 -141q-197 -131 -459 -131q-206 0 -369.5 84t-257.5 241t-94 359zM672 451q0 -140 112 -140q67 0 121 51q-2 12 -2 41q0 41 15 129l22 132q-43 30 -100 30t-96.5 -41t-55.5 -94t-16 -108z" />
<glyph unicode="A" horiz-adv-x="1550" d="M-111 0l844 1483h416l330 -1483h-441l-55 303h-491l-160 -303h-443zM657 614h269l-15 76q-33 188 -51 316h-4q-64 -133 -162 -322z" />
<glyph unicode="B" horiz-adv-x="1325" d="M25 0l262 1483h483q252 0 381 -70q170 -93 170 -274q0 -121 -74 -207.5t-198 -124.5l-2 -4q112 -49 164.5 -124t52.5 -181q0 -122 -64.5 -228t-179.5 -172q-176 -98 -485 -98h-510zM506 336h121q117 0 164 39q51 42 51 119q0 71 -46 99t-130 28h-111zM604 891h105 q190 0 190 143q0 62 -39 88q-35 25 -119 25h-92z" />
<glyph unicode="C" horiz-adv-x="1382" d="M102 639q0 172 69.5 340.5t193.5 290.5q117 114 272 174.5t338 60.5q251 0 452 -106l-67 -387q-206 118 -402 118q-187 0 -305 -118q-62 -62 -95.5 -151t-33.5 -179q0 -159 102 -244.5t273 -85.5q94 0 201.5 26t195.5 71l-71 -408q-157 -64 -365 -64q-219 0 -389 76 t-269.5 227.5t-99.5 358.5z" />
<glyph unicode="D" horiz-adv-x="1540" d="M25 0l262 1483h387q372 0 573 -131q126 -81 198.5 -218t72.5 -315q0 -180 -77.5 -348.5t-228.5 -284.5q-241 -186 -628 -186h-559zM510 356h111q229 0 350 121q62 61 93.5 144.5t31.5 173.5q0 185 -133 268q-101 61 -279 61h-39z" />
<glyph unicode="E" horiz-adv-x="1146" d="M25 0l262 1483h946l-64 -357h-524l-33 -188h441l-60 -336h-440l-43 -246h549l-64 -356h-970z" />
<glyph unicode="F" horiz-adv-x="1159" d="M25 0l262 1483h966l-63 -357h-545l-39 -217h465l-59 -336h-465l-101 -573h-421z" />
<glyph unicode="G" horiz-adv-x="1482" d="M100 633q0 173 68.5 342.5t198.5 294.5q119 114 274 174.5t332 60.5q272 0 493 -135l-67 -387q-242 147 -443 147q-197 0 -311 -126q-59 -64 -91 -156t-32 -184q0 -152 91.5 -241t254.5 -89q40 0 76 8l29 160h-189l56 309h592l-132 -745q-209 -89 -454 -89 q-158 0 -293 42.5t-236.5 123t-159 206.5t-57.5 284z" />
<glyph unicode="H" horiz-adv-x="1583" d="M25 0l262 1483h422l-95 -535h508l95 535h421l-262 -1483h-422l105 592h-508l-105 -592h-421z" />
<glyph unicode="I" d="M25 0l262 1483h422l-263 -1483h-421z" />
<glyph unicode="J" horiz-adv-x="868" d="M-57 18l63 357q106 -45 187 -45q60 0 89.5 36.5t43.5 116.5l176 1000h422l-185 -1043q-40 -219 -161 -334q-137 -129 -383 -129q-134 0 -252 41z" />
<glyph unicode="K" horiz-adv-x="1462" d="M25 0l262 1483h422l-109 -619l4 -2l516 621h506l-598 -688l348 -795h-491l-301 750h-4l-134 -750h-421z" />
<glyph unicode="L" horiz-adv-x="1165" d="M25 0l262 1483h422l-199 -1127h598l-64 -356h-1019z" />
<glyph unicode="M" horiz-adv-x="1775" d="M25 0l262 1483h391l272 -539l463 539h418l-262 -1483h-422l90 510q33 190 63 340h-4q-162 -195 -215 -256l-237 -277l-142 281q-44 88 -122 252h-5q-19 -129 -47 -291l-98 -559h-405z" />
<glyph unicode="N" horiz-adv-x="1656" d="M25 0l262 1483h393l252 -457q103 -186 223 -414l4 2q30 198 74 447l74 422h405l-262 -1483h-393l-252 457q-113 204 -223 413l-4 -2q-29 -192 -74 -446l-74 -422h-405z" />
<glyph unicode="O" horiz-adv-x="1673" d="M102 664q0 153 54.5 306t165.5 275q111 124 265.5 192t334.5 68q149 0 281 -47t231.5 -133t158 -217t58.5 -289q0 -154 -54.5 -306.5t-164.5 -274.5q-112 -125 -267 -193t-334 -68q-149 0 -281 47.5t-231.5 133.5t-158 217t-58.5 289zM530 692q0 -148 84.5 -248 t231.5 -100q161 0 262 123q51 61 83 147t32 177q0 148 -84.5 248t-231.5 100q-161 0 -262 -123q-51 -61 -83 -147t-32 -177z" />
<glyph unicode="P" horiz-adv-x="1347" d="M25 0l262 1483h446q160 0 270.5 -25.5t192.5 -83.5t128 -144t46 -188q0 -106 -51 -215.5t-154 -189.5q-184 -141 -491 -141h-139l-89 -496h-421zM590 815h133q106 0 164 49q61 53 61 140q0 77 -57 112q-48 31 -152 31h-90z" />
<glyph unicode="Q" horiz-adv-x="1673" d="M102 664q0 153 54.5 306t165.5 275q111 124 265.5 192t334.5 68q149 0 281 -47t231.5 -133t158 -217t58.5 -289q0 -241 -121 -448q-59 -100 -139 -176t-166 -119v-4q189 -120 377 -201l-377 -215q-244 121 -451 324q-188 12 -338.5 94.5t-242 236t-91.5 353.5zM530 692 q0 -148 84.5 -248t231.5 -100q161 0 262 123q51 61 83 147t32 177q0 148 -84.5 248t-231.5 100q-161 0 -262 -123q-51 -61 -83 -147t-32 -177z" />
<glyph unicode="R" horiz-adv-x="1353" d="M25 0l262 1483h465q283 0 422 -90q172 -110 172 -316q0 -163 -97 -291q-89 -119 -237 -172l288 -614h-466l-230 563h-59l-99 -563h-421zM598 852h119q97 0 153 47q54 46 54 121q0 68 -47 98q-42 29 -136 29h-92z" />
<glyph unicode="S" horiz-adv-x="1204" d="M43 127l72 405q97 -84 231 -144t240 -60q63 0 96 26t33 70q0 24 -10 43t-36.5 37t-51 30.5t-75.5 37t-87 44.5q-55 30 -95.5 56t-80 62t-64.5 74t-40 88.5t-15 109.5q0 110 48 207t138 167q159 125 395 125q119 0 242 -34t225 -97l-67 -379q-103 67 -223 113.5t-205 46.5 q-53 0 -86 -20q-39 -24 -39 -74q0 -16 3.5 -28.5t14.5 -24t22 -20.5t34 -21.5t42 -21.5t55.5 -27t65.5 -33q77 -40 129.5 -76t98 -84.5t68 -110t22.5 -137.5q0 -108 -45 -202t-127 -162q-164 -136 -420 -136q-132 0 -263.5 39t-244.5 111z" />
<glyph unicode="T" horiz-adv-x="1415" d="M139 1126l64 357h1351l-63 -357h-465l-199 -1126h-421l198 1126h-465z" />
<glyph unicode="U" horiz-adv-x="1560" d="M117 496q0 110 20 221l135 766h422l-135 -768q-14 -85 -14 -142q0 -229 227 -229q135 0 213 88q38 42 60 102.5t40 161.5l140 787h405l-141 -799q-32 -184 -80.5 -301.5t-126.5 -198.5q-98 -101 -239 -154t-308 -53q-133 0 -246 34.5t-196 99.5t-129.5 163.5t-46.5 221.5 z" />
<glyph unicode="V" horiz-adv-x="1511" d="M164 1483h438l90 -518q44 -249 70 -426h4q156 310 217 426l268 518h439l-811 -1483h-420z" />
<glyph unicode="W" horiz-adv-x="2250" d="M193 1483h440l51 -527q25 -266 33 -387h4q60 140 176 389l246 525h309l57 -522q22 -206 37 -392h4q59 136 179 398l237 516h428l-713 -1483h-444l-39 356q-25 238 -37 387h-4q-68 -155 -178 -393l-164 -350h-436z" />
<glyph unicode="X" horiz-adv-x="1456" d="M-127 0l633 760l-340 723h477l66 -156q50 -120 108 -270h4q98 134 201 268l119 158h491l-598 -723l365 -760h-484l-75 178q-79 191 -115 285h-4q-133 -179 -215 -285l-137 -178h-496z" />
<glyph unicode="Y" horiz-adv-x="1472" d="M135 1483h475l88 -193q65 -144 119 -270h4q152 193 213 268l156 195h489l-700 -809l-119 -674h-422l117 668z" />
<glyph unicode="Z" horiz-adv-x="1335" d="M-59 4l778 1118l-2 4h-531l64 357h1223l2 -4l-779 -1119l2 -4h559l-63 -356h-1251z" />
<glyph unicode="[" horiz-adv-x="763" d="M-25 -436l347 1958h548l-49 -275h-172l-248 -1409h172l-49 -274h-549z" />
<glyph unicode="\" horiz-adv-x="974" d="M176 1561h291l414 -1639h-291z" />
<glyph unicode="]" horiz-adv-x="763" d="M-98 -436l49 274h172l248 1409h-172l49 275h549l-346 -1958h-549z" />
<glyph unicode="^" horiz-adv-x="1114" d="M109 741l489 764h264l223 -764h-309l-114 375h-3l-243 -375h-307z" />
<glyph unicode="_" horiz-adv-x="1060" d="M-141 -315l26 155h1053l-27 -155h-1052z" />
<glyph unicode="`" horiz-adv-x="1126" d="M469 1489h309l92 -336h-249z" />
<glyph unicode="a" horiz-adv-x="1261" d="M51 412q0 127 51.5 247.5t145.5 208.5q172 162 444 162q133 0 273.5 -38.5t251.5 -96.5l-158 -895h-393l10 59l-4 2q-87 -84 -230 -84q-170 0 -280.5 121.5t-110.5 313.5zM461 457q0 -77 40 -121.5t111 -44.5q50 0 97 20l65 375q-48 23 -106 23q-98 0 -152 -74 q-55 -73 -55 -178z" />
<glyph unicode="b" horiz-adv-x="1259" d="M27 45l268 1520h405l-106 -596l4 -2q91 63 215 63q170 0 279.5 -123.5t109.5 -308.5q0 -118 -48 -242t-144 -215q-174 -164 -504 -164q-268 0 -479 68zM477 299q36 -8 86 -8q111 0 172 78q26 33 42 82t16 94q0 72 -40.5 121t-111.5 49q-46 0 -94 -25z" />
<glyph unicode="c" horiz-adv-x="1071" d="M47 438q0 122 53 244t158 207q85 68 199 104.5t241 36.5q175 0 349 -59l-64 -357q-162 86 -301 86q-102 0 -158 -53q-32 -29 -49.5 -74t-17.5 -92q0 -76 51.5 -124t157.5 -48q139 0 297 62l-62 -348q-127 -43 -293 -43q-105 0 -201.5 27t-178.5 80t-131.5 143.5 t-49.5 207.5z" />
<glyph unicode="d" horiz-adv-x="1259" d="M51 414q0 126 51 248.5t146 209.5q80 74 190 116t240 42q76 0 151 -16l99 551h405l-276 -1565h-398l9 45l-4 2q-93 -70 -224 -70q-170 0 -279.5 122.5t-109.5 314.5zM461 465q0 -79 41 -125.5t110 -46.5q54 0 97 24l63 369q-52 27 -106 27q-93 0 -150 -74 q-55 -73 -55 -174z" />
<glyph unicode="e" horiz-adv-x="1163" d="M49 432q0 126 51.5 246.5t147.5 206.5q161 145 395 145q94 0 178 -26.5t151.5 -78t107.5 -136t40 -191.5q0 -92 -24 -186h-645q29 -144 219 -144q73 0 167 19.5t173 50.5l-54 -307q-66 -24 -164 -39t-186 -15q-116 0 -214.5 27.5t-176 81.5t-122 142.5t-44.5 203.5z M477 621h277q-3 59 -38 90.5t-87 31.5q-100 0 -152 -122z" />
<glyph unicode="f" horiz-adv-x="823" d="M61 688l58 320h133l29 168q37 215 182 317q130 94 348 94q89 0 174 -18l-57 -328q-86 19 -127 19q-49 0 -76 -21q-20 -16 -29 -42t-18 -77l-21 -112h220l-58 -320h-217l-121 -688h-405l121 688h-136z" />
<glyph unicode="g" horiz-adv-x="1239" d="M23 -389l61 346q191 -109 346 -109q94 0 143 47t70 148l6 35l-4 2q-91 -62 -201 -62q-160 0 -276.5 114.5t-116.5 301.5q0 118 50.5 235t142.5 203q172 158 432 158q131 0 273 -37t249 -92l-143 -815q-46 -268 -206 -413.5t-446 -145.5q-196 0 -380 84zM453 477 q0 -74 39 -117t106 -43q52 0 98 27l62 352q-44 23 -105 23q-91 0 -147 -72q-53 -71 -53 -170z" />
<glyph unicode="h" horiz-adv-x="1257" d="M18 0l277 1565h405l-110 -627l4 -2q123 94 260 94q149 0 237.5 -89t88.5 -228q0 -41 -11 -109l-106 -604h-406l101 569q4 20 4 39q0 40 -24 64t-68 24q-70 0 -131 -47l-115 -649h-406z" />
<glyph unicode="i" horiz-adv-x="624" d="M18 0l179 1008h405l-178 -1008h-406zM246 1333q0 93 66 157t159 64q87 0 145 -58t58 -142q0 -93 -66 -157t-159 -64q-87 0 -145 58t-58 142z" />
<glyph unicode="j" horiz-adv-x="626" d="M-272 -453l57 326q78 -20 121 -20q56 0 80 32.5t37 106.5l178 1016h405l-186 -1059q-37 -209 -146 -307q-125 -115 -360 -115q-107 0 -186 20zM250 1333q0 93 66 157t159 64q87 0 145 -58t58 -142q0 -93 -66 -157t-159 -64q-87 0 -145 58t-58 142z" />
<glyph unicode="k" horiz-adv-x="1236" d="M18 0l277 1565h405l-168 -947l5 -2l342 392h448l-414 -451l211 -557h-432l-174 510h-4l-90 -510h-406z" />
<glyph unicode="l" horiz-adv-x="624" d="M18 0l277 1565h405l-276 -1565h-406z" />
<glyph unicode="m" horiz-adv-x="1880" d="M18 0l179 1008h397l-14 -76l4 -2q126 100 266 100q88 0 158 -39t112 -108q69 62 163 104.5t192 42.5q145 0 236 -92t91 -233q0 -49 -10 -107l-104 -598h-408l100 571q4 20 4 39q0 43 -25.5 64.5t-68.5 21.5q-67 0 -125 -49q0 -15 -6 -49l-104 -598h-406l99 571q4 24 4 39 q0 43 -26 64.5t-69 21.5q-62 0 -118 -43l-115 -653h-406z" />
<glyph unicode="n" horiz-adv-x="1257" d="M18 0l179 1008h397l-14 -76l4 -2q129 100 274 100q147 0 234.5 -89t87.5 -228q0 -41 -11 -109l-106 -604h-406l101 569q4 20 4 39q0 40 -24 64t-68 24q-69 0 -131 -47l-115 -649h-406z" />
<glyph unicode="o" horiz-adv-x="1253" d="M51 446q0 113 44 223.5t128 192.5q171 168 441 168q150 0 269.5 -54.5t191 -162t71.5 -252.5q0 -113 -44 -223.5t-128 -192.5q-171 -168 -440 -168q-150 0 -270 54.5t-191.5 162.5t-71.5 252zM453 475q0 -72 40.5 -116t110.5 -44q81 0 133 60q58 68 58 157 q0 71 -41 115.5t-111 44.5q-82 0 -133 -59q-57 -67 -57 -158z" />
<glyph unicode="p" horiz-adv-x="1255" d="M-61 -451l258 1459h397l-10 -56l4 -2q80 80 221 80q170 0 279.5 -126t109.5 -320q0 -125 -47.5 -242.5t-128.5 -200.5q-164 -164 -416 -164q-89 0 -178 25l-4 -2l-80 -451h-405zM481 322q42 -29 113 -29q92 0 145 74q49 70 49 163q0 82 -41 131.5t-116 49.5 q-42 0 -86 -23z" />
<glyph unicode="q" horiz-adv-x="1261" d="M51 414q0 125 49.5 246.5t143.5 207.5q175 162 463 162q239 0 514 -108l-242 -1373h-406l84 478l-4 2q-91 -52 -204 -52q-175 0 -286.5 122.5t-111.5 314.5zM461 459q0 -77 41 -122.5t110 -45.5q56 0 97 20l67 383q-44 19 -102 19q-101 0 -158 -76q-55 -73 -55 -178z" />
<glyph unicode="r" horiz-adv-x="905" d="M18 0l179 1008h395l-19 -109l5 -2q115 125 266 125q50 0 102 -18l-65 -369q-58 22 -113 22q-139 0 -244 -84l-100 -573h-406z" />
<glyph unicode="s" horiz-adv-x="1073" d="M37 94l57 326q100 -70 221 -113t197 -43q55 0 55 43q0 15 -9.5 24.5t-30.5 17t-45.5 13.5t-63.5 18t-74 26q-225 91 -225 258q0 165 141 272q129 94 328 94q217 0 418 -92l-56 -313q-89 57 -195 87.5t-171 30.5q-56 0 -56 -38q0 -13 8.5 -22t28.5 -16.5t42 -13.5t61 -17 t73 -24q236 -87 236 -262q0 -75 -35 -146t-98 -122q-133 -105 -336 -105q-234 0 -471 117z" />
<glyph unicode="t" horiz-adv-x="890" d="M61 688l58 320h147l49 276l410 25l-53 -301h237l-57 -320h-238l-47 -262q-4 -20 -4 -39q0 -84 90 -84q66 0 135 29l-55 -320q-81 -35 -198 -35q-76 0 -142.5 17.5t-122 53.5t-88 99.5t-32.5 147.5q0 59 12 123l47 270h-148z" />
<glyph unicode="u" horiz-adv-x="1259" d="M78 297q0 44 10 106l107 605h403l-100 -570q-4 -24 -4 -41q0 -40 25 -64t71 -24q64 0 127 53l114 646h404l-178 -1008h-398l15 80l-4 2q-117 -105 -269 -105q-148 0 -235.5 89.5t-87.5 230.5z" />
<glyph unicode="v" horiz-adv-x="1146" d="M76 1008h401l37 -232q33 -208 45 -307h4q61 122 150 293l129 246h401l-590 -1008h-370z" />
<glyph unicode="w" horiz-adv-x="1669" d="M78 1008h399l27 -230q20 -176 28 -299h5q62 152 129 299l104 230h309l23 -230q17 -177 22 -299h4q64 145 136 299l108 230h393l-520 -1008h-366l-17 166q-19 193 -26 317h-5q-80 -186 -141 -321l-74 -162h-376z" />
<glyph unicode="x" horiz-adv-x="1130" d="M-125 0l465 551l-248 457h404l45 -103q31 -70 61 -151h4q62 88 113 155l74 99h413l-415 -480l280 -528h-420l-57 131q-25 55 -74 176h-4q-62 -93 -117 -166l-104 -141h-420z" />
<glyph unicode="y" horiz-adv-x="1138" d="M-74 -451l377 574l-240 885h406l43 -226q29 -154 47 -272h4q46 84 150 268l133 230h395l-897 -1459h-418z" />
<glyph unicode="z" horiz-adv-x="1017" d="M-68 4l484 680l-2 4h-314l58 320h921l2 -4l-483 -680l2 -5h336l-57 -319h-945z" />
<glyph unicode="{" horiz-adv-x="839" d="M57 406l49 274h64q64 0 96.5 29t44.5 100l72 412q26 143 137.5 233t288.5 90q86 0 176 -22l-47 -269q-46 11 -76 11q-93 0 -112 -109l-60 -332q-22 -121 -67.5 -182.5t-133.5 -87.5v-4q59 -25 87 -67.5t28 -112.5q0 -31 -12 -105l-57 -330q-5 -30 -5 -36q0 -86 105 -86 q21 0 49 4l-47 -267q-50 -8 -102 -8q-175 0 -282.5 74.5t-107.5 218.5q0 28 7 72l65 370q6 34 6 54q0 76 -100 76h-66z" />
<glyph unicode="|" horiz-adv-x="661" d="M8 -451l369 2089h305l-369 -2089h-305z" />
<glyph unicode="}" horiz-adv-x="839" d="M-135 -436l47 268q42 -10 76 -10q93 0 112 108l60 332q22 122 67 183t133 87v5q-59 25 -86.5 67.5t-27.5 112.5q0 30 12 104l57 330q4 24 4 37q0 86 -104 86q-21 0 -49 -4l47 266q50 8 102 8q175 0 282.5 -74.5t107.5 -218.5q0 -27 -7 -71l-65 -371q-6 -35 -6 -53 q0 -76 100 -76h66l-50 -274h-63q-64 0 -96.5 -29.5t-44.5 -100.5l-72 -411q-26 -143 -138 -233.5t-288 -90.5q-82 0 -176 23z" />
<glyph unicode="~" horiz-adv-x="1150" d="M176 559l55 313q87 76 207 76q103 0 228 -61q5 -2 22 -11t22.5 -11.5t20.5 -9.5t22 -9.5t20.5 -7.5t23 -6.5t22.5 -3t25 -1.5q113 0 207 101l4 -2l-56 -314q-85 -75 -206 -75q-103 0 -228 61q-1 1 -19 10t-20.5 10t-18 8.5t-20 9t-18 7t-20.5 6.5t-18.5 4t-22 3.5 t-21.5 0.5q-114 0 -207 -100z" />
<glyph unicode="&#xa1;" horiz-adv-x="579" d="M-123 -475l266 962h283l-74 -962h-475zM129 809q0 93 66 157t159 64q87 0 145 -58.5t58 -142.5q0 -93 -66 -157t-159 -64q-87 0 -145 58.5t-58 142.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1077" d="M98 676q0 122 53 243.5t158 206.5q103 84 260 121l47 273h308l-47 -260q117 -16 221 -52l-64 -356q-162 86 -301 86q-102 0 -158 -53q-32 -29 -49.5 -74t-17.5 -92q0 -76 51.5 -124t157.5 -48q141 0 297 61l-62 -348q-114 -40 -258 -43l-45 -254h-307l53 301 q-135 51 -216 154t-81 258z" />
<glyph unicode="&#xa3;" horiz-adv-x="1196" d="M37 0l51 287q99 39 157.5 122.5t61.5 198.5h-162l43 248h88q-8 62 -8 117q0 126 48 228.5t130.5 168t191 100.5t232.5 35q206 0 365 -88l-66 -377q-172 109 -295 109q-178 0 -178 -186q0 -37 4 -107h250l-43 -248h-196q-10 -152 -144 -248v-4h572l-64 -356h-1038z" />
<glyph unicode="&#xa4;" horiz-adv-x="1198" d="M4 375l207 176q-12 49 -12 108q0 127 59 236l-133 164l270 229l131 -162q79 27 154 27q106 0 190 -41l207 176l189 -229l-207 -176q12 -50 12 -109q0 -126 -59 -235l133 -164l-271 -230l-131 162q-76 -26 -153 -26q-107 0 -191 41l-206 -177zM485 684q0 -59 33.5 -98 t95.5 -39q72 0 121 62.5t49 140.5q0 59 -33.5 98t-95.5 39q-72 0 -121 -62.5t-49 -140.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1458" d="M162 1483h473l67 -148q65 -144 119 -270h4q152 193 213 268l119 150h488l-394 -455h113l-45 -250h-285l-51 -59l-12 -68h325l-45 -250h-325l-70 -401h-422l70 401h-326l45 250h326l10 62l-33 65h-280l45 250h106z" />
<glyph unicode="&#xa6;" horiz-adv-x="661" d="M8 -451l142 803h305l-142 -803h-305zM236 836l141 802h305l-141 -802h-305z" />
<glyph unicode="&#xa7;" horiz-adv-x="1218" d="M109 -233l59 333q64 -47 149.5 -77.5t151.5 -30.5q55 0 83 20t28 56q0 23 -12 42.5t-38.5 39.5t-54 36t-73 43.5t-80.5 51.5q-99 67 -154 142t-55 179q0 106 62 188t163 125v5q-57 79 -57 184q0 117 70 209.5t185 142t250 49.5q88 0 179.5 -20.5t152.5 -50.5l-61 -322 q-135 78 -256 78q-49 0 -84 -18.5t-35 -57.5q0 -21 9.5 -39.5t35 -38t47 -33.5t68 -41.5t76.5 -46.5q58 -37 97.5 -68t72 -70.5t48 -85t15.5 -101.5q0 -112 -62 -195.5t-165 -124.5v-4q55 -71 55 -180q0 -122 -73.5 -215.5t-190 -140.5t-254.5 -47q-95 0 -189.5 23 t-162.5 61zM492 631q0 -42 40.5 -75t121.5 -78q16 -9 24 -13q38 11 66 34.5t28 57.5q0 13 -4.5 24.5t-16.5 24t-21 21t-31.5 22.5t-34.5 21t-41.5 23.5t-42.5 23.5q-88 -27 -88 -86z" />
<glyph unicode="&#xa8;" horiz-adv-x="1126" d="M299 1313q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5zM766 1313q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1640" d="M78 741q0 214 102 389t281 275t399 100t399 -100t281 -275t102 -389t-102 -389t-281 -275t-399 -100t-399 100t-281 275t-102 389zM236 741q0 -175 77 -316t220 -222.5t327 -81.5t327.5 81.5t220.5 222.5t77 316q0 131 -45.5 245.5t-126 197.5t-198 130.5t-255.5 47.5 q-184 0 -327 -81.5t-220 -223t-77 -316.5zM453 750q0 183 133.5 301t333.5 118q123 0 225 -45v-247q-111 65 -213 65q-94 0 -151.5 -53t-57.5 -137t57.5 -137.5t155.5 -53.5q116 0 219 70v-248q-98 -49 -240 -49q-205 0 -333.5 118.5t-128.5 297.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="829" d="M154 1120q0 161 113.5 273t287.5 112q84 0 177.5 -29.5t172.5 -89.5l-96 -550h-262l6 34l-4 2q-48 -51 -129 -51q-114 0 -190 83.5t-76 215.5zM438 1145q0 -46 22 -71t60 -25q30 0 53 10l35 203q-24 14 -63 14q-50 0 -78.5 -38.5t-28.5 -92.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1204" d="M35 520l403 432h330l-420 -448l236 -449h-308zM532 520l404 432h330l-420 -448l235 -449h-307z" />
<glyph unicode="&#xac;" horiz-adv-x="1236" d="M123 604l49 275h1010l-113 -641h-307l65 366h-704z" />
<glyph unicode="&#xad;" d="M51 412l55 311h515l-56 -311h-514z" />
<glyph unicode="&#xae;" horiz-adv-x="903" d="M170 1124q0 163 111 272t278 109q166 0 277.5 -109t111.5 -272t-111.5 -272t-277.5 -109q-167 0 -278 109t-111 272zM260 1124q0 -129 83 -213t216 -84t216 84t83 213q0 128 -83 212.5t-216 84.5t-216 -84.5t-83 -212.5zM399 918v413h174q70 0 117 -36t47 -99 q0 -77 -75 -115l100 -163h-144l-75 143h-19v-143h-125zM524 1147h35q51 0 51 41q0 37 -51 37h-35v-78z" />
<glyph unicode="&#xaf;" horiz-adv-x="1126" d="M358 1198l43 246h652l-43 -246h-652z" />
<glyph unicode="&#xb0;" horiz-adv-x="698" d="M162 1204q0 127 89.5 214t223.5 87t223.5 -87t89.5 -214t-89.5 -214t-223.5 -87t-223.5 87t-89.5 214zM322 1204q0 -64 44 -108.5t109 -44.5t109.5 44.5t44.5 108.5t-44.5 109t-109.5 45t-109 -45t-44 -109z" />
<glyph unicode="&#xb1;" horiz-adv-x="1239" d="M74 111l49 274h946l-51 -274h-944zM195 793l49 274h319l53 305h308l-54 -305h320l-49 -274h-320l-53 -306h-307l53 306h-319z" />
<glyph unicode="&#xb2;" horiz-adv-x="770" d="M76 868l284 318q121 134 159 185t38 79q0 53 -86 53q-101 0 -225 -67l49 278q98 53 235 53q92 0 166.5 -28.5t122 -90.5t47.5 -149q0 -86 -44 -164t-117 -159l-43 -48l2 -4h176l-45 -260h-717z" />
<glyph unicode="&#xb3;" horiz-adv-x="753" d="M147 924l46 258q58 -32 87.5 -46.5t75.5 -28t88 -13.5q41 0 62 16q18 14 18 39q0 53 -100 53h-90l37 207h100q63 0 84 21q16 16 16 38q0 54 -92 54q-98 0 -219 -52l45 252q85 45 223 45q58 0 116.5 -12.5t114 -38.5t90 -75.5t34.5 -114.5q0 -77 -52 -135 q-43 -46 -108 -72v-4q111 -64 111 -174q0 -65 -33 -123t-96 -98q-112 -70 -269 -70q-151 0 -289 74z" />
<glyph unicode="&#xb4;" horiz-adv-x="1126" d="M487 1153l234 336h350l-307 -336h-277z" />
<glyph unicode="&#xb5;" horiz-adv-x="1259" d="M111 -451v1459h405v-574q0 -67 29 -96t76 -29q74 0 135 58v641h405v-1008h-397v82l-4 2q-128 -102 -275 -107l45 -428h-419z" />
<glyph unicode="&#xb6;" horiz-adv-x="1294" d="M152 1067q0 78 35 154.5t92.5 133.5t136.5 92.5t162 35.5h761l-340 -1934h-305l297 1682h-135l-297 -1682h-305l211 1192q-136 7 -224.5 101t-88.5 225z" />
<glyph unicode="&#xb7;" horiz-adv-x="661" d="M127 608q0 96 68.5 163t164.5 67q90 0 150.5 -61t60.5 -148q0 -96 -68.5 -163t-164.5 -67q-91 0 -151 61t-60 148z" />
<glyph unicode="&#xb8;" horiz-adv-x="1126" d="M213 -393l68 131q70 -51 133 -51q30 0 49.5 12t19.5 35q0 64 -161 90l139 270l117 -41l-62 -127q139 -45 139 -174q0 -90 -67 -147.5t-170 -57.5q-111 0 -205 60z" />
<glyph unicode="&#xb9;" horiz-adv-x="634" d="M207 1378l47 264l485 125l-159 -903h-301l98 557z" />
<glyph unicode="&#xba;" horiz-adv-x="835" d="M160 1122q0 163 114 273t287 110q146 0 245 -82t99 -219q0 -163 -114 -273t-287 -110q-146 0 -245 82t-99 219zM440 1139q0 -36 20.5 -59t57.5 -23q49 0 78 37t29 94q0 36 -20.5 59t-57.5 23q-49 0 -78 -37t-29 -94z" />
<glyph unicode="&#xbb;" horiz-adv-x="1204" d="M-66 55l420 449l-235 448h307l242 -465l-404 -432h-330zM432 55l420 449l-236 448h308l241 -465l-403 -432h-330z" />
<glyph unicode="&#xbc;" horiz-adv-x="2025" d="M201 1116l47 264l485 125l-160 -903h-301l99 557zM233 -27l1237 1510h355l-1237 -1510h-355zM1044 162l586 727h283l-92 -520h94l-37 -211h-94l-29 -158h-301l29 158h-436zM1450 373l2 -4h68l6 37q8 47 20 86l-4 2q-28 -43 -65 -88z" />
<glyph unicode="&#xbd;" horiz-adv-x="2054" d="M201 1116l47 264l485 125l-160 -903h-301l99 557zM233 -27l1237 1510h355l-1237 -1510h-355zM1153 4l285 318q120 133 158 184.5t38 79.5q0 53 -86 53q-99 0 -225 -68l49 279q98 53 236 53q92 0 166.5 -28.5t122 -90.5t47.5 -149q0 -86 -44 -164.5t-118 -159.5l-43 -47 l2 -4h176l-45 -260h-717z" />
<glyph unicode="&#xbe;" horiz-adv-x="2127" d="M121 662l45 258q59 -32 88 -46.5t75.5 -28.5t88.5 -14q39 0 61 17q19 15 19 39q0 53 -101 53h-90l37 207h100q64 0 84 20q17 17 17 39q0 54 -92 54q-99 0 -220 -52l46 252q85 45 223 45q58 0 116 -12.5t113.5 -38.5t90 -75.5t34.5 -114.5q0 -79 -51 -136 q-41 -44 -109 -71v-4q111 -64 111 -174q0 -65 -33 -123.5t-96 -98.5q-110 -69 -268 -69q-151 0 -289 74zM336 -27l1237 1510h354l-1237 -1510h-354zM1147 162l586 727h282l-92 -520h94l-37 -211h-94l-28 -158h-302l29 158h-436zM1552 373l2 -4h68l6 37q7 41 21 86l-4 2 q-24 -36 -66 -88z" />
<glyph unicode="&#xbf;" horiz-adv-x="1001" d="M-53 -45q0 83 29.5 159.5t83.5 139t137.5 105t185.5 53.5l47 112h365l-64 -358q-369 0 -369 -182q0 -69 48.5 -108.5t128.5 -39.5q97 0 239 64l-63 -361q-101 -37 -226 -37q-149 0 -272.5 56t-196.5 160t-73 237zM475 811q0 92 66 155.5t159 63.5q87 0 145 -59t58 -144 q0 -92 -66 -155.5t-159 -63.5q-87 0 -145 59t-58 144z" />
<glyph unicode="&#xc0;" horiz-adv-x="1550" d="M-111 0l844 1483h416l330 -1483h-441l-55 303h-491l-160 -303h-443zM657 614h269l-15 76q-33 188 -51 316h-4q-64 -133 -162 -322zM764 1964h309l92 -336h-250z" />
<glyph unicode="&#xc1;" horiz-adv-x="1550" d="M-111 0l844 1483h416l330 -1483h-441l-55 303h-491l-160 -303h-443zM657 614h269l-15 76q-33 188 -51 316h-4q-64 -133 -162 -322zM782 1628l234 336h350l-307 -336h-277z" />
<glyph unicode="&#xc2;" horiz-adv-x="1550" d="M-111 0l844 1483h416l330 -1483h-441l-55 303h-491l-160 -303h-443zM588 1628l325 336h277l168 -336h-242l-96 170l-174 -170h-258zM657 614h269l-15 76q-33 188 -51 316h-4q-64 -133 -162 -322z" />
<glyph unicode="&#xc3;" horiz-adv-x="1550" d="M-111 0l844 1483h416l330 -1483h-441l-55 303h-491l-160 -303h-443zM627 1634l45 256q80 76 190 76q72 0 176 -49q81 -38 113 -44q16 -3 33 -3q38 0 75.5 16t57 31t53.5 45l4 -2l-45 -256q-80 -76 -190 -76q-72 0 -176 49q-81 38 -113 44q-16 3 -33 3q-38 0 -75.5 -16 t-57 -31t-53.5 -45zM657 614h269l-15 76q-33 188 -51 316h-4q-64 -133 -162 -322z" />
<glyph unicode="&#xc4;" horiz-adv-x="1550" d="M-111 0l844 1483h416l330 -1483h-441l-55 303h-491l-160 -303h-443zM594 1788q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5zM657 614h269l-15 76q-33 188 -51 316h-4q-64 -133 -162 -322zM1061 1788q0 74 54 127 t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1550" d="M-111 0l844 1483h416l330 -1483h-441l-55 303h-491l-160 -303h-443zM657 614h269l-15 76q-33 188 -51 316h-4q-64 -133 -162 -322zM729 1815q0 118 84.5 204t210.5 86q110 0 187 -66.5t77 -178.5q0 -118 -84.5 -204.5t-210.5 -86.5q-110 0 -187 67t-77 179zM901 1821 q0 -41 26 -71t70 -30q51 0 85 38.5t34 94.5q0 41 -26 71t-70 30q-51 0 -85 -38.5t-34 -94.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1832" d="M-102 0l854 1483h1167l-64 -357h-491l-33 -188h408l-60 -336h-407l-43 -246h516l-64 -356h-938l54 303h-297l-160 -303h-442zM666 614h186l29 162q54 304 67 369h-4q-59 -118 -176 -338z" />
<glyph unicode="&#xc7;" horiz-adv-x="1382" d="M102 639q0 172 69.5 340.5t193.5 290.5q117 114 272 174.5t338 60.5q251 0 452 -106l-67 -387q-206 118 -402 118q-187 0 -305 -118q-62 -62 -95.5 -151t-33.5 -179q0 -159 102 -244.5t273 -85.5q94 0 201.5 26t195.5 71l-71 -408q-157 -64 -365 -64q-52 0 -86 5l-26 -56 q139 -45 139 -174q0 -90 -67.5 -147.5t-170.5 -57.5q-111 0 -205 60l68 131q70 -51 133 -51q30 0 50 12t20 35q0 63 -162 90l90 176q-248 51 -394.5 217t-146.5 422z" />
<glyph unicode="&#xc8;" horiz-adv-x="1146" d="M25 0l262 1483h946l-64 -357h-524l-33 -188h441l-60 -336h-440l-43 -246h549l-64 -356h-970zM592 1964h309l92 -336h-250z" />
<glyph unicode="&#xc9;" horiz-adv-x="1146" d="M25 0l262 1483h946l-64 -357h-524l-33 -188h441l-60 -336h-440l-43 -246h549l-64 -356h-970zM610 1628l234 336h350l-307 -336h-277z" />
<glyph unicode="&#xca;" horiz-adv-x="1146" d="M25 0l262 1483h946l-64 -357h-524l-33 -188h441l-60 -336h-440l-43 -246h549l-64 -356h-970zM416 1628l325 336h277l168 -336h-242l-96 170l-174 -170h-258z" />
<glyph unicode="&#xcb;" horiz-adv-x="1146" d="M25 0l262 1483h946l-64 -357h-524l-33 -188h441l-60 -336h-440l-43 -246h549l-64 -356h-970zM422 1788q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5zM889 1788q0 74 54 127t128 53q70 0 117 -48t47 -116 q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#xcc;" d="M25 0l262 1483h422l-263 -1483h-421zM315 1964h310l92 -336h-250z" />
<glyph unicode="&#xcd;" d="M25 0l262 1483h422l-263 -1483h-421zM334 1628l233 336h351l-308 -336h-276z" />
<glyph unicode="&#xce;" d="M25 0l262 1483h422l-263 -1483h-421zM139 1628l326 336h276l168 -336h-241l-97 170l-174 -170h-258z" />
<glyph unicode="&#xcf;" d="M25 0l262 1483h422l-263 -1483h-421zM145 1788q0 74 54.5 127t128.5 53q70 0 117 -48t47 -116q0 -74 -54.5 -127t-128.5 -53q-71 0 -117.5 47.5t-46.5 116.5zM612 1788q0 74 54.5 127t128.5 53q70 0 116.5 -48t46.5 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5 t-46.5 116.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1554" d="M12 616l49 279h136l104 588h387q373 0 574 -131q126 -81 198 -218t72 -315q0 -180 -77 -349t-228 -284q-241 -186 -629 -186h-559l108 616h-135zM524 356h111q229 0 350 121q62 61 93.5 144.5t31.5 173.5q0 185 -133 268q-101 61 -279 61h-39l-41 -229h240l-49 -279h-240 z" />
<glyph unicode="&#xd1;" horiz-adv-x="1656" d="M25 0l262 1483h393l252 -457q103 -186 223 -414l4 2q30 198 74 447l74 422h405l-262 -1483h-393l-252 457q-113 204 -223 413l-4 -2q-29 -192 -74 -446l-74 -422h-405zM680 1634l45 256q80 76 190 76q73 0 177 -49q4 -2 18 -8.5t18 -8.5t15.5 -7t16 -6.5t14.5 -5.5t16 -5 t14.5 -3t16.5 -2.5t16 -0.5q38 0 75.5 16t57 31t53.5 45l4 -2l-45 -256q-80 -76 -190 -76q-72 0 -176 49q-81 38 -113 44q-16 3 -33 3q-38 0 -75.5 -16t-57 -31t-53.5 -45z" />
<glyph unicode="&#xd2;" horiz-adv-x="1673" d="M102 664q0 153 54.5 306t165.5 275q111 124 265.5 192t334.5 68q149 0 281 -47t231.5 -133t158 -217t58.5 -289q0 -154 -54.5 -306.5t-164.5 -274.5q-112 -125 -267 -193t-334 -68q-149 0 -281 47.5t-231.5 133.5t-158 217t-58.5 289zM530 692q0 -148 84.5 -248 t231.5 -100q161 0 262 123q51 61 83 147t32 177q0 148 -84.5 248t-231.5 100q-161 0 -262 -123q-51 -61 -83 -147t-32 -177zM825 1964h310l92 -336h-250z" />
<glyph unicode="&#xd3;" horiz-adv-x="1673" d="M102 664q0 153 54.5 306t165.5 275q111 124 265.5 192t334.5 68q149 0 281 -47t231.5 -133t158 -217t58.5 -289q0 -154 -54.5 -306.5t-164.5 -274.5q-112 -125 -267 -193t-334 -68q-149 0 -281 47.5t-231.5 133.5t-158 217t-58.5 289zM530 692q0 -148 84.5 -248 t231.5 -100q161 0 262 123q51 61 83 147t32 177q0 148 -84.5 248t-231.5 100q-161 0 -262 -123q-51 -61 -83 -147t-32 -177zM844 1628l233 336h350l-307 -336h-276z" />
<glyph unicode="&#xd4;" horiz-adv-x="1673" d="M102 664q0 153 54.5 306t165.5 275q111 124 265.5 192t334.5 68q149 0 281 -47t231.5 -133t158 -217t58.5 -289q0 -154 -54.5 -306.5t-164.5 -274.5q-112 -125 -267 -193t-334 -68q-149 0 -281 47.5t-231.5 133.5t-158 217t-58.5 289zM530 692q0 -148 84.5 -248 t231.5 -100q161 0 262 123q51 61 83 147t32 177q0 148 -84.5 248t-231.5 100q-161 0 -262 -123q-51 -61 -83 -147t-32 -177zM649 1628l326 336h276l168 -336h-241l-97 170l-174 -170h-258z" />
<glyph unicode="&#xd5;" horiz-adv-x="1673" d="M102 664q0 153 54.5 306t165.5 275q111 124 265.5 192t334.5 68q149 0 281 -47t231.5 -133t158 -217t58.5 -289q0 -154 -54.5 -306.5t-164.5 -274.5q-112 -125 -267 -193t-334 -68q-149 0 -281 47.5t-231.5 133.5t-158 217t-58.5 289zM530 692q0 -148 84.5 -248 t231.5 -100q161 0 262 123q51 61 83 147t32 177q0 148 -84.5 248t-231.5 100q-161 0 -262 -123q-51 -61 -83 -147t-32 -177zM688 1634l45 256q80 76 191 76q72 0 176 -49q4 -2 18 -8.5t18 -8.5t15.5 -7t16 -6.5t14.5 -5.5t16 -5t14.5 -3t16.5 -2.5t16 -0.5q38 0 75.5 16 t58 31.5t53.5 44.5l4 -2l-45 -256q-80 -76 -191 -76q-72 0 -176 49q-4 2 -18 8.5t-18 8.5t-15.5 7t-16 6.5t-14.5 5.5t-16 5t-14.5 3t-16.5 2.5t-16 0.5q-38 0 -75.5 -16t-58 -31.5t-53.5 -44.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1673" d="M102 664q0 153 54.5 306t165.5 275q111 124 265.5 192t334.5 68q149 0 281 -47t231.5 -133t158 -217t58.5 -289q0 -154 -54.5 -306.5t-164.5 -274.5q-112 -125 -267 -193t-334 -68q-149 0 -281 47.5t-231.5 133.5t-158 217t-58.5 289zM530 692q0 -148 84.5 -248 t231.5 -100q161 0 262 123q51 61 83 147t32 177q0 148 -84.5 248t-231.5 100q-161 0 -262 -123q-51 -61 -83 -147t-32 -177zM655 1788q0 74 54.5 127t128.5 53q70 0 116.5 -48t46.5 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5zM1122 1788q0 74 54.5 127 t128.5 53q70 0 116.5 -48t46.5 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1239" d="M150 481l305 260l-213 260l241 205l213 -260l308 260l167 -205l-305 -260l213 -260l-241 -205l-213 261l-308 -261z" />
<glyph unicode="&#xd8;" horiz-adv-x="1673" d="M80 94l156 152q-134 174 -134 418q0 153 54.5 306t165.5 275q111 124 265.5 192t334.5 68q267 0 462 -141l160 156l129 -131l-155 -152q133 -173 133 -418q0 -154 -54.5 -306.5t-164.5 -274.5q-112 -125 -267 -193t-334 -68q-266 0 -462 142l-160 -156zM530 692 q0 -70 23 -137l541 526q-77 58 -187 58q-161 0 -262 -123q-51 -61 -83 -147t-32 -177zM659 401q75 -57 187 -57q161 0 262 123q51 61 83 147t32 177q0 70 -23 137z" />
<glyph unicode="&#xd9;" horiz-adv-x="1560" d="M117 496q0 110 20 221l135 766h422l-135 -768q-14 -85 -14 -142q0 -229 227 -229q135 0 213 88q38 42 60 102.5t40 161.5l140 787h405l-141 -799q-32 -184 -80.5 -301.5t-126.5 -198.5q-98 -101 -239 -154t-308 -53q-133 0 -246 34.5t-196 99.5t-129.5 163.5t-46.5 221.5 zM778 1964h309l93 -336h-250z" />
<glyph unicode="&#xda;" horiz-adv-x="1560" d="M117 496q0 110 20 221l135 766h422l-135 -768q-14 -85 -14 -142q0 -229 227 -229q135 0 213 88q38 42 60 102.5t40 161.5l140 787h405l-141 -799q-32 -184 -80.5 -301.5t-126.5 -198.5q-98 -101 -239 -154t-308 -53q-133 0 -246 34.5t-196 99.5t-129.5 163.5t-46.5 221.5 zM797 1628l233 336h350l-307 -336h-276z" />
<glyph unicode="&#xdb;" horiz-adv-x="1560" d="M117 496q0 110 20 221l135 766h422l-135 -768q-14 -85 -14 -142q0 -229 227 -229q135 0 213 88q38 42 60 102.5t40 161.5l140 787h405l-141 -799q-32 -184 -80.5 -301.5t-126.5 -198.5q-98 -101 -239 -154t-308 -53q-133 0 -246 34.5t-196 99.5t-129.5 163.5t-46.5 221.5 zM602 1628l326 336h276l168 -336h-242l-96 170l-174 -170h-258z" />
<glyph unicode="&#xdc;" horiz-adv-x="1560" d="M117 496q0 110 20 221l135 766h422l-135 -768q-14 -85 -14 -142q0 -229 227 -229q135 0 213 88q38 42 60 102.5t40 161.5l140 787h405l-141 -799q-32 -184 -80.5 -301.5t-126.5 -198.5q-98 -101 -239 -154t-308 -53q-133 0 -246 34.5t-196 99.5t-129.5 163.5t-46.5 221.5 zM608 1788q0 74 54.5 127t128.5 53q70 0 116.5 -48t46.5 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5zM1075 1788q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1472" d="M135 1483h475l88 -193q65 -144 119 -270h4q152 193 213 268l156 195h489l-700 -809l-119 -674h-422l117 668zM750 1628l233 336h350l-307 -336h-276z" />
<glyph unicode="&#xde;" horiz-adv-x="1347" d="M25 0l262 1483h422l-43 -242h24q161 0 271 -25t192 -83q80 -56 127 -142.5t47 -191.5q0 -108 -52.5 -216.5t-152.5 -187.5q-184 -141 -491 -141h-139l-46 -254h-421zM547 573h133q111 0 164 50q61 46 61 139q0 77 -57 112q-48 31 -152 31h-90z" />
<glyph unicode="&#xdf;" horiz-adv-x="1439" d="M18 0l201 1139q21 120 68 203.5t135 142.5q155 102 399 102q258 0 404 -112q127 -96 127 -254q0 -83 -33 -189t-94 -201q-50 5 -76 5q-143 0 -143 -78q0 -28 24.5 -49t112.5 -76q108 -67 162.5 -135t54.5 -172q0 -107 -61 -187.5t-162 -121t-226 -40.5q-190 0 -379 89 l58 323q71 -56 151.5 -90.5t137.5 -34.5q67 0 67 49q0 17 -11.5 31.5t-27 24.5t-51.5 33t-65 44q-92 65 -138.5 128.5t-46.5 158.5q0 129 84 217.5t223 128.5q21 48 21 84q0 49 -37 74q-36 25 -100 25q-73 0 -117 -35q-49 -40 -64 -133l-192 -1094h-406z" />
<glyph unicode="&#xe0;" horiz-adv-x="1261" d="M51 412q0 127 51.5 247.5t145.5 208.5q172 162 444 162q133 0 273.5 -38.5t251.5 -96.5l-158 -895h-393l10 59l-4 2q-87 -84 -230 -84q-170 0 -280.5 121.5t-110.5 313.5zM461 457q0 -77 40 -121.5t111 -44.5q50 0 97 20l65 375q-48 23 -106 23q-98 0 -152 -74 q-55 -73 -55 -178zM547 1489h309l92 -336h-250z" />
<glyph unicode="&#xe1;" horiz-adv-x="1261" d="M51 412q0 127 51.5 247.5t145.5 208.5q172 162 444 162q133 0 273.5 -38.5t251.5 -96.5l-158 -895h-393l10 59l-4 2q-87 -84 -230 -84q-170 0 -280.5 121.5t-110.5 313.5zM461 457q0 -77 40 -121.5t111 -44.5q50 0 97 20l65 375q-48 23 -106 23q-98 0 -152 -74 q-55 -73 -55 -178zM565 1153l234 336h350l-307 -336h-277z" />
<glyph unicode="&#xe2;" horiz-adv-x="1261" d="M51 412q0 127 51.5 247.5t145.5 208.5q172 162 444 162q133 0 273.5 -38.5t251.5 -96.5l-158 -895h-393l10 59l-4 2q-87 -84 -230 -84q-170 0 -280.5 121.5t-110.5 313.5zM371 1153l325 336h277l168 -336h-242l-96 170l-174 -170h-258zM461 457q0 -77 40 -121.5 t111 -44.5q50 0 97 20l65 375q-48 23 -106 23q-98 0 -152 -74q-55 -73 -55 -178z" />
<glyph unicode="&#xe3;" horiz-adv-x="1261" d="M51 412q0 127 51.5 247.5t145.5 208.5q172 162 444 162q133 0 273.5 -38.5t251.5 -96.5l-158 -895h-393l10 59l-4 2q-87 -84 -230 -84q-170 0 -280.5 121.5t-110.5 313.5zM410 1159l45 256q80 76 190 76q72 0 176 -49q81 -38 113 -44q16 -3 33 -3q38 0 75.5 16t57 31 t53.5 45l4 -2l-45 -256q-80 -76 -190 -76q-73 0 -177 49q-4 2 -18 8.5t-18 8.5t-15.5 7t-16 6.5t-14.5 5.5t-16 5t-14.5 3t-16.5 2.5t-16 0.5q-38 0 -75.5 -16t-57 -31t-53.5 -45zM461 457q0 -77 40 -121.5t111 -44.5q50 0 97 20l65 375q-48 23 -106 23q-98 0 -152 -74 q-55 -73 -55 -178z" />
<glyph unicode="&#xe4;" horiz-adv-x="1261" d="M51 412q0 127 51.5 247.5t145.5 208.5q172 162 444 162q133 0 273.5 -38.5t251.5 -96.5l-158 -895h-393l10 59l-4 2q-87 -84 -230 -84q-170 0 -280.5 121.5t-110.5 313.5zM377 1313q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5 t-46.5 116.5zM461 457q0 -77 40 -121.5t111 -44.5q50 0 97 20l65 375q-48 23 -106 23q-98 0 -152 -74q-55 -73 -55 -178zM844 1313q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1261" d="M51 412q0 127 51.5 247.5t145.5 208.5q172 162 444 162q133 0 273.5 -38.5t251.5 -96.5l-158 -895h-393l10 59l-4 2q-87 -84 -230 -84q-170 0 -280.5 121.5t-110.5 313.5zM461 457q0 -77 40 -121.5t111 -44.5q50 0 97 20l65 375q-48 23 -106 23q-98 0 -152 -74 q-55 -73 -55 -178zM512 1339q0 118 84.5 204.5t210.5 86.5q110 0 187 -67t77 -179q0 -118 -84.5 -204t-210.5 -86q-110 0 -187 66.5t-77 178.5zM684 1346q0 -41 26 -71t70 -30q51 0 85 38.5t34 94.5q0 41 -26 71t-70 30q-51 0 -85 -38.5t-34 -94.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1814" d="M51 408q0 125 49 247t144 211q174 164 469 164q175 0 372 -57q105 57 234 57q86 0 165 -26t144.5 -77t104.5 -136t39 -193q0 -88 -25 -186h-637q15 -76 67 -110t142 -34q74 0 171.5 20t170.5 50l-53 -307q-59 -22 -154 -38t-180 -16q-126 0 -243 39t-197 115 q-169 -154 -369 -154q-182 0 -298 116t-116 315zM461 459q0 -76 40.5 -122t108.5 -46t115 47q-8 36 -8 88q0 51 10 109l31 167q-42 11 -80 11q-107 0 -162 -76q-55 -77 -55 -178zM1130 621h277q-4 58 -39.5 90t-87.5 32q-103 0 -150 -122z" />
<glyph unicode="&#xe7;" horiz-adv-x="1071" d="M47 438q0 122 53 244t158 207q85 68 199 104.5t241 36.5q175 0 349 -59l-64 -357q-162 86 -301 86q-102 0 -158 -53q-32 -29 -49.5 -74t-17.5 -92q0 -76 51.5 -124t157.5 -48q139 0 297 62l-62 -348q-123 -39 -285 -43l-26 -58q139 -45 139 -174q0 -90 -67 -147.5 t-170 -57.5q-111 0 -205 60l67 131q70 -51 133 -51q30 0 50 12t20 35q0 63 -162 90l86 170q-88 14 -163.5 47.5t-137.5 87t-97.5 134t-35.5 179.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1163" d="M49 432q0 126 51.5 246.5t147.5 206.5q161 145 395 145q94 0 178 -26.5t151.5 -78t107.5 -136t40 -191.5q0 -92 -24 -186h-645q29 -144 219 -144q73 0 167 19.5t173 50.5l-54 -307q-66 -24 -164 -39t-186 -15q-116 0 -214.5 27.5t-176 81.5t-122 142.5t-44.5 203.5z M477 621h277q-3 59 -38 90.5t-87 31.5q-100 0 -152 -122zM483 1489h310l92 -336h-250z" />
<glyph unicode="&#xe9;" horiz-adv-x="1163" d="M49 432q0 126 51.5 246.5t147.5 206.5q161 145 395 145q94 0 178 -26.5t151.5 -78t107.5 -136t40 -191.5q0 -92 -24 -186h-645q29 -144 219 -144q73 0 167 19.5t173 50.5l-54 -307q-66 -24 -164 -39t-186 -15q-116 0 -214.5 27.5t-176 81.5t-122 142.5t-44.5 203.5z M477 621h277q-3 59 -38 90.5t-87 31.5q-100 0 -152 -122zM502 1153l233 336h350l-307 -336h-276z" />
<glyph unicode="&#xea;" horiz-adv-x="1163" d="M49 432q0 126 51.5 246.5t147.5 206.5q161 145 395 145q94 0 178 -26.5t151.5 -78t107.5 -136t40 -191.5q0 -92 -24 -186h-645q29 -144 219 -144q73 0 167 19.5t173 50.5l-54 -307q-66 -24 -164 -39t-186 -15q-116 0 -214.5 27.5t-176 81.5t-122 142.5t-44.5 203.5z M307 1153l326 336h276l168 -336h-241l-97 170l-174 -170h-258zM477 621h277q-3 59 -38 90.5t-87 31.5q-100 0 -152 -122z" />
<glyph unicode="&#xeb;" horiz-adv-x="1163" d="M49 432q0 126 51.5 246.5t147.5 206.5q161 145 395 145q94 0 178 -26.5t151.5 -78t107.5 -136t40 -191.5q0 -92 -24 -186h-645q29 -144 219 -144q73 0 167 19.5t173 50.5l-54 -307q-66 -24 -164 -39t-186 -15q-116 0 -214.5 27.5t-176 81.5t-122 142.5t-44.5 203.5z M313 1313q0 74 54.5 127t128.5 53q70 0 116.5 -48t46.5 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5zM477 621h277q-3 59 -38 90.5t-87 31.5q-100 0 -152 -122zM780 1313q0 74 54.5 127t128.5 53q70 0 116.5 -48t46.5 -116q0 -74 -54 -127t-128 -53 q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#xec;" horiz-adv-x="624" d="M18 0l179 1008h405l-178 -1008h-406zM217 1489h309l92 -336h-249z" />
<glyph unicode="&#xed;" horiz-adv-x="624" d="M18 0l179 1008h405l-178 -1008h-406zM236 1153l233 336h350l-307 -336h-276z" />
<glyph unicode="&#xee;" horiz-adv-x="624" d="M18 0l179 1008h405l-178 -1008h-406zM41 1153l326 336h276l168 -336h-242l-96 170l-174 -170h-258z" />
<glyph unicode="&#xef;" horiz-adv-x="624" d="M18 0l179 1008h405l-178 -1008h-406zM47 1313q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5zM514 1313q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1228" d="M49 432q0 105 40.5 207.5t123.5 183.5q77 74 174.5 112t202.5 38q62 0 92 -10l2 4q-25 52 -74 110l-338 -86l-6 230l160 41l-104 81l284 205q120 -88 213 -186l252 63l6 -229l-102 -27q184 -277 184 -577q0 -278 -164 -445q-80 -82 -192 -126t-242 -44q-144 0 -259 52.5 t-184 157t-69 245.5zM449 459q0 -67 35.5 -110.5t101.5 -43.5q80 0 133 70q47 61 47 147q0 67 -35.5 110.5t-101.5 43.5q-83 0 -133 -70q-47 -61 -47 -147z" />
<glyph unicode="&#xf1;" horiz-adv-x="1257" d="M18 0l179 1008h397l-14 -76l4 -2q129 100 274 100q147 0 234.5 -89t87.5 -228q0 -41 -11 -109l-106 -604h-406l101 569q4 20 4 39q0 40 -24 64t-68 24q-69 0 -131 -47l-115 -649h-406zM397 1159l45 256q80 76 191 76q72 0 176 -49q4 -2 18 -8.5t18 -8.5t15.5 -7t16 -6.5 t14.5 -5.5t16 -5t14.5 -3t16.5 -2.5t16 -0.5q38 0 75.5 16t58 31.5t53.5 44.5l4 -2l-45 -256q-80 -76 -191 -76q-72 0 -176 49q-4 2 -18 8.5t-18 8.5t-15.5 7t-16 6.5t-14.5 5.5t-16 5t-14.5 3t-16.5 2.5t-16 0.5q-38 0 -75.5 -16t-58 -31.5t-53.5 -44.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1253" d="M51 446q0 113 44 223.5t128 192.5q171 168 441 168q150 0 269.5 -54.5t191 -162t71.5 -252.5q0 -113 -44 -223.5t-128 -192.5q-171 -168 -440 -168q-150 0 -270 54.5t-191.5 162.5t-71.5 252zM453 475q0 -72 40.5 -116t110.5 -44q81 0 133 60q58 68 58 157 q0 71 -41 115.5t-111 44.5q-82 0 -133 -59q-57 -67 -57 -158zM532 1489h310l92 -336h-250z" />
<glyph unicode="&#xf3;" horiz-adv-x="1253" d="M51 446q0 113 44 223.5t128 192.5q171 168 441 168q150 0 269.5 -54.5t191 -162t71.5 -252.5q0 -113 -44 -223.5t-128 -192.5q-171 -168 -440 -168q-150 0 -270 54.5t-191.5 162.5t-71.5 252zM453 475q0 -72 40.5 -116t110.5 -44q81 0 133 60q58 68 58 157 q0 71 -41 115.5t-111 44.5q-82 0 -133 -59q-57 -67 -57 -158zM551 1153l233 336h351l-308 -336h-276z" />
<glyph unicode="&#xf4;" horiz-adv-x="1253" d="M51 446q0 113 44 223.5t128 192.5q171 168 441 168q150 0 269.5 -54.5t191 -162t71.5 -252.5q0 -113 -44 -223.5t-128 -192.5q-171 -168 -440 -168q-150 0 -270 54.5t-191.5 162.5t-71.5 252zM356 1153l326 336h276l168 -336h-241l-97 170l-174 -170h-258zM453 475 q0 -72 40.5 -116t110.5 -44q81 0 133 60q58 68 58 157q0 71 -41 115.5t-111 44.5q-82 0 -133 -59q-57 -67 -57 -158z" />
<glyph unicode="&#xf5;" horiz-adv-x="1253" d="M51 446q0 113 44 223.5t128 192.5q171 168 441 168q150 0 269.5 -54.5t191 -162t71.5 -252.5q0 -113 -44 -223.5t-128 -192.5q-171 -168 -440 -168q-150 0 -270 54.5t-191.5 162.5t-71.5 252zM395 1159l45 256q80 76 191 76q72 0 176 -49q4 -2 18 -8.5t18 -8.5t15.5 -7 t16 -6.5t14.5 -5.5t16 -5t14.5 -3t16.5 -2.5t16 -0.5q38 0 75.5 16t58 31.5t53.5 44.5l4 -2l-45 -256q-80 -76 -191 -76q-72 0 -176 49q-4 2 -18 8.5t-18 8.5t-15.5 7t-16 6.5t-14.5 5.5t-16 5t-14.5 3t-16.5 2.5t-16 0.5q-38 0 -75.5 -16t-58 -31.5t-53.5 -44.5zM453 475 q0 -72 40.5 -116t110.5 -44q81 0 133 60q58 68 58 157q0 71 -41 115.5t-111 44.5q-82 0 -133 -59q-57 -67 -57 -158z" />
<glyph unicode="&#xf6;" horiz-adv-x="1253" d="M51 446q0 113 44 223.5t128 192.5q171 168 441 168q150 0 269.5 -54.5t191 -162t71.5 -252.5q0 -113 -44 -223.5t-128 -192.5q-171 -168 -440 -168q-150 0 -270 54.5t-191.5 162.5t-71.5 252zM362 1313q0 74 54.5 127t128.5 53q70 0 117 -48t47 -116q0 -74 -54.5 -127 t-128.5 -53q-71 0 -117.5 47.5t-46.5 116.5zM453 475q0 -72 40.5 -116t110.5 -44q81 0 133 60q58 68 58 157q0 71 -41 115.5t-111 44.5q-82 0 -133 -59q-57 -67 -57 -158zM829 1313q0 74 54.5 127t128.5 53q70 0 117 -48t47 -116q0 -74 -54.5 -127t-128.5 -53 q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1239" d="M125 604l49 275h1020l-49 -275h-1020zM395 326q0 82 59 139t142 57q78 0 130 -52t52 -128q0 -83 -59 -140t-141 -57q-78 0 -130.5 52.5t-52.5 128.5zM541 1141q0 83 59 139.5t141 56.5q78 0 130.5 -52.5t52.5 -127.5q0 -82 -59 -139t-142 -57q-77 0 -129.5 52.5 t-52.5 127.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1253" d="M51 446q0 113 44 223.5t128 192.5q171 168 441 168q187 0 323 -84l111 107l92 -92l-103 -99q109 -122 109 -301q0 -113 -44 -223.5t-128 -192.5q-171 -168 -440 -168q-188 0 -324 84l-110 -106l-93 92l103 98q-109 122 -109 301zM453 475q0 -29 4 -43l254 246 q-28 14 -68 14q-82 0 -133 -59q-57 -67 -57 -158zM537 330q30 -15 67 -15q81 0 133 60q58 68 58 157q0 29 -4 43z" />
<glyph unicode="&#xf9;" horiz-adv-x="1259" d="M78 297q0 44 10 106l107 605h403l-100 -570q-4 -24 -4 -41q0 -40 25 -64t71 -24q64 0 127 53l114 646h404l-178 -1008h-398l15 80l-4 2q-117 -105 -269 -105q-148 0 -235.5 89.5t-87.5 230.5zM535 1489h309l92 -336h-250z" />
<glyph unicode="&#xfa;" horiz-adv-x="1259" d="M78 297q0 44 10 106l107 605h403l-100 -570q-4 -24 -4 -41q0 -40 25 -64t71 -24q64 0 127 53l114 646h404l-178 -1008h-398l15 80l-4 2q-117 -105 -269 -105q-148 0 -235.5 89.5t-87.5 230.5zM553 1153l233 336h351l-308 -336h-276z" />
<glyph unicode="&#xfb;" horiz-adv-x="1259" d="M78 297q0 44 10 106l107 605h403l-100 -570q-4 -24 -4 -41q0 -40 25 -64t71 -24q64 0 127 53l114 646h404l-178 -1008h-398l15 80l-4 2q-117 -105 -269 -105q-148 0 -235.5 89.5t-87.5 230.5zM358 1153l326 336h277l167 -336h-241l-96 170l-175 -170h-258z" />
<glyph unicode="&#xfc;" horiz-adv-x="1259" d="M78 297q0 44 10 106l107 605h403l-100 -570q-4 -24 -4 -41q0 -40 25 -64t71 -24q64 0 127 53l114 646h404l-178 -1008h-398l15 80l-4 2q-117 -105 -269 -105q-148 0 -235.5 89.5t-87.5 230.5zM365 1313q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54.5 -127 t-128.5 -53q-71 0 -117 47.5t-46 116.5zM831 1313q0 74 54.5 127t128.5 53q70 0 117 -48t47 -116q0 -74 -54.5 -127t-128.5 -53q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1138" d="M-74 -451l377 574l-240 885h406l43 -226q29 -154 47 -272h4q46 84 150 268l133 230h395l-897 -1459h-418zM508 1153l233 336h351l-308 -336h-276z" />
<glyph unicode="&#xfe;" horiz-adv-x="1255" d="M-61 -451l356 2016h405l-106 -600l4 -2q79 67 211 67q170 0 279.5 -125.5t109.5 -316.5q0 -124 -48 -243.5t-134 -203.5q-167 -164 -438 -164q-74 0 -152 19l-4 -2l-78 -445h-405zM479 313q33 -20 105 -20q98 0 153 74q51 68 51 165q0 80 -41 130.5t-114 50.5 q-40 0 -88 -25z" />
<glyph unicode="&#xff;" horiz-adv-x="1138" d="M-74 -451l377 574l-240 885h406l43 -226q29 -154 47 -272h4q46 84 150 268l133 230h395l-897 -1459h-418zM319 1313q0 74 54.5 127t128.5 53q70 0 117 -48t47 -116q0 -74 -54.5 -127t-128.5 -53q-71 0 -117.5 47.5t-46.5 116.5zM786 1313q0 74 54.5 127t128.5 53 q70 0 117 -48t47 -116q0 -74 -54.5 -127t-128.5 -53q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1902" d="M102 659q0 185 76.5 353t227.5 284q245 187 630 187h953l-64 -357h-491l-33 -188h407l-59 -336h-408l-43 -246h517l-64 -356h-829q-135 0 -253 21.5t-223.5 71.5t-180.5 125t-119 187.5t-44 253.5zM530 684q0 -307 347 -328l135 770q-240 0 -355 -118 q-127 -130 -127 -324z" />
<glyph unicode="&#x153;" horiz-adv-x="1880" d="M51 440q0 111 42.5 220t125.5 194t196 130.5t242 45.5q108 0 203.5 -33t157.5 -92h4q73 59 167 92t193 33q86 0 165.5 -26t145 -77t105 -136t39.5 -193q0 -88 -25 -186h-636q15 -76 66.5 -110t143.5 -34q158 0 340 70l-53 -307q-61 -23 -157.5 -38.5t-178.5 -15.5 q-230 0 -391 119h-4q-153 -119 -371 -119q-109 0 -203.5 31.5t-165 89.5t-111 146.5t-40.5 195.5zM453 475q0 -69 39.5 -114.5t105.5 -45.5q74 0 129 64t55 153q0 69 -39.5 114.5t-105.5 45.5q-75 0 -129 -63q-55 -64 -55 -154zM1196 621h277q-4 58 -39.5 90t-87.5 32 q-103 0 -150 -122z" />
<glyph unicode="&#x178;" horiz-adv-x="1472" d="M135 1483h475l88 -193q65 -144 119 -270h4q152 193 213 268l156 195h489l-700 -809l-119 -674h-422l117 668zM561 1788q0 74 54 127t128 53q70 0 117 -48t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5zM1028 1788q0 74 54 127t128 53q70 0 117 -48 t47 -116q0 -74 -54 -127t-128 -53q-71 0 -117.5 47.5t-46.5 116.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1126" d="M293 1153l325 336h277l168 -336h-242l-96 170l-174 -170h-258z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1126" d="M332 1159l45 256q80 76 190 76q72 0 176 -49q81 -38 113 -44q16 -3 33 -3q38 0 75.5 16t57 31t53.5 45l4 -2l-45 -256q-80 -76 -190 -76q-72 0 -176 49q-81 38 -113 44q-16 3 -33 3q-38 0 -75.5 -16t-57 -31t-53.5 -45z" />
<glyph unicode="&#x2000;" horiz-adv-x="1052" />
<glyph unicode="&#x2001;" horiz-adv-x="2105" />
<glyph unicode="&#x2002;" horiz-adv-x="1052" />
<glyph unicode="&#x2003;" horiz-adv-x="2105" />
<glyph unicode="&#x2004;" horiz-adv-x="701" />
<glyph unicode="&#x2005;" horiz-adv-x="526" />
<glyph unicode="&#x2006;" horiz-adv-x="350" />
<glyph unicode="&#x2007;" horiz-adv-x="350" />
<glyph unicode="&#x2008;" horiz-adv-x="263" />
<glyph unicode="&#x2009;" horiz-adv-x="421" />
<glyph unicode="&#x200a;" horiz-adv-x="116" />
<glyph unicode="&#x2010;" d="M51 412l55 311h515l-56 -311h-514z" />
<glyph unicode="&#x2011;" d="M51 412l55 311h515l-56 -311h-514z" />
<glyph unicode="&#x2012;" d="M51 412l55 311h515l-56 -311h-514z" />
<glyph unicode="&#x2013;" horiz-adv-x="980" d="M76 428l49 283h799l-50 -283h-798z" />
<glyph unicode="&#x2014;" horiz-adv-x="1579" d="M47 453l41 233h1462l-41 -233h-1462z" />
<glyph unicode="&#x2018;" horiz-adv-x="538" d="M86 926l315 594h254l-229 -594h-340z" />
<glyph unicode="&#x2019;" horiz-adv-x="538" d="M129 926l229 594h340l-315 -594h-254z" />
<glyph unicode="&#x201a;" horiz-adv-x="538" d="M-86 -258l229 594h340l-315 -594h-254z" />
<glyph unicode="&#x201c;" horiz-adv-x="976" d="M86 926l315 594h254l-229 -594h-340zM526 926l316 594h254l-230 -594h-340z" />
<glyph unicode="&#x201d;" horiz-adv-x="976" d="M129 926l229 594h340l-315 -594h-254zM567 926l230 594h340l-316 -594h-254z" />
<glyph unicode="&#x201e;" horiz-adv-x="976" d="M-86 -258l229 594h340l-315 -594h-254zM354 -258l230 594h340l-316 -594h-254z" />
<glyph unicode="&#x2022;" horiz-adv-x="751" d="M117 608q0 116 80.5 196.5t195.5 80.5t196 -80.5t81 -196.5q0 -115 -80.5 -195.5t-196.5 -80.5q-115 0 -195.5 80t-80.5 196z" />
<glyph unicode="&#x2026;" horiz-adv-x="1851" d="M53 186q0 96 69 163t165 67q90 0 150.5 -61t60.5 -148q0 -96 -69 -163t-165 -67q-91 0 -151 61t-60 148zM649 186q0 96 69 163t165 67q90 0 150.5 -61t60.5 -148q0 -96 -69 -163t-165 -67q-91 0 -151 61t-60 148zM1243 186q0 96 69 163t165 67q90 0 150.5 -61t60.5 -148 q0 -96 -69 -163t-165 -67q-91 0 -151 61t-60 148z" />
<glyph unicode="&#x202f;" horiz-adv-x="421" />
<glyph unicode="&#x2039;" horiz-adv-x="706" d="M35 520l403 432h330l-420 -448l236 -449h-308z" />
<glyph unicode="&#x203a;" horiz-adv-x="706" d="M-66 55l420 449l-235 448h307l242 -465l-404 -432h-330z" />
<glyph unicode="&#x205f;" horiz-adv-x="526" />
<glyph unicode="&#x20ac;" horiz-adv-x="1259" d="M-2 449l45 249h152q3 38 10 88h-146l45 250h179q106 220 294.5 344.5t430.5 124.5q199 0 346 -65l-68 -379q-129 78 -270 78q-147 0 -256 -103h237l-45 -250h-319q-9 -48 -12 -88h315l-45 -249h-199q88 -105 264 -105q119 0 244 55l-67 -385q-129 -37 -265 -37 q-243 0 -419.5 127.5t-233.5 344.5h-217z" />
<glyph unicode="&#x2122;" horiz-adv-x="1366" d="M182 1309v174h514v-174h-157v-420h-199v420h-158zM766 889v594h184l137 -189l134 189h186v-594h-197v125q0 78 4 164l-4 2q-49 -78 -80 -119l-47 -66l-47 66q-31 41 -80 119l-4 -2q4 -86 4 -164v-125h-190z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x2a;" u2="&#x2026;" k="246" />
<hkern u1="&#x2a;" u2="&#x2e;" k="246" />
<hkern u1="&#x2a;" u2="&#x2c;" k="246" />
<hkern u1="&#x2c;" u2="V" k="133" />
<hkern u1="&#x2c;" u2="&#x39;" k="143" />
<hkern u1="&#x2c;" u2="&#x38;" k="37" />
<hkern u1="&#x2c;" u2="&#x37;" k="72" />
<hkern u1="&#x2c;" u2="&#x2a;" k="246" />
<hkern u1="&#x2d;" u2="x" k="88" />
<hkern u1="&#x2d;" u2="X" k="72" />
<hkern u1="&#x2d;" u2="V" k="121" />
<hkern u1="&#x2d;" u2="&#x32;" k="115" />
<hkern u1="&#x2e;" u2="V" k="133" />
<hkern u1="&#x2e;" u2="&#x39;" k="143" />
<hkern u1="&#x2e;" u2="&#x38;" k="37" />
<hkern u1="&#x2e;" u2="&#x37;" k="72" />
<hkern u1="&#x2e;" u2="&#x2a;" k="246" />
<hkern u1="&#x2f;" u2="&#x153;" k="166" />
<hkern u1="&#x2f;" u2="&#xf8;" k="166" />
<hkern u1="&#x2f;" u2="&#xf6;" k="166" />
<hkern u1="&#x2f;" u2="&#xf5;" k="166" />
<hkern u1="&#x2f;" u2="&#xf4;" k="166" />
<hkern u1="&#x2f;" u2="&#xf3;" k="166" />
<hkern u1="&#x2f;" u2="&#xf2;" k="166" />
<hkern u1="&#x2f;" u2="&#xf0;" k="166" />
<hkern u1="&#x2f;" u2="&#xeb;" k="166" />
<hkern u1="&#x2f;" u2="&#xea;" k="166" />
<hkern u1="&#x2f;" u2="&#xe9;" k="166" />
<hkern u1="&#x2f;" u2="&#xe8;" k="166" />
<hkern u1="&#x2f;" u2="&#xe7;" k="166" />
<hkern u1="&#x2f;" u2="&#xe6;" k="166" />
<hkern u1="&#x2f;" u2="&#xe5;" k="166" />
<hkern u1="&#x2f;" u2="&#xe4;" k="166" />
<hkern u1="&#x2f;" u2="&#xe3;" k="166" />
<hkern u1="&#x2f;" u2="&#xe2;" k="166" />
<hkern u1="&#x2f;" u2="&#xe1;" k="166" />
<hkern u1="&#x2f;" u2="&#xe0;" k="166" />
<hkern u1="&#x2f;" u2="&#xc5;" k="143" />
<hkern u1="&#x2f;" u2="&#xc4;" k="143" />
<hkern u1="&#x2f;" u2="&#xc3;" k="143" />
<hkern u1="&#x2f;" u2="&#xc2;" k="143" />
<hkern u1="&#x2f;" u2="&#xc1;" k="143" />
<hkern u1="&#x2f;" u2="&#xc0;" k="143" />
<hkern u1="&#x2f;" u2="q" k="166" />
<hkern u1="&#x2f;" u2="o" k="166" />
<hkern u1="&#x2f;" u2="g" k="166" />
<hkern u1="&#x2f;" u2="e" k="166" />
<hkern u1="&#x2f;" u2="d" k="166" />
<hkern u1="&#x2f;" u2="c" k="166" />
<hkern u1="&#x2f;" u2="a" k="166" />
<hkern u1="&#x2f;" u2="A" k="143" />
<hkern u1="&#x2f;" u2="&#x34;" k="143" />
<hkern u1="&#x30;" u2="_" k="82" />
<hkern u1="&#x36;" u2="&#x37;" k="10" />
<hkern u1="&#x36;" u2="&#x31;" k="18" />
<hkern u1="&#x37;" u2="&#x2026;" k="215" />
<hkern u1="&#x37;" u2="&#x34;" k="102" />
<hkern u1="&#x37;" u2="&#x2f;" k="164" />
<hkern u1="&#x37;" u2="&#x2e;" k="215" />
<hkern u1="&#x37;" u2="&#x2c;" k="215" />
<hkern u1="&#x38;" u2="&#x2026;" k="6" />
<hkern u1="&#x38;" u2="&#x2e;" k="6" />
<hkern u1="&#x38;" u2="&#x2c;" k="6" />
<hkern u1="&#x39;" u2="&#x2026;" k="72" />
<hkern u1="&#x39;" u2="&#x2e;" k="72" />
<hkern u1="&#x39;" u2="&#x2c;" k="72" />
<hkern u1="A" u2="x" k="-72" />
<hkern u1="A" u2="\" k="143" />
<hkern u1="A" u2="V" k="160" />
<hkern u1="B" u2="&#x203a;" k="16" />
<hkern u1="B" u2="&#x201c;" k="10" />
<hkern u1="B" u2="&#x2018;" k="10" />
<hkern u1="B" u2="&#x178;" k="106" />
<hkern u1="B" u2="&#xff;" k="10" />
<hkern u1="B" u2="&#xfd;" k="10" />
<hkern u1="B" u2="&#xdd;" k="106" />
<hkern u1="B" u2="&#xc6;" k="16" />
<hkern u1="B" u2="&#xc5;" k="4" />
<hkern u1="B" u2="&#xc4;" k="4" />
<hkern u1="B" u2="&#xc3;" k="4" />
<hkern u1="B" u2="&#xc2;" k="4" />
<hkern u1="B" u2="&#xc1;" k="4" />
<hkern u1="B" u2="&#xc0;" k="4" />
<hkern u1="B" u2="&#xbb;" k="16" />
<hkern u1="B" u2="z" k="25" />
<hkern u1="B" u2="y" k="10" />
<hkern u1="B" u2="v" k="10" />
<hkern u1="B" u2="Y" k="106" />
<hkern u1="B" u2="W" k="63" />
<hkern u1="B" u2="V" k="43" />
<hkern u1="B" u2="A" k="4" />
<hkern u1="D" u2="x" k="20" />
<hkern u1="D" u2="_" k="102" />
<hkern u1="D" u2="X" k="74" />
<hkern u1="D" u2="V" k="80" />
<hkern u1="E" u2="x" k="-23" />
<hkern u1="F" u2="&#x203a;" k="49" />
<hkern u1="F" u2="&#x2039;" k="25" />
<hkern u1="F" u2="&#x2026;" k="195" />
<hkern u1="F" u2="&#x201e;" k="174" />
<hkern u1="F" u2="&#x201a;" k="174" />
<hkern u1="F" u2="&#x153;" k="41" />
<hkern u1="F" u2="&#xff;" k="43" />
<hkern u1="F" u2="&#xfd;" k="43" />
<hkern u1="F" u2="&#xfc;" k="55" />
<hkern u1="F" u2="&#xfb;" k="55" />
<hkern u1="F" u2="&#xfa;" k="55" />
<hkern u1="F" u2="&#xf9;" k="55" />
<hkern u1="F" u2="&#xf8;" k="41" />
<hkern u1="F" u2="&#xf6;" k="41" />
<hkern u1="F" u2="&#xf5;" k="41" />
<hkern u1="F" u2="&#xf4;" k="41" />
<hkern u1="F" u2="&#xf3;" k="41" />
<hkern u1="F" u2="&#xf2;" k="41" />
<hkern u1="F" u2="&#xf1;" k="23" />
<hkern u1="F" u2="&#xf0;" k="41" />
<hkern u1="F" u2="&#xef;" k="-96" />
<hkern u1="F" u2="&#xee;" k="-43" />
<hkern u1="F" u2="&#xeb;" k="41" />
<hkern u1="F" u2="&#xea;" k="41" />
<hkern u1="F" u2="&#xe9;" k="41" />
<hkern u1="F" u2="&#xe8;" k="41" />
<hkern u1="F" u2="&#xe7;" k="41" />
<hkern u1="F" u2="&#xe6;" k="41" />
<hkern u1="F" u2="&#xe5;" k="41" />
<hkern u1="F" u2="&#xe4;" k="41" />
<hkern u1="F" u2="&#xe3;" k="41" />
<hkern u1="F" u2="&#xe2;" k="41" />
<hkern u1="F" u2="&#xe1;" k="41" />
<hkern u1="F" u2="&#xe0;" k="41" />
<hkern u1="F" u2="&#xc5;" k="154" />
<hkern u1="F" u2="&#xc4;" k="154" />
<hkern u1="F" u2="&#xc3;" k="154" />
<hkern u1="F" u2="&#xc2;" k="154" />
<hkern u1="F" u2="&#xc1;" k="154" />
<hkern u1="F" u2="&#xc0;" k="154" />
<hkern u1="F" u2="&#xbb;" k="49" />
<hkern u1="F" u2="&#xab;" k="25" />
<hkern u1="F" u2="z" k="55" />
<hkern u1="F" u2="y" k="43" />
<hkern u1="F" u2="x" k="84" />
<hkern u1="F" u2="w" k="33" />
<hkern u1="F" u2="v" k="43" />
<hkern u1="F" u2="u" k="55" />
<hkern u1="F" u2="t" k="31" />
<hkern u1="F" u2="s" k="47" />
<hkern u1="F" u2="r" k="23" />
<hkern u1="F" u2="q" k="41" />
<hkern u1="F" u2="p" k="23" />
<hkern u1="F" u2="o" k="41" />
<hkern u1="F" u2="n" k="23" />
<hkern u1="F" u2="m" k="23" />
<hkern u1="F" u2="g" k="41" />
<hkern u1="F" u2="e" k="41" />
<hkern u1="F" u2="d" k="41" />
<hkern u1="F" u2="c" k="41" />
<hkern u1="F" u2="a" k="41" />
<hkern u1="F" u2="A" k="154" />
<hkern u1="F" u2="&#x2e;" k="195" />
<hkern u1="F" u2="&#x2c;" k="195" />
<hkern u1="K" u2="&#xf8;" k="41" />
<hkern u1="K" u2="x" k="-96" />
<hkern u1="L" u2="&#x2019;" k="258" />
<hkern u1="L" u2="x" k="-57" />
<hkern u1="L" u2="V" k="244" />
<hkern u1="O" u2="x" k="20" />
<hkern u1="O" u2="_" k="102" />
<hkern u1="O" u2="X" k="74" />
<hkern u1="O" u2="V" k="80" />
<hkern u1="P" u2="&#x2039;" k="25" />
<hkern u1="P" u2="&#x2026;" k="215" />
<hkern u1="P" u2="&#x201e;" k="205" />
<hkern u1="P" u2="&#x201a;" k="205" />
<hkern u1="P" u2="&#x153;" k="41" />
<hkern u1="P" u2="&#xff;" k="-41" />
<hkern u1="P" u2="&#xfd;" k="-41" />
<hkern u1="P" u2="&#xfc;" k="4" />
<hkern u1="P" u2="&#xfb;" k="4" />
<hkern u1="P" u2="&#xfa;" k="4" />
<hkern u1="P" u2="&#xf9;" k="4" />
<hkern u1="P" u2="&#xf8;" k="41" />
<hkern u1="P" u2="&#xf6;" k="41" />
<hkern u1="P" u2="&#xf5;" k="41" />
<hkern u1="P" u2="&#xf4;" k="41" />
<hkern u1="P" u2="&#xf3;" k="41" />
<hkern u1="P" u2="&#xf2;" k="41" />
<hkern u1="P" u2="&#xf1;" k="10" />
<hkern u1="P" u2="&#xf0;" k="41" />
<hkern u1="P" u2="&#xef;" k="-18" />
<hkern u1="P" u2="&#xee;" k="-51" />
<hkern u1="P" u2="&#xeb;" k="41" />
<hkern u1="P" u2="&#xea;" k="41" />
<hkern u1="P" u2="&#xe9;" k="41" />
<hkern u1="P" u2="&#xe8;" k="41" />
<hkern u1="P" u2="&#xe7;" k="41" />
<hkern u1="P" u2="&#xe6;" k="41" />
<hkern u1="P" u2="&#xe5;" k="41" />
<hkern u1="P" u2="&#xe4;" k="41" />
<hkern u1="P" u2="&#xe3;" k="41" />
<hkern u1="P" u2="&#xe2;" k="41" />
<hkern u1="P" u2="&#xe1;" k="41" />
<hkern u1="P" u2="&#xe0;" k="41" />
<hkern u1="P" u2="&#xc5;" k="98" />
<hkern u1="P" u2="&#xc4;" k="98" />
<hkern u1="P" u2="&#xc3;" k="98" />
<hkern u1="P" u2="&#xc2;" k="98" />
<hkern u1="P" u2="&#xc1;" k="98" />
<hkern u1="P" u2="&#xc0;" k="98" />
<hkern u1="P" u2="&#xab;" k="25" />
<hkern u1="P" u2="z" k="8" />
<hkern u1="P" u2="y" k="-41" />
<hkern u1="P" u2="x" k="-41" />
<hkern u1="P" u2="w" k="-31" />
<hkern u1="P" u2="v" k="-41" />
<hkern u1="P" u2="u" k="4" />
<hkern u1="P" u2="s" k="20" />
<hkern u1="P" u2="r" k="10" />
<hkern u1="P" u2="q" k="41" />
<hkern u1="P" u2="p" k="10" />
<hkern u1="P" u2="o" k="41" />
<hkern u1="P" u2="n" k="10" />
<hkern u1="P" u2="m" k="10" />
<hkern u1="P" u2="g" k="41" />
<hkern u1="P" u2="e" k="41" />
<hkern u1="P" u2="d" k="41" />
<hkern u1="P" u2="c" k="41" />
<hkern u1="P" u2="a" k="41" />
<hkern u1="P" u2="W" k="45" />
<hkern u1="P" u2="J" k="84" />
<hkern u1="P" u2="A" k="98" />
<hkern u1="P" u2="&#x2e;" k="215" />
<hkern u1="P" u2="&#x2c;" k="215" />
<hkern u1="Q" u2="x" k="20" />
<hkern u1="Q" u2="_" k="102" />
<hkern u1="Q" u2="X" k="74" />
<hkern u1="Q" u2="V" k="80" />
<hkern u1="R" u2="V" k="51" />
<hkern u1="S" u2="_" k="8" />
<hkern u1="S" u2="V" k="41" />
<hkern u1="T" u2="&#xff;" k="66" />
<hkern u1="T" u2="&#xfc;" k="133" />
<hkern u1="T" u2="&#xf6;" k="131" />
<hkern u1="T" u2="&#xf1;" k="188" />
<hkern u1="T" u2="&#xee;" k="-102" />
<hkern u1="T" u2="&#xeb;" k="119" />
<hkern u1="T" u2="&#xea;" k="139" />
<hkern u1="T" u2="&#xe4;" k="170" />
<hkern u1="T" u2="&#xe3;" k="178" />
<hkern u1="T" u2="&#xe2;" k="215" />
<hkern u1="T" u2="x" k="154" />
<hkern u1="T" u2="_" k="139" />
<hkern u1="T" u2="&#x2f;" k="195" />
<hkern u1="U" u2="x" k="10" />
<hkern u1="V" u2="&#x203a;" k="27" />
<hkern u1="V" u2="&#x2039;" k="106" />
<hkern u1="V" u2="&#x2026;" k="82" />
<hkern u1="V" u2="&#x2014;" k="96" />
<hkern u1="V" u2="&#x2013;" k="96" />
<hkern u1="V" u2="&#x178;" k="-66" />
<hkern u1="V" u2="&#x153;" k="131" />
<hkern u1="V" u2="&#x152;" k="10" />
<hkern u1="V" u2="&#xff;" k="35" />
<hkern u1="V" u2="&#xfd;" k="35" />
<hkern u1="V" u2="&#xfc;" k="106" />
<hkern u1="V" u2="&#xfb;" k="106" />
<hkern u1="V" u2="&#xfa;" k="106" />
<hkern u1="V" u2="&#xf9;" k="106" />
<hkern u1="V" u2="&#xf8;" k="131" />
<hkern u1="V" u2="&#xf6;" k="131" />
<hkern u1="V" u2="&#xf5;" k="131" />
<hkern u1="V" u2="&#xf4;" k="131" />
<hkern u1="V" u2="&#xf3;" k="131" />
<hkern u1="V" u2="&#xf2;" k="131" />
<hkern u1="V" u2="&#xf1;" k="76" />
<hkern u1="V" u2="&#xf0;" k="131" />
<hkern u1="V" u2="&#xeb;" k="131" />
<hkern u1="V" u2="&#xea;" k="131" />
<hkern u1="V" u2="&#xe9;" k="131" />
<hkern u1="V" u2="&#xe8;" k="131" />
<hkern u1="V" u2="&#xe7;" k="131" />
<hkern u1="V" u2="&#xe6;" k="131" />
<hkern u1="V" u2="&#xe5;" k="131" />
<hkern u1="V" u2="&#xe4;" k="131" />
<hkern u1="V" u2="&#xe3;" k="131" />
<hkern u1="V" u2="&#xe2;" k="131" />
<hkern u1="V" u2="&#xe1;" k="131" />
<hkern u1="V" u2="&#xe0;" k="131" />
<hkern u1="V" u2="&#xdd;" k="-66" />
<hkern u1="V" u2="&#xd8;" k="10" />
<hkern u1="V" u2="&#xd6;" k="10" />
<hkern u1="V" u2="&#xd5;" k="10" />
<hkern u1="V" u2="&#xd4;" k="10" />
<hkern u1="V" u2="&#xd3;" k="10" />
<hkern u1="V" u2="&#xd2;" k="10" />
<hkern u1="V" u2="&#xc7;" k="10" />
<hkern u1="V" u2="&#xc6;" k="123" />
<hkern u1="V" u2="&#xc5;" k="154" />
<hkern u1="V" u2="&#xc4;" k="154" />
<hkern u1="V" u2="&#xc3;" k="154" />
<hkern u1="V" u2="&#xc2;" k="154" />
<hkern u1="V" u2="&#xc1;" k="154" />
<hkern u1="V" u2="&#xc0;" k="154" />
<hkern u1="V" u2="&#xbb;" k="27" />
<hkern u1="V" u2="&#xad;" k="96" />
<hkern u1="V" u2="&#xab;" k="106" />
<hkern u1="V" u2="y" k="35" />
<hkern u1="V" u2="x" k="35" />
<hkern u1="V" u2="w" k="25" />
<hkern u1="V" u2="v" k="35" />
<hkern u1="V" u2="u" k="106" />
<hkern u1="V" u2="t" k="41" />
<hkern u1="V" u2="s" k="121" />
<hkern u1="V" u2="r" k="76" />
<hkern u1="V" u2="q" k="131" />
<hkern u1="V" u2="p" k="76" />
<hkern u1="V" u2="o" k="131" />
<hkern u1="V" u2="n" k="76" />
<hkern u1="V" u2="m" k="76" />
<hkern u1="V" u2="g" k="131" />
<hkern u1="V" u2="e" k="131" />
<hkern u1="V" u2="d" k="131" />
<hkern u1="V" u2="c" k="131" />
<hkern u1="V" u2="a" k="131" />
<hkern u1="V" u2="_" k="88" />
<hkern u1="V" u2="Y" k="-66" />
<hkern u1="V" u2="W" k="-16" />
<hkern u1="V" u2="V" k="-47" />
<hkern u1="V" u2="S" k="16" />
<hkern u1="V" u2="Q" k="10" />
<hkern u1="V" u2="O" k="10" />
<hkern u1="V" u2="J" k="92" />
<hkern u1="V" u2="G" k="10" />
<hkern u1="V" u2="C" k="10" />
<hkern u1="V" u2="A" k="154" />
<hkern u1="V" u2="&#x2f;" k="133" />
<hkern u1="V" u2="&#x2e;" k="82" />
<hkern u1="V" u2="&#x2d;" k="96" />
<hkern u1="V" u2="&#x2c;" k="82" />
<hkern u1="W" u2="x" k="35" />
<hkern u1="W" u2="_" k="147" />
<hkern u1="W" u2="V" k="-16" />
<hkern u1="W" u2="&#x2f;" k="96" />
<hkern u1="X" u2="&#x203a;" k="-51" />
<hkern u1="X" u2="&#x2039;" k="41" />
<hkern u1="X" u2="&#x201d;" k="18" />
<hkern u1="X" u2="&#x201c;" k="18" />
<hkern u1="X" u2="&#x2019;" k="18" />
<hkern u1="X" u2="&#x2018;" k="18" />
<hkern u1="X" u2="&#x2014;" k="109" />
<hkern u1="X" u2="&#x2013;" k="109" />
<hkern u1="X" u2="&#x153;" k="31" />
<hkern u1="X" u2="&#x152;" k="74" />
<hkern u1="X" u2="&#xff;" k="106" />
<hkern u1="X" u2="&#xfd;" k="106" />
<hkern u1="X" u2="&#xf8;" k="31" />
<hkern u1="X" u2="&#xf6;" k="31" />
<hkern u1="X" u2="&#xf5;" k="31" />
<hkern u1="X" u2="&#xf4;" k="31" />
<hkern u1="X" u2="&#xf3;" k="31" />
<hkern u1="X" u2="&#xf2;" k="31" />
<hkern u1="X" u2="&#xf0;" k="31" />
<hkern u1="X" u2="&#xeb;" k="31" />
<hkern u1="X" u2="&#xea;" k="31" />
<hkern u1="X" u2="&#xe9;" k="31" />
<hkern u1="X" u2="&#xe8;" k="31" />
<hkern u1="X" u2="&#xe7;" k="31" />
<hkern u1="X" u2="&#xe6;" k="31" />
<hkern u1="X" u2="&#xe5;" k="31" />
<hkern u1="X" u2="&#xe4;" k="31" />
<hkern u1="X" u2="&#xe3;" k="31" />
<hkern u1="X" u2="&#xe2;" k="31" />
<hkern u1="X" u2="&#xe1;" k="31" />
<hkern u1="X" u2="&#xe0;" k="31" />
<hkern u1="X" u2="&#xd8;" k="74" />
<hkern u1="X" u2="&#xd6;" k="74" />
<hkern u1="X" u2="&#xd5;" k="74" />
<hkern u1="X" u2="&#xd4;" k="74" />
<hkern u1="X" u2="&#xd3;" k="74" />
<hkern u1="X" u2="&#xd2;" k="74" />
<hkern u1="X" u2="&#xc7;" k="74" />
<hkern u1="X" u2="&#xbb;" k="-51" />
<hkern u1="X" u2="&#xad;" k="109" />
<hkern u1="X" u2="&#xab;" k="41" />
<hkern u1="X" u2="y" k="106" />
<hkern u1="X" u2="w" k="86" />
<hkern u1="X" u2="v" k="106" />
<hkern u1="X" u2="q" k="31" />
<hkern u1="X" u2="o" k="31" />
<hkern u1="X" u2="g" k="31" />
<hkern u1="X" u2="e" k="31" />
<hkern u1="X" u2="d" k="31" />
<hkern u1="X" u2="c" k="31" />
<hkern u1="X" u2="a" k="31" />
<hkern u1="X" u2="Q" k="74" />
<hkern u1="X" u2="O" k="74" />
<hkern u1="X" u2="G" k="74" />
<hkern u1="X" u2="C" k="74" />
<hkern u1="X" u2="&#x2d;" k="109" />
<hkern u1="Y" u2="&#xff;" k="68" />
<hkern u1="Y" u2="&#xf6;" k="135" />
<hkern u1="Y" u2="&#xf5;" k="190" />
<hkern u1="Y" u2="&#xf4;" k="211" />
<hkern u1="Y" u2="&#xee;" k="-51" />
<hkern u1="Y" u2="&#xec;" k="-41" />
<hkern u1="Y" u2="&#xeb;" k="104" />
<hkern u1="Y" u2="&#xea;" k="207" />
<hkern u1="Y" u2="&#xe8;" k="195" />
<hkern u1="Y" u2="&#xe4;" k="141" />
<hkern u1="Y" u2="&#xe3;" k="150" />
<hkern u1="Y" u2="&#xe0;" k="197" />
<hkern u1="Y" u2="x" k="94" />
<hkern u1="Y" u2="_" k="205" />
<hkern u1="Y" u2="V" k="-66" />
<hkern u1="Y" u2="&#x34;" k="180" />
<hkern u1="Y" u2="&#x2f;" k="223" />
<hkern u1="Z" u2="x" k="-41" />
<hkern u1="\" u2="&#x178;" k="223" />
<hkern u1="\" u2="&#xdd;" k="223" />
<hkern u1="\" u2="Y" k="223" />
<hkern u1="\" u2="W" k="121" />
<hkern u1="\" u2="V" k="154" />
<hkern u1="\" u2="T" k="195" />
<hkern u1="\" u2="&#x37;" k="20" />
<hkern u1="_" u2="&#x178;" k="205" />
<hkern u1="_" u2="&#x153;" k="113" />
<hkern u1="_" u2="&#x152;" k="102" />
<hkern u1="_" u2="&#xff;" k="82" />
<hkern u1="_" u2="&#xfd;" k="82" />
<hkern u1="_" u2="&#xf8;" k="113" />
<hkern u1="_" u2="&#xf6;" k="113" />
<hkern u1="_" u2="&#xf5;" k="113" />
<hkern u1="_" u2="&#xf4;" k="113" />
<hkern u1="_" u2="&#xf3;" k="113" />
<hkern u1="_" u2="&#xf2;" k="113" />
<hkern u1="_" u2="&#xf0;" k="113" />
<hkern u1="_" u2="&#xeb;" k="113" />
<hkern u1="_" u2="&#xea;" k="113" />
<hkern u1="_" u2="&#xe9;" k="113" />
<hkern u1="_" u2="&#xe8;" k="113" />
<hkern u1="_" u2="&#xe7;" k="113" />
<hkern u1="_" u2="&#xe6;" k="113" />
<hkern u1="_" u2="&#xe5;" k="113" />
<hkern u1="_" u2="&#xe4;" k="113" />
<hkern u1="_" u2="&#xe3;" k="113" />
<hkern u1="_" u2="&#xe2;" k="113" />
<hkern u1="_" u2="&#xe1;" k="113" />
<hkern u1="_" u2="&#xe0;" k="113" />
<hkern u1="_" u2="&#xdd;" k="205" />
<hkern u1="_" u2="&#xd8;" k="102" />
<hkern u1="_" u2="&#xd6;" k="102" />
<hkern u1="_" u2="&#xd5;" k="102" />
<hkern u1="_" u2="&#xd4;" k="102" />
<hkern u1="_" u2="&#xd3;" k="102" />
<hkern u1="_" u2="&#xd2;" k="102" />
<hkern u1="_" u2="&#xc7;" k="102" />
<hkern u1="_" u2="y" k="82" />
<hkern u1="_" u2="w" k="113" />
<hkern u1="_" u2="v" k="82" />
<hkern u1="_" u2="q" k="113" />
<hkern u1="_" u2="o" k="113" />
<hkern u1="_" u2="g" k="113" />
<hkern u1="_" u2="e" k="113" />
<hkern u1="_" u2="d" k="113" />
<hkern u1="_" u2="c" k="113" />
<hkern u1="_" u2="a" k="113" />
<hkern u1="_" u2="Y" k="205" />
<hkern u1="_" u2="W" k="61" />
<hkern u1="_" u2="V" k="184" />
<hkern u1="_" u2="T" k="139" />
<hkern u1="_" u2="Q" k="102" />
<hkern u1="_" u2="O" k="102" />
<hkern u1="_" u2="G" k="102" />
<hkern u1="_" u2="C" k="102" />
<hkern u1="_" u2="&#x30;" k="82" />
<hkern u1="b" u2="x" k="55" />
<hkern u1="b" u2="_" k="57" />
<hkern u1="b" u2="X" k="31" />
<hkern u1="b" u2="V" k="168" />
<hkern u1="c" u2="V" k="76" />
<hkern u1="e" u2="x" k="25" />
<hkern u1="e" u2="V" k="229" />
<hkern u1="f" u2="_" k="31" />
<hkern u1="f" u2="&#x3f;" k="-14" />
<hkern u1="f" u2="&#x2a;" k="-41" />
<hkern u1="f" u2="&#x21;" k="-51" />
<hkern u1="g" u2="&#x201d;" k="20" />
<hkern u1="g" u2="&#x2019;" k="20" />
<hkern u1="g" u2="j" k="-47" />
<hkern u1="h" u2="V" k="121" />
<hkern u1="j" u2="j" k="-49" />
<hkern u1="k" u2="&#xf8;" k="47" />
<hkern u1="m" u2="V" k="121" />
<hkern u1="n" u2="&#x201c;" k="49" />
<hkern u1="n" u2="&#x2018;" k="49" />
<hkern u1="n" u2="V" k="121" />
<hkern u1="o" u2="x" k="55" />
<hkern u1="o" u2="_" k="57" />
<hkern u1="o" u2="X" k="31" />
<hkern u1="o" u2="V" k="168" />
<hkern u1="p" u2="x" k="55" />
<hkern u1="p" u2="_" k="57" />
<hkern u1="p" u2="X" k="31" />
<hkern u1="p" u2="V" k="168" />
<hkern u1="s" u2="x" k="20" />
<hkern u1="s" u2="V" k="174" />
<hkern u1="u" u2="V" k="113" />
<hkern u1="v" u2="x" k="-37" />
<hkern u1="v" u2="_" k="41" />
<hkern u1="v" u2="X" k="-43" />
<hkern u1="v" u2="V" k="31" />
<hkern u1="w" u2="x" k="-37" />
<hkern u1="w" u2="_" k="96" />
<hkern u1="w" u2="X" k="45" />
<hkern u1="w" u2="V" k="25" />
<hkern u1="x" u2="&#x203a;" k="-20" />
<hkern u1="x" u2="&#x2039;" k="57" />
<hkern u1="x" u2="&#x201d;" k="-20" />
<hkern u1="x" u2="&#x201c;" k="-72" />
<hkern u1="x" u2="&#x2019;" k="-20" />
<hkern u1="x" u2="&#x2018;" k="-72" />
<hkern u1="x" u2="&#x2014;" k="88" />
<hkern u1="x" u2="&#x2013;" k="88" />
<hkern u1="x" u2="&#x153;" k="55" />
<hkern u1="x" u2="&#xff;" k="-37" />
<hkern u1="x" u2="&#xfd;" k="-37" />
<hkern u1="x" u2="&#xf8;" k="55" />
<hkern u1="x" u2="&#xf6;" k="55" />
<hkern u1="x" u2="&#xf5;" k="55" />
<hkern u1="x" u2="&#xf4;" k="55" />
<hkern u1="x" u2="&#xf3;" k="55" />
<hkern u1="x" u2="&#xf2;" k="55" />
<hkern u1="x" u2="&#xf0;" k="55" />
<hkern u1="x" u2="&#xeb;" k="55" />
<hkern u1="x" u2="&#xea;" k="55" />
<hkern u1="x" u2="&#xe9;" k="55" />
<hkern u1="x" u2="&#xe8;" k="55" />
<hkern u1="x" u2="&#xe7;" k="55" />
<hkern u1="x" u2="&#xe6;" k="55" />
<hkern u1="x" u2="&#xe5;" k="55" />
<hkern u1="x" u2="&#xe4;" k="55" />
<hkern u1="x" u2="&#xe3;" k="55" />
<hkern u1="x" u2="&#xe2;" k="55" />
<hkern u1="x" u2="&#xe1;" k="55" />
<hkern u1="x" u2="&#xe0;" k="55" />
<hkern u1="x" u2="&#xbb;" k="-20" />
<hkern u1="x" u2="&#xad;" k="88" />
<hkern u1="x" u2="&#xab;" k="57" />
<hkern u1="x" u2="y" k="-37" />
<hkern u1="x" u2="w" k="-37" />
<hkern u1="x" u2="v" k="-37" />
<hkern u1="x" u2="q" k="55" />
<hkern u1="x" u2="o" k="55" />
<hkern u1="x" u2="g" k="55" />
<hkern u1="x" u2="e" k="55" />
<hkern u1="x" u2="d" k="55" />
<hkern u1="x" u2="c" k="55" />
<hkern u1="x" u2="a" k="55" />
<hkern u1="x" u2="W" k="37" />
<hkern u1="x" u2="V" k="37" />
<hkern u1="x" u2="T" k="94" />
<hkern u1="x" u2="&#x32;" k="-57" />
<hkern u1="x" u2="&#x2d;" k="88" />
<hkern u1="y" u2="x" k="-37" />
<hkern u1="y" u2="_" k="41" />
<hkern u1="y" u2="X" k="-43" />
<hkern u1="y" u2="V" k="31" />
<hkern u1="&#xab;" u2="x" k="8" />
<hkern u1="&#xab;" u2="V" k="74" />
<hkern u1="&#xad;" u2="x" k="88" />
<hkern u1="&#xad;" u2="X" k="72" />
<hkern u1="&#xad;" u2="V" k="121" />
<hkern u1="&#xad;" u2="&#x32;" k="115" />
<hkern u1="&#xbb;" u2="x" k="57" />
<hkern u1="&#xbb;" u2="a" k="8" />
<hkern u1="&#xbb;" u2="X" k="51" />
<hkern u1="&#xbb;" u2="V" k="106" />
<hkern u1="&#xc0;" u2="x" k="-72" />
<hkern u1="&#xc0;" u2="\" k="143" />
<hkern u1="&#xc0;" u2="V" k="160" />
<hkern u1="&#xc1;" u2="x" k="-72" />
<hkern u1="&#xc1;" u2="\" k="143" />
<hkern u1="&#xc1;" u2="V" k="160" />
<hkern u1="&#xc2;" u2="x" k="-72" />
<hkern u1="&#xc2;" u2="\" k="143" />
<hkern u1="&#xc2;" u2="V" k="160" />
<hkern u1="&#xc3;" u2="x" k="-72" />
<hkern u1="&#xc3;" u2="\" k="143" />
<hkern u1="&#xc3;" u2="V" k="160" />
<hkern u1="&#xc4;" u2="x" k="-72" />
<hkern u1="&#xc4;" u2="\" k="143" />
<hkern u1="&#xc4;" u2="V" k="160" />
<hkern u1="&#xc5;" u2="x" k="-72" />
<hkern u1="&#xc5;" u2="\" k="143" />
<hkern u1="&#xc5;" u2="V" k="160" />
<hkern u1="&#xc6;" u2="x" k="-23" />
<hkern u1="&#xc8;" u2="x" k="-23" />
<hkern u1="&#xc9;" u2="x" k="-23" />
<hkern u1="&#xca;" u2="x" k="-23" />
<hkern u1="&#xcb;" u2="x" k="-23" />
<hkern u1="&#xd2;" u2="x" k="20" />
<hkern u1="&#xd2;" u2="_" k="102" />
<hkern u1="&#xd2;" u2="X" k="74" />
<hkern u1="&#xd2;" u2="V" k="80" />
<hkern u1="&#xd3;" u2="x" k="20" />
<hkern u1="&#xd3;" u2="_" k="102" />
<hkern u1="&#xd3;" u2="X" k="74" />
<hkern u1="&#xd3;" u2="V" k="80" />
<hkern u1="&#xd4;" u2="x" k="20" />
<hkern u1="&#xd4;" u2="_" k="102" />
<hkern u1="&#xd4;" u2="X" k="74" />
<hkern u1="&#xd4;" u2="V" k="80" />
<hkern u1="&#xd5;" u2="x" k="20" />
<hkern u1="&#xd5;" u2="_" k="102" />
<hkern u1="&#xd5;" u2="X" k="74" />
<hkern u1="&#xd5;" u2="V" k="80" />
<hkern u1="&#xd6;" u2="x" k="20" />
<hkern u1="&#xd6;" u2="_" k="102" />
<hkern u1="&#xd6;" u2="X" k="74" />
<hkern u1="&#xd6;" u2="V" k="80" />
<hkern u1="&#xd8;" u2="x" k="20" />
<hkern u1="&#xd8;" u2="_" k="102" />
<hkern u1="&#xd8;" u2="X" k="74" />
<hkern u1="&#xd8;" u2="V" k="80" />
<hkern u1="&#xd9;" u2="x" k="10" />
<hkern u1="&#xda;" u2="x" k="10" />
<hkern u1="&#xdb;" u2="x" k="10" />
<hkern u1="&#xdc;" u2="x" k="10" />
<hkern u1="&#xdd;" u2="&#xff;" k="68" />
<hkern u1="&#xdd;" u2="&#xf6;" k="135" />
<hkern u1="&#xdd;" u2="&#xf5;" k="190" />
<hkern u1="&#xdd;" u2="&#xf4;" k="211" />
<hkern u1="&#xdd;" u2="&#xee;" k="-51" />
<hkern u1="&#xdd;" u2="&#xec;" k="-41" />
<hkern u1="&#xdd;" u2="&#xeb;" k="104" />
<hkern u1="&#xdd;" u2="&#xea;" k="207" />
<hkern u1="&#xdd;" u2="&#xe8;" k="195" />
<hkern u1="&#xdd;" u2="&#xe4;" k="141" />
<hkern u1="&#xdd;" u2="&#xe3;" k="150" />
<hkern u1="&#xdd;" u2="&#xe0;" k="197" />
<hkern u1="&#xdd;" u2="x" k="94" />
<hkern u1="&#xdd;" u2="_" k="205" />
<hkern u1="&#xdd;" u2="V" k="-66" />
<hkern u1="&#xdd;" u2="&#x34;" k="180" />
<hkern u1="&#xdd;" u2="&#x2f;" k="223" />
<hkern u1="&#xde;" u2="&#x178;" k="113" />
<hkern u1="&#xde;" u2="&#xdd;" k="113" />
<hkern u1="&#xde;" u2="&#xc5;" k="41" />
<hkern u1="&#xde;" u2="&#xc4;" k="41" />
<hkern u1="&#xde;" u2="&#xc3;" k="41" />
<hkern u1="&#xde;" u2="&#xc2;" k="41" />
<hkern u1="&#xde;" u2="&#xc1;" k="41" />
<hkern u1="&#xde;" u2="&#xc0;" k="41" />
<hkern u1="&#xde;" u2="_" k="246" />
<hkern u1="&#xde;" u2="Y" k="113" />
<hkern u1="&#xde;" u2="W" k="51" />
<hkern u1="&#xde;" u2="V" k="41" />
<hkern u1="&#xde;" u2="T" k="41" />
<hkern u1="&#xde;" u2="A" k="41" />
<hkern u1="&#xdf;" u2="&#xff;" k="74" />
<hkern u1="&#xdf;" u2="&#xfd;" k="74" />
<hkern u1="&#xdf;" u2="y" k="74" />
<hkern u1="&#xdf;" u2="w" k="72" />
<hkern u1="&#xdf;" u2="v" k="74" />
<hkern u1="&#xe4;" u2="T" k="131" />
<hkern u1="&#xe6;" u2="x" k="25" />
<hkern u1="&#xe6;" u2="V" k="229" />
<hkern u1="&#xe7;" u2="V" k="76" />
<hkern u1="&#xe8;" u2="x" k="25" />
<hkern u1="&#xe8;" u2="V" k="229" />
<hkern u1="&#xe9;" u2="x" k="25" />
<hkern u1="&#xe9;" u2="V" k="229" />
<hkern u1="&#xea;" u2="x" k="25" />
<hkern u1="&#xea;" u2="V" k="229" />
<hkern u1="&#xeb;" u2="x" k="25" />
<hkern u1="&#xeb;" u2="V" k="229" />
<hkern u1="&#xed;" u2="T" k="-41" />
<hkern u1="&#xee;" u2="T" k="-94" />
<hkern u1="&#xef;" u2="V" k="-113" />
<hkern u1="&#xef;" u2="P" k="-57" />
<hkern u1="&#xf2;" u2="x" k="55" />
<hkern u1="&#xf2;" u2="_" k="57" />
<hkern u1="&#xf2;" u2="X" k="31" />
<hkern u1="&#xf2;" u2="V" k="168" />
<hkern u1="&#xf3;" u2="x" k="55" />
<hkern u1="&#xf3;" u2="_" k="57" />
<hkern u1="&#xf3;" u2="X" k="31" />
<hkern u1="&#xf3;" u2="V" k="168" />
<hkern u1="&#xf4;" u2="x" k="55" />
<hkern u1="&#xf4;" u2="_" k="57" />
<hkern u1="&#xf4;" u2="X" k="31" />
<hkern u1="&#xf4;" u2="V" k="168" />
<hkern u1="&#xf5;" u2="x" k="55" />
<hkern u1="&#xf5;" u2="_" k="57" />
<hkern u1="&#xf5;" u2="X" k="31" />
<hkern u1="&#xf5;" u2="V" k="168" />
<hkern u1="&#xf6;" u2="x" k="55" />
<hkern u1="&#xf6;" u2="_" k="57" />
<hkern u1="&#xf6;" u2="X" k="31" />
<hkern u1="&#xf6;" u2="V" k="168" />
<hkern u1="&#xf8;" u2="x" k="55" />
<hkern u1="&#xf8;" u2="_" k="57" />
<hkern u1="&#xf8;" u2="X" k="31" />
<hkern u1="&#xf8;" u2="V" k="168" />
<hkern u1="&#xf9;" u2="V" k="113" />
<hkern u1="&#xfa;" u2="V" k="113" />
<hkern u1="&#xfb;" u2="V" k="113" />
<hkern u1="&#xfc;" u2="V" k="113" />
<hkern u1="&#xfd;" u2="x" k="-37" />
<hkern u1="&#xfd;" u2="_" k="41" />
<hkern u1="&#xfd;" u2="X" k="-43" />
<hkern u1="&#xfd;" u2="V" k="31" />
<hkern u1="&#xfe;" u2="x" k="55" />
<hkern u1="&#xfe;" u2="_" k="57" />
<hkern u1="&#xfe;" u2="X" k="31" />
<hkern u1="&#xfe;" u2="V" k="168" />
<hkern u1="&#xff;" u2="x" k="-37" />
<hkern u1="&#xff;" u2="_" k="41" />
<hkern u1="&#xff;" u2="X" k="-43" />
<hkern u1="&#xff;" u2="V" k="31" />
<hkern u1="&#x152;" u2="x" k="-23" />
<hkern u1="&#x153;" u2="x" k="25" />
<hkern u1="&#x153;" u2="V" k="229" />
<hkern u1="&#x178;" u2="&#xff;" k="68" />
<hkern u1="&#x178;" u2="&#xf6;" k="135" />
<hkern u1="&#x178;" u2="&#xf5;" k="190" />
<hkern u1="&#x178;" u2="&#xf4;" k="211" />
<hkern u1="&#x178;" u2="&#xee;" k="-51" />
<hkern u1="&#x178;" u2="&#xec;" k="-41" />
<hkern u1="&#x178;" u2="&#xeb;" k="104" />
<hkern u1="&#x178;" u2="&#xea;" k="207" />
<hkern u1="&#x178;" u2="&#xe8;" k="195" />
<hkern u1="&#x178;" u2="&#xe4;" k="141" />
<hkern u1="&#x178;" u2="&#xe3;" k="150" />
<hkern u1="&#x178;" u2="&#xe0;" k="197" />
<hkern u1="&#x178;" u2="x" k="94" />
<hkern u1="&#x178;" u2="_" k="205" />
<hkern u1="&#x178;" u2="V" k="-66" />
<hkern u1="&#x178;" u2="&#x34;" k="180" />
<hkern u1="&#x178;" u2="&#x2f;" k="223" />
<hkern u1="&#x2013;" u2="x" k="88" />
<hkern u1="&#x2013;" u2="X" k="72" />
<hkern u1="&#x2013;" u2="V" k="121" />
<hkern u1="&#x2013;" u2="&#x32;" k="115" />
<hkern u1="&#x2013;" u2="&#x31;" k="51" />
<hkern u1="&#x2014;" u2="x" k="88" />
<hkern u1="&#x2014;" u2="X" k="72" />
<hkern u1="&#x2014;" u2="V" k="121" />
<hkern u1="&#x2014;" u2="&#x32;" k="115" />
<hkern u1="&#x2018;" u2="x" k="41" />
<hkern u1="&#x2018;" u2="j" k="20" />
<hkern u1="&#x2018;" u2="V" k="-29" />
<hkern u1="&#x2019;" u2="&#xc5;" k="152" />
<hkern u1="&#x2019;" u2="&#xc4;" k="152" />
<hkern u1="&#x2019;" u2="&#xc3;" k="152" />
<hkern u1="&#x2019;" u2="&#xc2;" k="152" />
<hkern u1="&#x2019;" u2="&#xc1;" k="152" />
<hkern u1="&#x2019;" u2="&#xc0;" k="152" />
<hkern u1="&#x2019;" u2="x" k="66" />
<hkern u1="&#x2019;" u2="s" k="184" />
<hkern u1="&#x2019;" u2="j" k="10" />
<hkern u1="&#x2019;" u2="V" k="-49" />
<hkern u1="&#x2019;" u2="A" k="152" />
<hkern u1="&#x201a;" u2="V" k="72" />
<hkern u1="&#x201c;" u2="x" k="41" />
<hkern u1="&#x201c;" u2="j" k="20" />
<hkern u1="&#x201c;" u2="V" k="-29" />
<hkern u1="&#x201d;" u2="x" k="66" />
<hkern u1="&#x201d;" u2="j" k="10" />
<hkern u1="&#x201d;" u2="V" k="-49" />
<hkern u1="&#x201e;" u2="V" k="72" />
<hkern u1="&#x2026;" u2="V" k="133" />
<hkern u1="&#x2026;" u2="&#x39;" k="143" />
<hkern u1="&#x2026;" u2="&#x38;" k="37" />
<hkern u1="&#x2026;" u2="&#x37;" k="72" />
<hkern u1="&#x2026;" u2="&#x2a;" k="246" />
<hkern u1="&#x2039;" u2="x" k="8" />
<hkern u1="&#x2039;" u2="V" k="74" />
<hkern u1="&#x203a;" u2="x" k="57" />
<hkern u1="&#x203a;" u2="a" k="8" />
<hkern u1="&#x203a;" u2="X" k="51" />
<hkern u1="&#x203a;" u2="V" k="106" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="G" 	g2="T" 	k="53" />
<hkern g1="G" 	g2="w" 	k="20" />
<hkern g1="G" 	g2="v,y,yacute,ydieresis" 	k="25" />
<hkern g1="G" 	g2="z" 	k="6" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="27" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="K" 	g2="w" 	k="92" />
<hkern g1="K" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="61" />
<hkern g1="K" 	g2="t" 	k="72" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="74" />
<hkern g1="K" 	g2="s" 	k="20" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-51" />
<hkern g1="K" 	g2="z" 	k="-29" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="156" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-31" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="266" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="289" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="L" 	g2="W" 	k="225" />
<hkern g1="L" 	g2="T" 	k="188" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="139" />
<hkern g1="L" 	g2="w" 	k="84" />
<hkern g1="L" 	g2="v,y,yacute,ydieresis" 	k="135" />
<hkern g1="L" 	g2="t" 	k="41" />
<hkern g1="L" 	g2="z" 	k="-23" />
<hkern g1="L" 	g2="J" 	k="-29" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="160" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotright,guilsinglright" 	k="8" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="70" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="55" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="123" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="45" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="121" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="R" 	g2="W" 	k="82" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="R" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-74" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="S" 	g2="W" 	k="55" />
<hkern g1="S" 	g2="T" 	k="41" />
<hkern g1="S" 	g2="w" 	k="35" />
<hkern g1="S" 	g2="v,y,yacute,ydieresis" 	k="35" />
<hkern g1="S" 	g2="t" 	k="14" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="S" 	g2="z" 	k="20" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="236" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="180" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="139" />
<hkern g1="T" 	g2="w" 	k="164" />
<hkern g1="T" 	g2="v,y,yacute,ydieresis" 	k="143" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="174" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="195" />
<hkern g1="T" 	g2="s" 	k="160" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="164" />
<hkern g1="T" 	g2="z" 	k="188" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="164" />
<hkern g1="T" 	g2="J" 	k="129" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="205" />
<hkern g1="T" 	g2="AE" 	k="174" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="184" />
<hkern g1="T" 	g2="idieresis" 	k="-131" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="27" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-35" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="102" />
<hkern g1="W" 	g2="w" 	k="35" />
<hkern g1="W" 	g2="v,y,yacute,ydieresis" 	k="14" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="51" />
<hkern g1="W" 	g2="t" 	k="31" />
<hkern g1="W" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="W" 	g2="s" 	k="119" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="113" />
<hkern g1="W" 	g2="J" 	k="119" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="68" />
<hkern g1="W" 	g2="AE" 	k="96" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="68" />
<hkern g1="W" 	g2="S" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute,ydieresis" 	k="104" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="227" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="188" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="160" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="174" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="129" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="idieresis" 	k="-158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="Z" 	g2="w" 	k="74" />
<hkern g1="Z" 	g2="v,y,yacute,ydieresis" 	k="90" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="10" />
<hkern g1="Z" 	g2="idieresis" 	k="-109" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="39" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="25" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v,y,yacute,ydieresis" 	k="29" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="236" />
<hkern g1="comma,period,ellipsis" 	g2="quoteleft,quotedblleft" 	k="164" />
<hkern g1="comma,period,ellipsis" 	g2="quoteright,quotedblright" 	k="174" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="45" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="205" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="27" />
<hkern g1="comma,period,ellipsis" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="v,y,yacute,ydieresis" 	k="113" />
<hkern g1="comma,period,ellipsis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="57" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="20" />
<hkern g1="comma,period,ellipsis" 	g2="parenright,bracketright,braceright" 	k="29" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="227" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="49" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="229" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="188" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="25" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v,y,yacute,ydieresis" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotesinglbase,quotedblbase" 	k="8" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-25" />
<hkern g1="f" 	g2="w" 	k="-25" />
<hkern g1="f" 	g2="v,y,yacute,ydieresis" 	k="-37" />
<hkern g1="f" 	g2="t" 	k="-4" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="49" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="41" />
<hkern g1="f" 	g2="parenright,bracketright,braceright" 	k="-102" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="180" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="180" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="w" 	k="-16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v,y,yacute,ydieresis" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="s" 	k="35" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="z" 	k="4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Z" 	k="16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="S" 	k="4" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="guillemotright,guilsinglright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="236" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="43" />
<hkern g1="guillemotright,guilsinglright" 	g2="v,y,yacute,ydieresis" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="55" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="195" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="72" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="174" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="w" 	k="14" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="v,y,yacute,ydieresis" 	k="18" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="61" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="14" />
<hkern g1="idieresis" 	g2="Y,Yacute,Ydieresis" 	k="-131" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="-8" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-57" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="k" 	g2="w" 	k="-49" />
<hkern g1="k" 	g2="v,y,yacute,ydieresis" 	k="-41" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="68" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="55" />
<hkern g1="k" 	g2="s" 	k="27" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="h,m,n" 	g2="Y,Yacute,Ydieresis" 	k="162" />
<hkern g1="h,m,n" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="h,m,n" 	g2="quoteright,quotedblright" 	k="92" />
<hkern g1="h,m,n" 	g2="W" 	k="133" />
<hkern g1="h,m,n" 	g2="T" 	k="176" />
<hkern g1="h,m,n" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="h,m,n" 	g2="w" 	k="45" />
<hkern g1="h,m,n" 	g2="v,y,yacute,ydieresis" 	k="49" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="227" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="guillemotright,guilsinglright" 	k="57" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="66" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="W" 	k="113" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="195" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="45" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v,y,yacute,ydieresis" 	k="59" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotesinglbase,quotedblbase" 	k="49" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="parenright,bracketright,braceright" 	k="31" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-57" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="98" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="v,y,yacute,ydieresis" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="156" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="115" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="190" />
<hkern g1="quoteleft,quotedblleft" 	g2="z" 	k="74" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,ellipsis" 	k="266" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="123" />
<hkern g1="quoteright,quotedblright" 	g2="w" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="v,y,yacute,ydieresis" 	k="90" />
<hkern g1="quoteright,quotedblright" 	g2="t" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="205" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="215" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="178" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,ellipsis" 	k="225" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="106" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB01,uniFB02" 	k="16" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="113" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="45" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="113" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v,y,yacute,ydieresis" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="45" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="quotedbl,quotesingle" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="154" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="r" 	g2="w" 	k="-14" />
<hkern g1="r" 	g2="v,y,yacute,ydieresis" 	k="-8" />
<hkern g1="r" 	g2="t" 	k="-18" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="147" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="143" />
<hkern g1="r" 	g2="f,uniFB01,uniFB02" 	k="-12" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="160" />
<hkern g1="s" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="68" />
<hkern g1="s" 	g2="W" 	k="188" />
<hkern g1="s" 	g2="T" 	k="188" />
<hkern g1="s" 	g2="w" 	k="55" />
<hkern g1="s" 	g2="v,y,yacute,ydieresis" 	k="53" />
<hkern g1="s" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="s" 	g2="t" 	k="47" />
<hkern g1="s" 	g2="Z" 	k="20" />
<hkern g1="s" 	g2="quotesinglbase,quotedblbase" 	k="16" />
<hkern g1="s" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="t" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="102" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="96" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="w" 	g2="W" 	k="25" />
<hkern g1="w" 	g2="T" 	k="174" />
<hkern g1="w" 	g2="w" 	k="-47" />
<hkern g1="w" 	g2="v,y,yacute,ydieresis" 	k="-51" />
<hkern g1="w" 	g2="hyphen,uni00AD,endash,emdash" 	k="10" />
<hkern g1="w" 	g2="t" 	k="-31" />
<hkern g1="w" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="w" 	g2="s" 	k="14" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="47" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="82" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="82" />
<hkern g1="w" 	g2="f,uniFB01,uniFB02" 	k="-20" />
<hkern g1="zero" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="106" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="v,y,yacute,ydieresis" 	g2="W" 	k="10" />
<hkern g1="v,y,yacute,ydieresis" 	g2="T" 	k="133" />
<hkern g1="v,y,yacute,ydieresis" 	g2="w" 	k="-51" />
<hkern g1="v,y,yacute,ydieresis" 	g2="v,y,yacute,ydieresis" 	k="-41" />
<hkern g1="v,y,yacute,ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="14" />
<hkern g1="v,y,yacute,ydieresis" 	g2="t" 	k="-31" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="v,y,yacute,ydieresis" 	g2="s" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="98" />
<hkern g1="v,y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="102" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="127" />
<hkern g1="v,y,yacute,ydieresis" 	g2="f,uniFB01,uniFB02" 	k="-25" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="z" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="z" 	g2="z" 	k="10" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="170" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="c,ccedilla" 	g2="W" 	k="47" />
<hkern g1="c,ccedilla" 	g2="T" 	k="160" />
<hkern g1="c,ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
</font>
</defs></svg> 