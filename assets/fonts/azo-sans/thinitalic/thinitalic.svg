<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="azo_sansthin_italic" horiz-adv-x="1652" >
<font-face units-per-em="2048" ascent="1581" descent="-467" />
<missing-glyph horiz-adv-x="499" />
<glyph unicode="&#xfb01;" horiz-adv-x="1075" d="M129 965l8 43h187l53 305q14 82 37 130t59 81q69 63 180 63q63 0 154 -24l-8 -45q-86 24 -146 24q-95 0 -149 -51q-56 -54 -80 -184l-53 -299h303l-8 -43h-304l-169 -965h-48l170 965h-186zM741 0l179 1008h47l-179 -1008h-47zM950 1362q0 23 17 39t41 16 q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1075" d="M129 965l8 43h187l53 305q14 82 37 130t59 81q69 63 180 63q63 0 154 -24l-8 -45q-86 24 -146 24q-95 0 -149 -51q-56 -54 -80 -184l-53 -299h303l-8 -43h-304l-169 -965h-48l170 965h-186zM741 0l277 1565h47l-277 -1565h-47z" />
<glyph horiz-adv-x="2" />
<glyph horiz-adv-x="2" />
<glyph horiz-adv-x="682" />
<glyph unicode="&#xd;" horiz-adv-x="688" />
<glyph unicode=" "  horiz-adv-x="499" />
<glyph unicode="&#x09;" horiz-adv-x="499" />
<glyph unicode="&#xa0;" horiz-adv-x="499" />
<glyph unicode="!" horiz-adv-x="649" d="M176 43q0 28 19 47t47 19t46.5 -19t18.5 -47t-18.5 -47t-46.5 -19t-47 19t-19 47zM287 401l184 1082h49l-196 -1082h-37z" />
<glyph unicode="&#x22;" horiz-adv-x="661" d="M260 1001l82 519h55l-102 -519h-35zM541 1001l82 519h55l-103 -519h-34z" />
<glyph unicode="#" horiz-adv-x="1208" d="M29 502l20 43h291l182 393h-291l21 43h291l233 502h45l-233 -502h340l233 502h45l-233 -502h291l-21 -43h-291l-182 -393h291l-21 -43h-290l-234 -502h-45l234 502h-340l-234 -502h-45l233 502h-290zM385 545h340l182 393h-340z" />
<glyph unicode="$" horiz-adv-x="1079" d="M113 219l8 49q80 -60 186.5 -93.5t210.5 -33.5q57 0 110 11t101.5 34.5t84 58.5t56.5 86t21 113q0 103 -71 165.5t-226 115.5q-79 28 -133.5 55t-101.5 65t-70 88t-23 114q0 154 114 245.5t288 93.5l45 252h45l-45 -254q161 -8 317 -100l-8 -49q-164 106 -350 106 q-68 0 -130.5 -17t-114.5 -51t-83 -91t-31 -129q0 -105 70 -168t225 -117q64 -23 107.5 -42t89 -47.5t72.5 -60t44 -76t17 -98.5q0 -162 -120 -255t-300 -93h-31l-45 -252h-45l45 256q-197 22 -329 119z" />
<glyph unicode="%" horiz-adv-x="1878" d="M242 1081q0 102 46.5 198.5t134.5 161t195 64.5q135 0 222.5 -102t87.5 -240q0 -75 -28 -151t-76.5 -136.5t-120 -98.5t-152.5 -38q-135 0 -222 102t-87 240zM287 1085q0 -122 73.5 -212.5t194.5 -90.5q89 0 165.5 55t119.5 141.5t43 180.5q0 122 -74 212.5t-195 90.5 q-89 0 -165.5 -55t-119 -141.5t-42.5 -180.5zM342 0l1225 1483h53l-1225 -1483h-53zM1034 319q0 75 28 151t76.5 136.5t120 98.5t152.5 38q135 0 222 -102t87 -240q0 -75 -28 -151t-76.5 -136.5t-120 -98.5t-152.5 -38q-135 0 -222 102t-87 240zM1079 324q0 -122 74 -213 t195 -91q89 0 165.5 55t119 141.5t42.5 180.5q0 122 -73.5 212.5t-194.5 90.5q-89 0 -165.5 -55t-119.5 -141t-43 -180z" />
<glyph unicode="&#x26;" horiz-adv-x="1415" d="M164 317q0 131 79 238t212 182l151 86l-65 82q-105 130 -105 256q0 139 100 241.5t248 102.5q120 0 200.5 -70t80.5 -190q0 -209 -254 -354l-139 -80l375 -471l237 549h47l-254 -586l238 -299l-2 -4h-53l-205 256q-72 -134 -198 -206.5t-288 -72.5q-186 0 -295.5 92.5 t-109.5 247.5zM211 319q0 -135 95 -215.5t265 -80.5q146 0 265 65.5t190 204.5l-393 495l-150 -83q-52 -30 -96.5 -65.5t-86 -83t-65.5 -109t-24 -128.5zM483 1161q0 -60 21.5 -114t73.5 -117l67 -84l146 82q227 129 227 311q0 100 -65 160.5t-169 60.5q-125 0 -213 -83 t-88 -216z" />
<glyph unicode="'" horiz-adv-x="380" d="M260 1001l82 519h55l-102 -519h-35z" />
<glyph unicode="(" horiz-adv-x="606" d="M158 223q0 178 37 372t106 366q66 163 159 307t197 254h54q-234 -242 -369 -576q-139 -344 -139 -715q0 -389 157 -667h-49q-74 134 -113.5 304.5t-39.5 354.5z" />
<glyph unicode=")" horiz-adv-x="606" d="M-61 -436q233 241 368 575q139 344 139 715q0 390 -157 668h49q74 -134 114 -305t40 -355q0 -178 -37.5 -371.5t-106.5 -365.5q-66 -163 -159 -307t-197 -254h-53z" />
<glyph unicode="*" horiz-adv-x="937" d="M190 1194l23 47l303 -113l-16 -34zM248 772l256 277l26 -23l-245 -281zM561 1155l55 342h50l-68 -342h-37zM584 1026l34 21l158 -275l-45 -27zM635 1128l344 113l6 -47l-346 -100z" />
<glyph unicode="+" horiz-adv-x="1167" d="M150 721l8 43h450l84 481h45l-84 -481h451l-8 -43h-451l-86 -483h-45l86 483h-450z" />
<glyph unicode="," horiz-adv-x="614" d="M41 -301l217 534h57l-235 -534h-39z" />
<glyph unicode="-" horiz-adv-x="741" d="M150 545l8 45h456l-8 -45h-456z" />
<glyph unicode="." horiz-adv-x="614" d="M160 43q0 28 18.5 47t46.5 19t47 -19t19 -47t-19 -47t-47 -19t-46.5 19t-18.5 47z" />
<glyph unicode="/" horiz-adv-x="860" d="M-43 -78l979 1639h51l-979 -1639h-51z" />
<glyph unicode="0" horiz-adv-x="1114" d="M119 492q0 157 37.5 344t105.5 335q152 334 438 334q189 0 284 -136t95 -378q0 -157 -37.5 -344.5t-105.5 -335.5q-152 -334 -438 -334q-189 0 -284 136.5t-95 378.5zM166 494q0 -105 19 -189.5t57.5 -148.5t103.5 -98.5t152 -34.5q254 0 395 305q64 139 101.5 321 t37.5 340q0 105 -19 189.5t-57.5 148.5t-103.5 98.5t-152 34.5q-254 0 -395 -305q-64 -139 -101.5 -321t-37.5 -340z" />
<glyph unicode="1" horiz-adv-x="1001" d="M301 1272l8 51l471 182l-266 -1505h-47l252 1434z" />
<glyph unicode="2" horiz-adv-x="1097" d="M-12 4l645 592q73 67 125.5 123t107 129.5t83 152t28.5 158.5q0 145 -93 223t-243 78q-90 0 -175.5 -27t-150.5 -71l11 57q146 86 313 86q79 0 147 -21t122 -62.5t85 -109.5t31 -155q0 -81 -27 -160t-79 -154t-110 -138t-138 -136l-568 -520l2 -4h791l-8 -45h-897z" />
<glyph unicode="3" horiz-adv-x="1042" d="M37 109l8 51q82 -67 187 -102t208 -35q188 0 308 104q61 53 93.5 125t32.5 149q0 101 -56 176.5t-143 111.5t-190 36h-131l8 43h134q110 0 198 39.5t144 103.5q90 103 90 240q0 143 -96.5 226t-253.5 83q-154 0 -261 -69l11 57q114 57 250 57q169 0 283 -92.5t114 -259.5 q0 -151 -98 -264q-89 -103 -236 -141v-5q126 -35 203.5 -124.5t77.5 -212.5q0 -86 -38 -168.5t-106 -141.5q-136 -119 -342 -119q-109 0 -215.5 35.5t-183.5 96.5z" />
<glyph unicode="4" horiz-adv-x="1249" d="M53 430l961 1053h49l-178 -1012h329l-8 -45h-329l-76 -426h-47l75 426h-774zM150 471h688l163 930l-2 2z" />
<glyph unicode="5" horiz-adv-x="1077" d="M61 119l13 51q176 -147 391 -147q202 0 321 116q52 49 82.5 120.5t30.5 154.5q0 222 -227 323q-148 66 -398 66h-34l178 680h612l-12 -45h-565l-156 -592q237 0 401 -72q115 -53 181.5 -144t66.5 -214q0 -90 -34 -170t-93 -137q-135 -132 -356 -132q-225 0 -402 142z" />
<glyph unicode="6" horiz-adv-x="1136" d="M104 381q0 68 17.5 135t54 136t69.5 120t87 125l424 586h55l-440 -606q-97 -133 -135 -201l2 -2q61 70 156.5 113.5t201.5 43.5q180 0 301 -110t121 -287q0 -185 -135 -317q-140 -140 -348 -140q-186 0 -308.5 113t-122.5 291zM154 381q0 -152 103.5 -255t279.5 -103 q184 0 309 122t125 289q0 152 -105.5 251t-269.5 99q-184 0 -313 -118q-129 -119 -129 -285z" />
<glyph unicode="7" horiz-adv-x="1075" d="M199 0l882 1438h-813l8 45h881l2 -4l-907 -1479h-53z" />
<glyph unicode="8" horiz-adv-x="1187" d="M106 358q0 164 110.5 285.5t283.5 155.5v4q-92 36 -153.5 117.5t-61.5 189.5q0 80 36.5 155.5t102.5 129.5q138 110 309 110q164 0 277.5 -93t113.5 -251q0 -144 -96 -249.5t-242 -133.5v-4q112 -43 183 -136t71 -216q0 -90 -38.5 -172.5t-106.5 -143.5 q-139 -126 -352 -126q-190 0 -313.5 102t-123.5 276zM154 358q0 -150 106.5 -241.5t280.5 -91.5q197 0 321 112q61 55 96 129t35 156q0 149 -113.5 246.5t-279.5 97.5q-101 0 -190 -38t-152 -105q-104 -113 -104 -265zM332 1112q0 -93 50.5 -164t129 -106t170.5 -35 q82 0 158.5 30t132.5 83q104 97 104 239t-101 221.5t-245 79.5q-158 0 -274 -96q-125 -104 -125 -252z" />
<glyph unicode="9" horiz-adv-x="1101" d="M176 1044q0 192 137 326q142 135 340 135q179 0 301.5 -111t122.5 -292q0 -69 -16 -133.5t-51.5 -132t-69 -119.5t-90.5 -131l-424 -586h-55l440 606q99 134 141 211l-4 2q-61 -72 -159 -118t-207 -46q-171 0 -288.5 107t-117.5 282zM223 1049q0 -156 103 -252.5 t260 -96.5q180 0 311 121q129 116 129 285q0 152 -105.5 253t-271.5 101q-176 0 -297 -114q-129 -120 -129 -297z" />
<glyph unicode=":" horiz-adv-x="614" d="M160 43q0 28 18.5 47t46.5 19t47 -19t19 -47t-19 -47t-47 -19t-46.5 19t-18.5 47zM324 965q0 28 18.5 46.5t46.5 18.5t47 -18.5t19 -46.5t-19 -47t-47 -19t-46.5 19t-18.5 47z" />
<glyph unicode=";" horiz-adv-x="614" d="M41 -301l217 534h57l-235 -534h-39zM324 965q0 28 18.5 46.5t46.5 18.5t47 -18.5t19 -46.5t-19 -47t-47 -19t-46.5 19t-18.5 47z" />
<glyph unicode="&#x3c;" horiz-adv-x="1323" d="M233 723l7 35l1015 454l-10 -51l-942 -422v-2l793 -422l-9 -47z" />
<glyph unicode="=" horiz-adv-x="1167" d="M115 528l8 43h946l-8 -43h-946zM182 911l8 43h947l-9 -43h-946z" />
<glyph unicode="&#x3e;" horiz-adv-x="1323" d="M152 268l10 51l942 422v2l-793 422l8 47l855 -454l-7 -35z" />
<glyph unicode="?" horiz-adv-x="940" d="M270 43q0 28 19 47t47 19t46.5 -19t18.5 -47t-18.5 -47t-46.5 -19t-47 19t-19 47zM289 1409l8 51q105 45 221 45q188 0 314 -113.5t126 -297.5q0 -129 -67 -230.5t-177.5 -157.5t-246.5 -63l-49 -266h-43l53 307q127 0 235.5 46.5t178 141.5t69.5 220q0 163 -110 265.5 t-287 102.5q-117 0 -225 -51z" />
<glyph unicode="@" d="M100 442q0 159 59 309t161.5 264.5t251 183.5t315.5 69q193 0 350 -88t246.5 -243.5t89.5 -348.5q0 -127 -37.5 -234.5t-112.5 -175t-174 -67.5q-71 0 -126 34.5t-74 102.5h-5q-133 -139 -299 -139q-126 0 -206 87t-80 226q0 87 32.5 172.5t89 153t139.5 109.5t177 42 q146 0 274 -92l-75 -412q-11 -59 -11 -88q0 -74 45.5 -113.5t116.5 -39.5q87 0 152 62t96 157t31 207q0 134 -47 253t-130 204.5t-202.5 135.5t-257.5 50q-206 0 -377 -106t-268 -284.5t-97 -388.5q0 -191 83 -342t228.5 -234t327.5 -83q219 0 394 121l24 -39 q-189 -127 -418 -127q-145 0 -272 52.5t-218 145t-143.5 223.5t-52.5 281zM506 426q0 -123 67 -197.5t179 -74.5q80 0 158 42t130 109q0 31 11 90l71 389q-108 70 -229 70q-108 0 -198 -62.5t-139.5 -160.5t-49.5 -205z" />
<glyph unicode="A" horiz-adv-x="1353" d="M-51 0l876 1483h47l355 -1483h-49l-115 477h-778l-281 -477h-55zM311 522h742l-211 891h-4z" />
<glyph unicode="B" horiz-adv-x="1249" d="M115 0l262 1483h348q239 0 354 -88q121 -93 121 -258q0 -148 -94.5 -249t-237.5 -130v-4q127 -37 200 -127.5t73 -210.5q0 -80 -31.5 -154.5t-85.5 -128.5q-133 -133 -424 -133h-485zM170 45h434q267 0 385 115q48 47 76.5 113.5t28.5 138.5q0 139 -107.5 232t-267.5 93 h-426zM301 782h457q73 0 142.5 23.5t126 66.5t91.5 111.5t35 151.5q0 146 -109 227q-101 76 -329 76h-299z" />
<glyph unicode="C" horiz-adv-x="1384" d="M156 623q0 176 66.5 347t195.5 300q110 110 259 172.5t327 62.5q195 0 393 -88l-8 -49q-181 92 -388 92q-329 0 -548 -219q-118 -118 -184 -281.5t-66 -334.5q0 -271 168 -436.5t477 -165.5q198 0 352 71l-10 -51q-152 -66 -348 -66q-309 0 -497.5 172.5t-188.5 473.5z " />
<glyph unicode="D" horiz-adv-x="1488" d="M115 0l262 1483h311q384 0 578 -217q166 -187 166 -453q0 -135 -49 -274.5t-148 -255.5q-242 -283 -684 -283h-436zM170 45h383q425 0 649 268q84 101 133 231t49 265q0 121 -41 234t-118 198q-180 197 -537 197h-272z" />
<glyph unicode="E" horiz-adv-x="1134" d="M115 0l262 1483h790l-8 -45h-743l-115 -645h651l-8 -45h-651l-123 -703h760l-8 -45h-807z" />
<glyph unicode="F" horiz-adv-x="1138" d="M115 0l262 1483h797l-9 -45h-749l-119 -674h672l-8 -45h-672l-127 -719h-47z" />
<glyph unicode="G" horiz-adv-x="1478" d="M152 627q0 187 74 367t216 306q229 205 555 205q229 0 441 -110l-8 -49q-200 114 -439 114q-152 0 -284 -50.5t-232 -139.5q-135 -120 -205.5 -291.5t-70.5 -349.5q0 -273 164 -438.5t454 -165.5q200 0 383 77l92 529h-401l8 45h449l-107 -604q-199 -95 -426 -95 q-197 0 -347 78.5t-233 226.5t-83 345z" />
<glyph unicode="H" horiz-adv-x="1480" d="M115 0l262 1483h47l-121 -690h979l121 690h47l-262 -1483h-47l131 748h-979l-131 -748h-47z" />
<glyph unicode="I" horiz-adv-x="454" d="M115 0l262 1483h47l-262 -1483h-47z" />
<glyph unicode="J" horiz-adv-x="755" d="M-20 33l8 49q122 -59 221 -59q53 0 94 16.5t68.5 43.5t48.5 70t33.5 87.5t23.5 105.5l201 1137h47l-203 -1147q-31 -169 -78 -238q-40 -60 -103.5 -90.5t-137.5 -30.5q-109 0 -223 56z" />
<glyph unicode="K" horiz-adv-x="1204" d="M115 0l262 1483h47l-121 -680h2l899 680h68l-912 -688l705 -795h-61l-701 793h-2l-139 -793h-47z" />
<glyph unicode="L" horiz-adv-x="1089" d="M115 0l262 1483h47l-254 -1438h776l-8 -45h-823z" />
<glyph unicode="M" horiz-adv-x="1738" d="M115 0l262 1483h43l459 -910l778 910h51l-262 -1483h-47l243 1393h-4l-772 -901l-454 901h-4l-246 -1393h-47z" />
<glyph unicode="N" horiz-adv-x="1527" d="M115 0l262 1483h45l776 -1405h4l248 1405h47l-262 -1483h-45l-776 1405h-4l-248 -1405h-47z" />
<glyph unicode="O" d="M156 662q0 147 48 292t142 265q105 134 256 210t326 76q186 0 334.5 -85.5t233.5 -242t85 -356.5q0 -147 -48 -292t-142 -265q-105 -134 -256 -210.5t-326 -76.5q-186 0 -334.5 86t-233.5 242.5t-85 356.5zM203 666q0 -136 43.5 -254.5t122 -204t193.5 -135t251 -49.5 q163 0 301.5 69.5t237.5 194.5q87 110 134.5 248.5t47.5 281.5q0 136 -43.5 254.5t-122 204t-193.5 135t-251 49.5q-163 0 -301.5 -69.5t-237.5 -194.5q-87 -110 -134.5 -248.5t-47.5 -281.5z" />
<glyph unicode="P" horiz-adv-x="1232" d="M115 0l262 1483h364q272 0 400 -125q102 -99 102 -254q0 -179 -123 -305q-69 -71 -175 -107.5t-269 -36.5h-400l-114 -655h-47zM287 700h395q146 0 243 30t162 97q109 112 109 271q0 137 -90 227q-113 113 -365 113h-325z" />
<glyph unicode="Q" d="M156 662q0 147 48 292t142 265q105 134 256 210t326 76q186 0 334.5 -85.5t233.5 -242t85 -356.5q0 -147 -48 -292t-142 -265q-104 -133 -254.5 -209.5t-319.5 -77.5l-2 -4q255 -186 567 -286l-63 -31q-314 110 -590 326q-252 27 -412.5 214.5t-160.5 465.5zM203 666 q0 -136 43.5 -254.5t122 -204t193.5 -135t251 -49.5q163 0 301.5 69.5t237.5 194.5q87 110 134.5 248.5t47.5 281.5q0 136 -43.5 254.5t-122 204t-193.5 135t-251 49.5q-163 0 -301.5 -69.5t-237.5 -194.5q-87 -110 -134.5 -248.5t-47.5 -281.5z" />
<glyph unicode="R" horiz-adv-x="1259" d="M115 0l262 1483h356q260 0 379 -105q111 -97 111 -260q0 -167 -117 -284q-123 -123 -365 -136l338 -698h-53l-338 696h-403l-123 -696h-47zM293 741h393q266 0 385 119q105 105 105 256q0 144 -99 230q-109 92 -348 92h-313z" />
<glyph unicode="S" horiz-adv-x="1083" d="M53 137l10 53q83 -79 196.5 -123t229.5 -44q186 0 302 104q118 106 118 268q0 46 -10.5 84.5t-34 69.5t-48 54.5t-67 48t-75 40t-86.5 39.5q-52 23 -78 35t-71 35.5t-68.5 41.5t-54 47t-46 58t-26.5 68t-11 84q0 83 33.5 159t93.5 132q120 114 318 114q214 0 397 -137 l-8 -49q-81 67 -185 104t-206 37q-169 0 -279 -96q-116 -102 -116 -260q0 -31 6 -58.5t14 -49.5t25.5 -44t30.5 -37t40.5 -33.5t44 -29t51.5 -27.5t53 -25t58 -26q42 -19 60 -27t56 -27t56 -30.5t48 -32.5t45 -38t35 -43t29 -52t16 -59t7 -70q0 -184 -139 -305 q-129 -113 -328 -113q-120 0 -234.5 42.5t-201.5 117.5z" />
<glyph unicode="T" horiz-adv-x="1290" d="M272 1438l9 45h1075l-8 -45h-514l-254 -1438h-48l254 1438h-514z" />
<glyph unicode="U" horiz-adv-x="1437" d="M188 430q0 107 31 279l137 774h47l-137 -774q-30 -169 -30 -277q0 -193 103.5 -301t317.5 -108q291 0 433 206q97 142 151 453l141 801h45l-143 -813q-57 -322 -160 -469q-155 -224 -467 -224q-235 0 -352 121.5t-117 331.5z" />
<glyph unicode="V" horiz-adv-x="1263" d="M211 1483h49l295 -1411h4l789 1411h51l-830 -1483h-47z" />
<glyph unicode="W" horiz-adv-x="1961" d="M205 1483h47l190 -1411h4l687 1411h41l186 -1411h4l688 1411h51l-725 -1483h-55l-184 1391h-2l-676 -1391h-55z" />
<glyph unicode="X" horiz-adv-x="1277" d="M-14 0l673 770l-372 713h51l352 -676h4l588 676h55l-622 -715l399 -768h-51l-379 731h-4l-639 -731h-55z" />
<glyph unicode="Y" horiz-adv-x="1257" d="M215 1483h51l396 -768h4l665 768h58l-707 -813l-119 -670h-47l119 670z" />
<glyph unicode="Z" horiz-adv-x="1155" d="M-51 4l1225 1430l-3 4h-917l8 45h1010l2 -4l-1225 -1430l2 -4h965l-8 -45h-1057z" />
<glyph unicode="[" horiz-adv-x="634" d="M39 -436l346 1958h377l-8 -43h-332l-330 -1872h332l-8 -43h-377z" />
<glyph unicode="\" horiz-adv-x="866" d="M252 1561h47l397 -1639h-47z" />
<glyph unicode="]" horiz-adv-x="634" d="M-115 -436l9 43h331l330 1872h-332l8 43h377l-346 -1958h-377z" />
<glyph unicode="^" horiz-adv-x="1198" d="M223 741l529 764h45l262 -764h-51l-240 711h-2l-490 -711h-53z" />
<glyph unicode="_" horiz-adv-x="899" d="M-135 -258l8 43h899l-8 -43h-899z" />
<glyph unicode="`" horiz-adv-x="950" d="M485 1489h50l137 -336h-41z" />
<glyph unicode="a" horiz-adv-x="1193" d="M113 397q0 123 45 249t137 222q156 162 375 162q215 0 395 -141l-158 -889h-47l27 150l-4 2q-183 -175 -381 -175q-76 0 -144.5 26t-123.5 76t-88 132t-33 186zM160 399q0 -179 99 -277.5t247 -98.5q199 0 393 198l115 647q-162 117 -346 117q-205 0 -340 -147 q-80 -86 -124 -204t-44 -235z" />
<glyph unicode="b" horiz-adv-x="1173" d="M119 63l266 1502h47l-119 -662l2 -2q161 129 342 129q84 0 157.5 -29.5t129 -84.5t87.5 -138.5t32 -185.5q0 -118 -42 -235.5t-124 -206.5q-160 -173 -422 -173q-197 0 -356 86zM170 88q146 -65 305 -65q247 0 387 155q75 83 114.5 193t39.5 219q0 180 -100 287.5 t-263 107.5q-189 0 -350 -145z" />
<glyph unicode="c" horiz-adv-x="1034" d="M115 424q0 124 46 246.5t140 212.5q154 147 381 147q161 0 295 -82l-8 -47q-139 84 -291 84q-207 0 -344 -131q-81 -77 -126.5 -191.5t-45.5 -236.5q0 -186 106 -294.5t291 -108.5q79 0 159 19.5t136 53.5l-10 -53q-120 -66 -283 -66q-202 0 -324 120.5t-122 326.5z" />
<glyph unicode="d" horiz-adv-x="1193" d="M113 397q0 124 44 247.5t128 215.5q157 170 383 170q188 0 348 -121l4 2l117 654h47l-277 -1565h-47l27 150l-4 2q-183 -175 -381 -175q-76 0 -144.5 25.5t-124 75.5t-88 132.5t-32.5 186.5zM160 401q0 -181 98.5 -279.5t247.5 -98.5q199 0 393 198l113 635 q-159 129 -346 129q-208 0 -347 -154q-75 -85 -117 -199.5t-42 -230.5z" />
<glyph unicode="e" horiz-adv-x="1126" d="M113 434q0 105 34.5 210.5t102.5 191.5q155 194 379 194q179 0 285 -115.5t106 -308.5q0 -49 -10 -108h-846q-4 -40 -4 -62q0 -192 121 -302.5t313 -110.5q147 0 283 73l-9 -51q-50 -29 -126 -48.5t-156 -19.5q-96 0 -181 30t-150.5 86.5t-103.5 144.5t-38 196zM170 543 h799q4 32 4 61q0 74 -19.5 140t-58.5 121t-106.5 87.5t-155.5 32.5q-213 0 -348 -176q-91 -118 -115 -266z" />
<glyph unicode="f" horiz-adv-x="632" d="M129 965l8 43h187l53 305q14 82 37 130t59 81q69 63 180 63q63 0 154 -24l-8 -45q-86 24 -146 24q-95 0 -149 -51q-56 -54 -80 -184l-53 -299h303l-8 -43h-304l-169 -965h-48l170 965h-186z" />
<glyph unicode="g" horiz-adv-x="1177" d="M63 -377l9 47q170 -98 340 -98q164 0 277 93t147 282l34 203l-4 2q-173 -154 -360 -154q-78 0 -148 26.5t-125 77t-87.5 132t-32.5 182.5q0 120 44 240t132 212q153 162 385 162q200 0 377 -121l-170 -966q-21 -116 -68.5 -200t-113.5 -129.5t-137 -66t-152 -20.5 q-186 0 -347 96zM160 418q0 -176 97.5 -275.5t250.5 -99.5q192 0 375 176l116 668q-151 98 -325 98q-215 0 -352 -145q-78 -84 -120 -197t-42 -225z" />
<glyph unicode="h" horiz-adv-x="1191" d="M109 0l276 1565h47l-131 -742l4 -2q234 209 440 209q133 0 211 -76t78 -209q0 -49 -22 -184l-99 -561h-47l101 567q20 114 20 176q0 107 -62.5 174.5t-183.5 67.5q-111 0 -220 -59.5t-232 -171.5l-133 -754h-47z" />
<glyph unicode="i" horiz-adv-x="442" d="M109 0l178 1008h47l-178 -1008h-47zM317 1362q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -37 14.5t-15 36.5z" />
<glyph unicode="j" horiz-adv-x="444" d="M-252 -451l8 48q73 -25 123 -25q86 0 130.5 56t64.5 165l215 1215h47l-215 -1219q-22 -123 -64 -176q-33 -43 -83 -64.5t-101 -21.5q-65 0 -125 22zM319 1362q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -37 14.5t-15 36.5z" />
<glyph unicode="k" horiz-adv-x="978" d="M109 0l276 1565h47l-176 -1000h2l657 443h74l-672 -451l531 -557h-66l-526 555h-2l-98 -555h-47z" />
<glyph unicode="l" horiz-adv-x="442" d="M109 0l276 1565h47l-276 -1565h-47z" />
<glyph unicode="m" horiz-adv-x="1904" d="M109 0l178 1008h47l-33 -185l4 -2q107 99 211.5 154t216.5 55q119 0 193 -62.5t84 -172.5q120 116 231 175.5t225 59.5q129 0 204 -72.5t75 -199.5q0 -51 -21 -174l-102 -584h-47l104 588q19 108 19 168q0 102 -61 165.5t-175 63.5q-112 0 -221.5 -63.5t-228.5 -180.5 q-2 -44 -21 -157l-102 -584h-47l104 588q19 108 19 168q0 102 -61 165.5t-175 63.5q-111 0 -217 -59.5t-223 -171.5l-133 -754h-47z" />
<glyph unicode="n" horiz-adv-x="1196" d="M109 0l178 1008h47l-33 -185l4 -2q234 209 440 209q133 0 211 -76t78 -209q0 -49 -22 -184l-99 -561h-47l101 567q20 114 20 176q0 107 -62.5 174.5t-183.5 67.5q-111 0 -220 -59.5t-232 -171.5l-133 -754h-47z" />
<glyph unicode="o" horiz-adv-x="1202" d="M113 449q0 114 42 228.5t124 201.5q144 151 358 151q203 0 329 -133.5t126 -337.5q0 -114 -42 -228.5t-124 -201.5q-145 -152 -359 -152q-203 0 -328.5 134t-125.5 338zM160 451q0 -184 110.5 -306t296.5 -122q192 0 324 135q73 76 113 183t40 216q0 184 -110.5 306 t-296.5 122q-192 0 -324 -135q-73 -76 -113 -183t-40 -216z" />
<glyph unicode="p" horiz-adv-x="1181" d="M29 -451l258 1459h47l-19 -103l4 -2q161 127 338 127q178 0 296 -121.5t118 -322.5q0 -119 -42.5 -237.5t-125.5 -207.5q-154 -164 -389 -164q-192 0 -340 111l-4 -2l-94 -537h-47zM180 139q142 -116 334 -116q219 0 354 147q72 78 114 189.5t42 224.5q0 179 -102.5 290 t-268.5 111q-187 0 -350 -149z" />
<glyph unicode="q" horiz-adv-x="1181" d="M113 410q0 119 43.5 240t128.5 212q158 168 405 168q210 0 369 -102l-244 -1379h-47l100 570l-4 2q-164 -144 -350 -144q-170 0 -285.5 115.5t-115.5 317.5zM160 412q0 -179 99.5 -284t256.5 -105q192 0 365 163l127 719q-151 80 -318 80q-234 0 -373 -154 q-76 -84 -116.5 -196t-40.5 -223z" />
<glyph unicode="r" horiz-adv-x="694" d="M109 0l178 1008h47l-31 -177l2 -2q68 75 166 131t199 56q33 0 61 -4l-8 -45q-28 4 -59 4q-100 0 -196.5 -60.5t-178.5 -156.5l-133 -754h-47z" />
<glyph unicode="s" horiz-adv-x="952" d="M35 106l8 52q73 -65 185 -100t223 -35q151 0 235 69q88 71 88 176q0 29 -7 54t-22.5 45t-30.5 35.5t-42 29t-44.5 21.5t-50.5 17.5t-48 13.5t-48 12t-50.5 12.5t-49 13.5t-50.5 16.5t-45.5 20t-42.5 25t-34.5 30t-28.5 37.5t-17 45t-7 54q0 62 28.5 118t83.5 95 q95 67 250 67q213 0 375 -104l-8 -47q-164 106 -367 106q-139 0 -221 -57q-94 -68 -94 -178q0 -40 16 -71t39 -51t67.5 -38t80.5 -28t100 -25q33 -8 53.5 -13.5t57.5 -18t61 -25t53.5 -33.5t47.5 -45t30 -57.5t12 -72.5q0 -131 -106 -215q-101 -80 -271 -80q-112 0 -219 33 t-190 96z" />
<glyph unicode="t" horiz-adv-x="718" d="M121 965l8 43h217l51 292l49 7l-53 -299h352l-8 -43h-352l-106 -603q-19 -108 -19 -149q0 -94 51.5 -142t134.5 -48q73 0 129 26l-8 -49q-58 -20 -123 -20q-98 0 -164.5 57.5t-66.5 169.5q0 52 18 155l107 603h-217z" />
<glyph unicode="u" horiz-adv-x="1196" d="M162 262q0 49 22 184l99 562h47l-101 -568q-20 -114 -20 -176q0 -107 62.5 -174t183.5 -67q111 0 220 59.5t232 171.5l133 754h47l-178 -1008h-47l33 184l-4 2q-110 -98 -216.5 -153.5t-223.5 -55.5q-133 0 -211 76t-78 209z" />
<glyph unicode="v" horiz-adv-x="1069" d="M172 1008h47l234 -945h4l567 945h51l-606 -1008h-47z" />
<glyph unicode="w" horiz-adv-x="1648" d="M180 1008h47l168 -932h4l496 932h33l168 -932h4l495 932h52l-537 -1008h-47l-166 918h-2l-487 -918h-46z" />
<glyph unicode="x" horiz-adv-x="962" d="M-43 0l496 514l-303 494h51l284 -465h2l449 465h59l-481 -498l313 -510h-51l-295 481h-4l-463 -481h-57z" />
<glyph unicode="y" horiz-adv-x="1069" d="M66 -451l374 564l-274 895h47l258 -846h2l559 846h51l-964 -1459h-53z" />
<glyph unicode="z" horiz-adv-x="978" d="M-25 4l928 957l-2 4h-702l8 43h799l2 -4l-928 -957l2 -4h752l-9 -43h-848z" />
<glyph unicode="{" horiz-adv-x="679" d="M59 520l9 45h63q47 0 80 9.5t53 23.5t33.5 43t20 53t14.5 70l84 477q28 164 87.5 233.5t180.5 69.5q66 0 129 -18l-8 -43q-59 18 -123 18q-103 0 -150 -62.5t-71 -197.5l-86 -487q-17 -96 -48.5 -146.5t-101.5 -66.5v-2q82 -26 82 -131q0 -39 -12 -109l-76 -430 q-14 -83 -14 -129q0 -85 43 -120.5t139 -35.5q51 0 86 6l-8 -43q-35 -6 -78 -6q-117 0 -172 46.5t-55 152.5q0 45 14 131l78 440q10 62 10 92q0 58 -31.5 87.5t-111.5 29.5h-60z" />
<glyph unicode="|" horiz-adv-x="645" d="M131 -451l369 2089h45l-369 -2089h-45z" />
<glyph unicode="}" horiz-adv-x="679" d="M-119 -440l8 43q63 -19 123 -19q103 0 150 62.5t71 197.5l86 488q17 96 48.5 146.5t101.5 66.5v2q-82 26 -82 131q0 38 12 108l76 431q14 83 14 129q0 85 -43 120t-139 35q-51 0 -86 -6l8 43q35 6 78 6q118 0 173 -46t55 -152q0 -40 -15 -132l-78 -440q-10 -62 -10 -92 q0 -58 31.5 -87.5t111.5 -29.5h60l-8 -45h-64q-39 0 -68.5 -5.5t-50 -19t-33.5 -27t-22.5 -40t-14.5 -47.5t-12 -59l-83 -478q-28 -164 -88 -233.5t-181 -69.5q-62 0 -129 19z" />
<glyph unicode="~" horiz-adv-x="1107" d="M184 664l15 75q71 109 190 109q20 0 38 -2.5t37 -9t32 -11.5t33.5 -16.5t29 -16.5t31 -19.5t28.5 -18.5q40 -26 63.5 -39t58.5 -25t69 -12q122 0 197 143l4 -2l-15 -76q-71 -108 -190 -108q-20 0 -38 2.5t-37.5 9t-32 11t-34 17t-29 16.5t-31.5 19.5t-28 18.5 q-40 26 -63 39t-58 25t-69 12q-122 0 -197 -143z" />
<glyph unicode="&#xa1;" horiz-adv-x="649" d="M127 -475l197 1081h36l-184 -1081h-49zM342 965q0 28 19 46.5t47 18.5t46.5 -18.5t18.5 -46.5t-18.5 -47t-46.5 -19t-47 19t-19 47z" />
<glyph unicode="&#xa2;" horiz-adv-x="1048" d="M125 662q0 123 46 245.5t140 212.5q155 148 379 148l45 252h45l-45 -254q135 -8 252 -80l-8 -47q-139 84 -291 84q-207 0 -344 -131q-81 -77 -126.5 -191.5t-45.5 -236.5q0 -186 106 -295t291 -109q78 0 158.5 19.5t136.5 54.5l-10 -53q-120 -66 -283 -66h-20l-45 -252 h-45l45 256q-175 19 -278 136.5t-103 306.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1085" d="M57 0l6 35q134 115 212.5 260t78.5 307q0 44 -6 90h-168l8 43h154q-5 35 -16.5 100.5t-18 117.5t-6.5 96q0 200 127 328t315 128q98 0 189 -35.5t155 -87.5l-8 -51q-155 129 -334 129q-173 0 -285 -112t-112 -297q0 -44 6.5 -94.5t19 -119.5t17.5 -102h418l-8 -43h-404 q4 -58 4 -88q0 -300 -260 -555v-4h779l-9 -45h-854z" />
<glyph unicode="&#xa4;" horiz-adv-x="1200" d="M94 272l189 164q-76 107 -76 244q0 98 39 189t108 157l-110 139l37 29l108 -137q124 96 275 96q182 0 299 -123l188 164l29 -33l-189 -164q76 -107 76 -243q0 -98 -39 -189t-108 -157l110 -140l-37 -28l-108 137q-124 -96 -275 -96q-183 0 -299 122l-188 -163zM254 682 q0 -154 99.5 -255t256.5 -101q169 0 289.5 124t120.5 302q0 154 -99.5 255t-256.5 101q-169 0 -289.5 -124t-120.5 -302z" />
<glyph unicode="&#xa5;" horiz-adv-x="1300" d="M182 502l8 43h445l37 207l-72 137h-348l8 43h318l-285 551h51l354 -686h4l594 686h58l-480 -551h322l-8 -43h-350l-119 -137l-37 -207h444l-8 -43h-444l-88 -502h-47l88 502h-445z" />
<glyph unicode="&#xa6;" horiz-adv-x="645" d="M131 -451l141 803h45l-141 -803h-45zM358 836l142 802h45l-142 -802h-45z" />
<glyph unicode="&#xa7;" horiz-adv-x="1097" d="M119 -205l8 51q69 -52 154 -85t172 -33q130 0 212.5 72t82.5 196q0 32 -7 60t-16.5 49.5t-31 44t-40 38t-54 37t-62.5 36t-76 40.5q-59 30 -91.5 48.5t-80 53.5t-72.5 67t-44 79.5t-19 101.5q0 126 82.5 209t216.5 105v4q-111 90 -111 217q0 131 93 218t245 87 q155 0 297 -94l-8 -49q-60 43 -139 70.5t-156 27.5q-121 0 -203 -68.5t-82 -191.5q0 -92 62 -152t219 -139q60 -30 100.5 -53.5t85 -57.5t71 -67.5t43.5 -79.5t17 -98q0 -125 -78 -211t-217 -109v-4q103 -87 103 -217q0 -138 -96.5 -226.5t-249.5 -88.5q-177 0 -330 112z M201 653q0 -46 14 -84.5t34.5 -67t59 -57.5t73 -49t89.5 -49q137 -72 170 -94q127 6 213 84.5t86 198.5q0 47 -16 88t-39.5 71.5t-64 60.5t-77 51t-90.5 48q-104 51 -151 80q-129 -11 -215 -84t-86 -197z" />
<glyph unicode="&#xa8;" horiz-adv-x="950" d="M389 1319q0 23 17 39t40 16q22 0 37 -14.5t15 -36.5q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5zM741 1319q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -37 14.5t-15 36.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1689" d="M135 741q0 158 57 299t156 243t239 162t300 60t300 -60t238.5 -162t155.5 -243t57 -299t-57 -299t-155.5 -243t-238.5 -162t-300 -60t-300 60t-239 162t-156 243t-57 299zM182 741q0 -198 89 -362t251 -260t365 -96q152 0 284 56.5t223.5 153t144 228.5t52.5 280 q0 198 -88.5 362t-250.5 260.5t-365 96.5t-365 -96.5t-251 -260.5t-89 -362zM504 750q0 180 116.5 299.5t301.5 119.5q118 0 215 -51v-53q-101 59 -217 59q-163 0 -266 -106t-103 -268q0 -164 103.5 -267.5t263.5 -103.5q139 0 231 67v-53q-105 -59 -234 -59 q-181 0 -296 118.5t-115 297.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="800" d="M215 1102q0 75 27 147t74 129t117.5 92t150.5 35q135 0 250 -96l-101 -573h-45l12 69l-4 2q-94 -86 -215 -86q-116 0 -191 76.5t-75 204.5zM260 1104q0 -109 62 -174.5t161 -65.5q125 0 228 107l73 420q-95 71 -204 71q-136 0 -228 -109.5t-92 -248.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1073" d="M98 520l422 432h58l-426 -436l268 -461h-51zM479 520l422 432h57l-426 -436l269 -461h-51z" />
<glyph unicode="&#xac;" horiz-adv-x="1167" d="M147 721l9 43h946l-92 -526h-45l86 483h-904z" />
<glyph unicode="&#xad;" horiz-adv-x="741" d="M150 545l8 45h456l-8 -45h-456z" />
<glyph unicode="&#xae;" horiz-adv-x="755" d="M104 1124q0 164 108.5 272.5t274.5 108.5t274.5 -108.5t108.5 -272.5t-108.5 -272.5t-274.5 -108.5t-274.5 108.5t-108.5 272.5zM141 1124q0 -152 97 -249t249 -97t249.5 97t97.5 249t-97.5 249t-249.5 97t-249 -97t-97 -249zM358 918v413h127q72 0 112 -33.5t40 -89.5 q0 -45 -27 -76t-73 -42l127 -172h-48l-120 165h-101v-165h-37zM395 1118h94q54 0 81.5 24t27.5 64t-28.5 65t-80.5 25h-94v-178z" />
<glyph unicode="&#xaf;" horiz-adv-x="950" d="M354 1300l8 41h523l-8 -41h-523z" />
<glyph unicode="&#xb0;" horiz-adv-x="694" d="M170 1204q0 127 86 214t215 87t215 -87t86 -214t-86 -214t-215 -87t-215 87t-86 214zM211 1204q0 -113 74 -187.5t186 -74.5t186 74.5t74 187.5t-74 187.5t-186 74.5t-186 -74.5t-74 -187.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="1167" d="M86 199l8 43h889l-8 -43h-889zM211 907l8 43h422l74 420h45l-74 -420h422l-8 -43h-422l-74 -420h-45l74 420h-422z" />
<glyph unicode="&#xb2;" horiz-adv-x="702" d="M109 868l395 353q49 44 81.5 76t67.5 75.5t52.5 88.5t17.5 91q0 85 -58 131t-145 46q-121 0 -207 -76l11 55q88 59 196 59q102 0 173 -54.5t71 -158.5q0 -51 -17.5 -99t-53 -94t-70 -81t-86.5 -82l-328 -291l2 -4h477l-6 -39h-571z" />
<glyph unicode="&#xb3;" horiz-adv-x="665" d="M162 932l8 47q98 -88 238 -88q113 0 184 59q76 65 76 158q0 87 -69.5 135.5t-166.5 48.5h-82l6 39h84q132 0 207 82q55 58 55 137q0 82 -58 130.5t-152 48.5q-93 0 -166 -48l8 48q65 38 158 38q106 0 178.5 -56t72.5 -157q0 -92 -59 -157q-52 -59 -137 -84v-4 q73 -19 118.5 -72.5t45.5 -126.5q0 -111 -93 -188q-85 -72 -215 -72q-139 0 -241 82z" />
<glyph unicode="&#xb4;" horiz-adv-x="950" d="M508 1153l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xb5;" horiz-adv-x="1243" d="M236 -451v1459h47v-590q0 -229 79 -314q75 -81 224 -81q212 0 422 200v785h47v-1008h-47v162l-4 2q-203 -187 -424 -187q-98 0 -176.5 39t-120.5 123h-2l4 -590h-49z" />
<glyph unicode="&#xb6;" horiz-adv-x="1071" d="M250 1065q0 103 56 199.5t153 157.5t207 61h313l-340 -1934h-45l332 1885h-203l-332 -1885h-45l211 1192q-134 7 -220.5 99t-86.5 225z" />
<glyph unicode="&#xb7;" horiz-adv-x="614" d="M262 618q0 28 19 47t47 19t46.5 -19t18.5 -47t-18.5 -46.5t-46.5 -18.5t-47 18.5t-19 46.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="950" d="M190 -408l21 43q74 -45 131 -45q55 0 98 33t43 92q0 103 -168 138l80 170l39 -13l-61 -129q157 -43 157 -162q0 -74 -52 -123t-136 -49q-76 0 -152 45z" />
<glyph unicode="&#xb9;" horiz-adv-x="641" d="M311 1614l8 43l304 110l-160 -903h-41l149 844z" />
<glyph unicode="&#xba;" horiz-adv-x="854" d="M215 1133q0 154 100.5 263t253.5 109q134 0 217.5 -86.5t83.5 -224.5q0 -154 -101 -263.5t-253 -109.5q-133 0 -217 87t-84 225zM258 1139q0 -118 71.5 -197.5t188.5 -79.5q129 0 219 95.5t90 230.5q0 118 -71.5 197t-188.5 79q-129 0 -219 -95t-90 -230z" />
<glyph unicode="&#xbb;" horiz-adv-x="1073" d="M115 55l426 437l-269 460h52l270 -465l-422 -432h-57zM496 55l426 437l-269 460h52l270 -465l-422 -432h-57z" />
<glyph unicode="&#xbc;" horiz-adv-x="1855" d="M264 -27l1254 1510h55l-1254 -1510h-55zM281 1352l8 43l303 110l-160 -903h-41l150 844zM979 254l600 635h41l-107 -600h199l-6 -39h-199l-45 -250h-41l45 250h-485zM1067 289h406l92 520l-4 2z" />
<glyph unicode="&#xbd;" horiz-adv-x="1941" d="M264 -27l1254 1510h55l-1254 -1510h-55zM281 1352l8 43l303 110l-160 -903h-41l150 844zM1126 4l396 352q49 43 81.5 75.5t67.5 76.5t52.5 89t17.5 91q0 85 -58 130.5t-145 45.5q-121 0 -207 -76l10 56q88 59 197 59q102 0 173 -54.5t71 -158.5q0 -51 -17.5 -99t-53 -94 t-70 -81t-87.5 -82l-327 -291l2 -4h477l-6 -39h-572z" />
<glyph unicode="&#xbe;" horiz-adv-x="1959" d="M180 670l8 47q98 -88 238 -88q113 0 184 59q76 65 76 158q0 87 -69.5 135.5t-165.5 48.5h-82l6 39h84q132 0 207 82q55 58 55 137q0 82 -58.5 130t-152.5 48q-95 0 -166 -47l8 47q67 39 158 39q106 0 179 -56t73 -157q0 -91 -60 -157q-52 -59 -137 -84v-4 q73 -19 118.5 -72.5t45.5 -126.5q0 -54 -24.5 -104t-67.5 -85q-84 -71 -215 -71q-140 0 -242 82zM369 -27l1253 1510h55l-1253 -1510h-55zM1083 254l600 635h41l-106 -600h199l-7 -39h-198l-45 -250h-41l45 250h-486zM1171 289h406l92 520l-4 2z" />
<glyph unicode="&#xbf;" horiz-adv-x="940" d="M-8 -86q0 129 67 230.5t177.5 157.5t246.5 63l49 266h43l-53 -307q-127 0 -235.5 -46.5t-178 -141.5t-69.5 -220q0 -163 110 -266t287 -103q116 0 226 52l-9 -52q-105 -45 -221 -45q-188 0 -314 114t-126 298zM549 965q0 28 18.5 46.5t46.5 18.5t47 -18.5t19 -46.5 t-19 -47t-47 -19t-46.5 19t-18.5 47z" />
<glyph unicode="&#xc0;" horiz-adv-x="1353" d="M-51 0l876 1483h47l355 -1483h-49l-115 477h-778l-281 -477h-55zM311 522h742l-211 891h-4zM770 1964h49l137 -336h-41z" />
<glyph unicode="&#xc1;" horiz-adv-x="1353" d="M-51 0l876 1483h47l355 -1483h-49l-115 477h-778l-281 -477h-55zM311 522h742l-211 891h-4zM793 1628l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xc2;" horiz-adv-x="1353" d="M-51 0l876 1483h47l355 -1483h-49l-115 477h-778l-281 -477h-55zM311 522h742l-211 891h-4zM604 1628l305 336h49l187 -336h-41l-174 303l-277 -303h-49z" />
<glyph unicode="&#xc3;" horiz-adv-x="1353" d="M-51 0l876 1483h47l355 -1483h-49l-115 477h-778l-281 -477h-55zM311 522h742l-211 891h-4zM604 1735l10 61q63 88 154 88q39 0 72.5 -16t81.5 -49q41 -28 67.5 -41t57.5 -13q91 0 155 109h4l-10 -64q-61 -86 -154 -86q-27 0 -54.5 10t-45 21t-51.5 35q-42 29 -69 41 t-58 12q-89 0 -156 -108h-4z" />
<glyph unicode="&#xc4;" horiz-adv-x="1353" d="M-51 0l876 1483h47l355 -1483h-49l-115 477h-778l-281 -477h-55zM311 522h742l-211 891h-4zM674 1794q0 23 17 39t40 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -36.5 14.5t-14.5 36.5zM1026 1794q0 23 17 39t40 16q22 0 37 -14.5t15 -36.5 q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1353" d="M-51 0l876 1483h47l355 -1483h-49l-115 477h-778l-281 -477h-55zM311 522h742l-211 891h-4zM696 1821q0 96 66 165.5t164 69.5q89 0 144.5 -57.5t55.5 -145.5q0 -96 -65.5 -165.5t-163.5 -69.5q-89 0 -145 57.5t-56 145.5zM737 1825q0 -71 43 -119.5t117 -48.5 q78 0 133 55.5t55 136.5q0 71 -42.5 119.5t-116.5 48.5q-79 0 -134 -55.5t-55 -136.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1843" d="M-76 0l1172 1483h780l-8 -45h-701l-114 -645h608l-8 -45h-609l-122 -703h716l-8 -45h-764l84 477h-592l-376 -477h-58zM393 522h565l162 916h-4z" />
<glyph unicode="&#xc7;" horiz-adv-x="1384" d="M156 623q0 176 66.5 347t195.5 300q110 110 259 172.5t327 62.5q195 0 393 -88l-8 -49q-181 92 -388 92q-329 0 -548 -219q-118 -118 -184 -281.5t-66 -334.5q0 -271 168 -436.5t477 -165.5q198 0 352 71l-10 -51q-152 -66 -348 -66q-22 0 -72 3l-47 -99 q158 -43 158 -162q0 -74 -52.5 -123t-136.5 -49q-75 0 -151 45l20 43q74 -45 131 -45q55 0 98.5 33t43.5 92q0 103 -168 138l61 131q-263 33 -417 200t-154 439z" />
<glyph unicode="&#xc8;" horiz-adv-x="1134" d="M115 0l262 1483h790l-8 -45h-743l-115 -645h651l-8 -45h-651l-123 -703h760l-8 -45h-807zM690 1964h49l138 -336h-41z" />
<glyph unicode="&#xc9;" horiz-adv-x="1134" d="M115 0l262 1483h790l-8 -45h-743l-115 -645h651l-8 -45h-651l-123 -703h760l-8 -45h-807zM713 1628l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xca;" horiz-adv-x="1134" d="M115 0l262 1483h790l-8 -45h-743l-115 -645h651l-8 -45h-651l-123 -703h760l-8 -45h-807zM524 1628l305 336h50l186 -336h-41l-174 303l-277 -303h-49z" />
<glyph unicode="&#xcb;" horiz-adv-x="1134" d="M115 0l262 1483h790l-8 -45h-743l-115 -645h651l-8 -45h-651l-123 -703h760l-8 -45h-807zM594 1794q0 23 17 39t40 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -36.5 14.5t-14.5 36.5zM946 1794q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5 q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="454" d="M115 0l262 1483h47l-262 -1483h-47zM322 1964h49l137 -336h-41z" />
<glyph unicode="&#xcd;" horiz-adv-x="454" d="M115 0l262 1483h47l-262 -1483h-47zM344 1628l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xce;" horiz-adv-x="454" d="M115 0l262 1483h47l-262 -1483h-47zM156 1628l305 336h49l186 -336h-41l-174 303l-276 -303h-49z" />
<glyph unicode="&#xcf;" horiz-adv-x="454" d="M115 0l262 1483h47l-262 -1483h-47zM225 1794q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5zM578 1794q0 23 17 39t40 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1513" d="M82 733l8 43h186l125 707h312q383 0 577 -217q166 -187 166 -453q0 -135 -49 -274.5t-147 -255.5q-242 -283 -685 -283h-436l129 733h-186zM195 45h383q425 0 649 268q84 101 133 231t49 265q0 121 -41.5 233.5t-118.5 198.5q-180 197 -536 197h-273l-116 -662h389 l-8 -43h-390z" />
<glyph unicode="&#xd1;" horiz-adv-x="1527" d="M115 0l262 1483h45l776 -1405h4l248 1405h47l-262 -1483h-45l-776 1405h-4l-248 -1405h-47zM692 1735l10 61q63 88 154 88q39 0 72.5 -16t81.5 -49q41 -28 67.5 -41t57.5 -13q91 0 155 109h4l-10 -64q-61 -86 -154 -86q-27 0 -54.5 10t-45 21t-51.5 35q-42 29 -69 41 t-58 12q-89 0 -156 -108h-4z" />
<glyph unicode="&#xd2;" d="M156 662q0 147 48 292t142 265q105 134 256 210t326 76q186 0 334.5 -85.5t233.5 -242t85 -356.5q0 -147 -48 -292t-142 -265q-105 -134 -256 -210.5t-326 -76.5q-186 0 -334.5 86t-233.5 242.5t-85 356.5zM203 666q0 -136 43.5 -254.5t122 -204t193.5 -135t251 -49.5 q163 0 301.5 69.5t237.5 194.5q87 110 134.5 248.5t47.5 281.5q0 136 -43.5 254.5t-122 204t-193.5 135t-251 49.5q-163 0 -301.5 -69.5t-237.5 -194.5q-87 -110 -134.5 -248.5t-47.5 -281.5zM920 1964h49l137 -336h-41z" />
<glyph unicode="&#xd3;" d="M156 662q0 147 48 292t142 265q105 134 256 210t326 76q186 0 334.5 -85.5t233.5 -242t85 -356.5q0 -147 -48 -292t-142 -265q-105 -134 -256 -210.5t-326 -76.5q-186 0 -334.5 86t-233.5 242.5t-85 356.5zM203 666q0 -136 43.5 -254.5t122 -204t193.5 -135t251 -49.5 q163 0 301.5 69.5t237.5 194.5q87 110 134.5 248.5t47.5 281.5q0 136 -43.5 254.5t-122 204t-193.5 135t-251 49.5q-163 0 -301.5 -69.5t-237.5 -194.5q-87 -110 -134.5 -248.5t-47.5 -281.5zM942 1628l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xd4;" d="M156 662q0 147 48 292t142 265q105 134 256 210t326 76q186 0 334.5 -85.5t233.5 -242t85 -356.5q0 -147 -48 -292t-142 -265q-105 -134 -256 -210.5t-326 -76.5q-186 0 -334.5 86t-233.5 242.5t-85 356.5zM203 666q0 -136 43.5 -254.5t122 -204t193.5 -135t251 -49.5 q163 0 301.5 69.5t237.5 194.5q87 110 134.5 248.5t47.5 281.5q0 136 -43.5 254.5t-122 204t-193.5 135t-251 49.5q-163 0 -301.5 -69.5t-237.5 -194.5q-87 -110 -134.5 -248.5t-47.5 -281.5zM754 1628l305 336h49l186 -336h-41l-174 303l-276 -303h-49z" />
<glyph unicode="&#xd5;" d="M156 662q0 147 48 292t142 265q105 134 256 210t326 76q186 0 334.5 -85.5t233.5 -242t85 -356.5q0 -147 -48 -292t-142 -265q-105 -134 -256 -210.5t-326 -76.5q-186 0 -334.5 86t-233.5 242.5t-85 356.5zM203 666q0 -136 43.5 -254.5t122 -204t193.5 -135t251 -49.5 q163 0 301.5 69.5t237.5 194.5q87 110 134.5 248.5t47.5 281.5q0 136 -43.5 254.5t-122 204t-193.5 135t-251 49.5q-163 0 -301.5 -69.5t-237.5 -194.5q-87 -110 -134.5 -248.5t-47.5 -281.5zM754 1735l10 61q63 88 154 88q39 0 72 -15.5t81 -49.5q41 -28 67.5 -41t57.5 -13 q92 0 156 109h4l-10 -64q-61 -86 -154 -86q-27 0 -54.5 10t-45.5 21t-52 35q-42 29 -69 41t-58 12q-88 0 -155 -108h-4z" />
<glyph unicode="&#xd6;" d="M156 662q0 147 48 292t142 265q105 134 256 210t326 76q186 0 334.5 -85.5t233.5 -242t85 -356.5q0 -147 -48 -292t-142 -265q-105 -134 -256 -210.5t-326 -76.5q-186 0 -334.5 86t-233.5 242.5t-85 356.5zM203 666q0 -136 43.5 -254.5t122 -204t193.5 -135t251 -49.5 q163 0 301.5 69.5t237.5 194.5q87 110 134.5 248.5t47.5 281.5q0 136 -43.5 254.5t-122 204t-193.5 135t-251 49.5q-163 0 -301.5 -69.5t-237.5 -194.5q-87 -110 -134.5 -248.5t-47.5 -281.5zM823 1794q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-41 -16 q-22 0 -36.5 14.5t-14.5 36.5zM1176 1794q0 23 17 39t40 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1167" d="M156 369l440 372l-305 373l33 27l305 -373l440 373l27 -31l-441 -373l306 -372l-33 -27l-305 373l-441 -373z" />
<glyph unicode="&#xd8;" d="M102 12l201 199q-147 187 -147 451q0 147 48 292t142 265q105 134 256 210t326 76q143 0 265.5 -52t211.5 -148l199 196l30 -31l-200 -198q147 -187 147 -451q0 -147 -48 -292t-142 -265q-105 -134 -256 -210.5t-326 -76.5q-143 0 -265.5 52.5t-211.5 148.5l-199 -196z M203 666q0 -250 133 -422l1036 1028q-81 89 -196 138.5t-252 49.5q-163 0 -301.5 -69.5t-237.5 -194.5q-87 -110 -134.5 -248.5t-47.5 -281.5zM365 211q81 -89 196 -138.5t252 -49.5q163 0 301.5 69.5t237.5 194.5q87 110 134.5 248.5t47.5 281.5q0 250 -133 422z" />
<glyph unicode="&#xd9;" horiz-adv-x="1437" d="M188 430q0 107 31 279l137 774h47l-137 -774q-30 -169 -30 -277q0 -193 103.5 -301t317.5 -108q291 0 433 206q97 142 151 453l141 801h45l-143 -813q-57 -322 -160 -469q-155 -224 -467 -224q-235 0 -352 121.5t-117 331.5zM813 1964h49l137 -336h-41z" />
<glyph unicode="&#xda;" horiz-adv-x="1437" d="M188 430q0 107 31 279l137 774h47l-137 -774q-30 -169 -30 -277q0 -193 103.5 -301t317.5 -108q291 0 433 206q97 142 151 453l141 801h45l-143 -813q-57 -322 -160 -469q-155 -224 -467 -224q-235 0 -352 121.5t-117 331.5zM836 1628l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xdb;" horiz-adv-x="1437" d="M188 430q0 107 31 279l137 774h47l-137 -774q-30 -169 -30 -277q0 -193 103.5 -301t317.5 -108q291 0 433 206q97 142 151 453l141 801h45l-143 -813q-57 -322 -160 -469q-155 -224 -467 -224q-235 0 -352 121.5t-117 331.5zM647 1628l305 336h49l187 -336h-41l-174 303 l-277 -303h-49z" />
<glyph unicode="&#xdc;" horiz-adv-x="1437" d="M188 430q0 107 31 279l137 774h47l-137 -774q-30 -169 -30 -277q0 -193 103.5 -301t317.5 -108q291 0 433 206q97 142 151 453l141 801h45l-143 -813q-57 -322 -160 -469q-155 -224 -467 -224q-235 0 -352 121.5t-117 331.5zM717 1794q0 23 17 39t40 16q22 0 36.5 -14.5 t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -36.5 14.5t-14.5 36.5zM1069 1794q0 23 17 39t40 16q22 0 37 -14.5t15 -36.5q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1257" d="M215 1483h51l396 -768h4l665 768h58l-707 -813l-119 -670h-47l119 670zM748 1628l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xde;" horiz-adv-x="1232" d="M115 0l262 1483h47l-57 -326h317q271 0 399 -125q103 -100 103 -254q0 -179 -123 -305q-69 -71 -175 -107t-270 -36h-399l-57 -330h-47zM229 375h396q146 0 243 30t162 97q109 112 109 270q0 137 -90 227q-113 113 -365 113h-326z" />
<glyph unicode="&#xdf;" horiz-adv-x="1083" d="M109 0l213 1202q36 201 141 289q114 96 309 96q181 0 266 -82q68 -65 68 -178q0 -109 -68 -248q-66 8 -108 8q-53 0 -105.5 -8t-106.5 -28t-95 -50t-67 -78.5t-26 -109.5q0 -47 14 -80.5t50.5 -61.5t71.5 -45.5t106 -47.5q43 -18 65.5 -28t57.5 -28t53 -32t41 -36.5 t33.5 -46t18.5 -55.5t8 -69q0 -139 -107 -224q-100 -79 -270 -79q-190 0 -338 96l12 51q70 -50 158 -76t174 -26q151 0 235 69q88 71 88 187q0 43 -12 76.5t-29.5 57t-52.5 46t-66 37.5t-85 37q-49 20 -75 31.5t-63.5 31.5t-57 38.5t-40 44.5t-29 58t-8.5 72q0 82 39.5 145 t105.5 100t142.5 55.5t159.5 18.5q40 0 80 -4q49 108 49 193q0 96 -55 151q-72 72 -236 72q-184 0 -279 -86q-91 -82 -122 -260l-211 -1196h-47z" />
<glyph unicode="&#xe0;" horiz-adv-x="1193" d="M113 397q0 123 45 249t137 222q156 162 375 162q215 0 395 -141l-158 -889h-47l27 150l-4 2q-183 -175 -381 -175q-76 0 -144.5 26t-123.5 76t-88 132t-33 186zM160 399q0 -179 99 -277.5t247 -98.5q199 0 393 198l115 647q-162 117 -346 117q-205 0 -340 -147 q-80 -86 -124 -204t-44 -235zM618 1489h50l137 -336h-41z" />
<glyph unicode="&#xe1;" horiz-adv-x="1193" d="M113 397q0 123 45 249t137 222q156 162 375 162q215 0 395 -141l-158 -889h-47l27 150l-4 2q-183 -175 -381 -175q-76 0 -144.5 26t-123.5 76t-88 132t-33 186zM160 399q0 -179 99 -277.5t247 -98.5q199 0 393 198l115 647q-162 117 -346 117q-205 0 -340 -147 q-80 -86 -124 -204t-44 -235zM641 1153l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xe2;" horiz-adv-x="1193" d="M113 397q0 123 45 249t137 222q156 162 375 162q215 0 395 -141l-158 -889h-47l27 150l-4 2q-183 -175 -381 -175q-76 0 -144.5 26t-123.5 76t-88 132t-33 186zM160 399q0 -179 99 -277.5t247 -98.5q199 0 393 198l115 647q-162 117 -346 117q-205 0 -340 -147 q-80 -86 -124 -204t-44 -235zM453 1153l305 336h49l186 -336h-41l-174 303l-276 -303h-49z" />
<glyph unicode="&#xe3;" horiz-adv-x="1193" d="M113 397q0 123 45 249t137 222q156 162 375 162q215 0 395 -141l-158 -889h-47l27 150l-4 2q-183 -175 -381 -175q-76 0 -144.5 26t-123.5 76t-88 132t-33 186zM160 399q0 -179 99 -277.5t247 -98.5q199 0 393 198l115 647q-162 117 -346 117q-205 0 -340 -147 q-80 -86 -124 -204t-44 -235zM453 1260l10 61q63 88 153 88q39 0 72 -16t82 -50q40 -28 67 -40.5t58 -12.5q92 0 156 109h4l-11 -64q-61 -86 -153 -86q-27 0 -54.5 10t-45.5 21t-52 35q-42 29 -69 41t-58 12q-88 0 -155 -108h-4z" />
<glyph unicode="&#xe4;" horiz-adv-x="1193" d="M113 397q0 123 45 249t137 222q156 162 375 162q215 0 395 -141l-158 -889h-47l27 150l-4 2q-183 -175 -381 -175q-76 0 -144.5 26t-123.5 76t-88 132t-33 186zM160 399q0 -179 99 -277.5t247 -98.5q199 0 393 198l115 647q-162 117 -346 117q-205 0 -340 -147 q-80 -86 -124 -204t-44 -235zM522 1319q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5zM874 1319q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -37 14.5t-15 36.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1193" d="M113 397q0 123 45 249t137 222q156 162 375 162q215 0 395 -141l-158 -889h-47l27 150l-4 2q-183 -175 -381 -175q-76 0 -144.5 26t-123.5 76t-88 132t-33 186zM160 399q0 -179 99 -277.5t247 -98.5q199 0 393 198l115 647q-162 117 -346 117q-205 0 -340 -147 q-80 -86 -124 -204t-44 -235zM545 1346q0 96 65.5 165.5t163.5 69.5q89 0 145 -57.5t56 -145.5q0 -96 -66 -165.5t-164 -69.5q-89 0 -144.5 57.5t-55.5 145.5zM586 1350q0 -71 42.5 -119.5t116.5 -48.5q79 0 134 55.5t55 136.5q0 71 -43 119.5t-117 48.5q-78 0 -133 -55.5 t-55 -136.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1892" d="M113 416q0 118 42.5 237t125.5 209q155 168 409 168q185 0 340 -90l-26 -152l4 -2q66 109 170 176.5t235 67.5q169 0 271 -115.5t102 -308.5q0 -49 -10 -108h-822q-2 -20 -2 -62q0 -195 115.5 -304t302.5 -109q142 0 272 73l-8 -51q-48 -29 -122 -48.5t-152 -19.5 q-133 0 -241 57.5t-163 166.5q-86 -104 -200.5 -164t-235.5 -60q-175 0 -291 119.5t-116 319.5zM160 418q0 -179 99.5 -287t262.5 -108q122 0 229 60t187 159q-33 79 -33 190q0 59 10 115l66 368q-140 70 -291 70q-239 0 -377 -154q-74 -81 -113.5 -191.5t-39.5 -221.5z M961 543h774q4 32 4 59q0 77 -19 144t-57 121.5t-103 86t-149 31.5q-204 0 -336 -176q-86 -111 -114 -266z" />
<glyph unicode="&#xe7;" horiz-adv-x="1034" d="M115 424q0 124 46 246.5t140 212.5q154 147 381 147q161 0 295 -82l-8 -47q-139 84 -291 84q-207 0 -344 -131q-81 -77 -126.5 -191.5t-45.5 -236.5q0 -186 106 -294.5t291 -108.5q79 0 159 19.5t136 53.5l-10 -53q-120 -66 -283 -66q-22 0 -43 3l-45 -99 q158 -43 158 -162q0 -74 -52.5 -123t-136.5 -49q-75 0 -151 45l20 43q74 -45 131 -45q55 0 98.5 33t43.5 92q0 103 -168 138l59 131q-166 25 -263 141.5t-97 298.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1126" d="M113 434q0 105 34.5 210.5t102.5 191.5q155 194 379 194q179 0 285 -115.5t106 -308.5q0 -49 -10 -108h-846q-4 -40 -4 -62q0 -192 121 -302.5t313 -110.5q147 0 283 73l-9 -51q-50 -29 -126 -48.5t-156 -19.5q-96 0 -181 30t-150.5 86.5t-103.5 144.5t-38 196zM170 543 h799q4 32 4 61q0 74 -19.5 140t-58.5 121t-106.5 87.5t-155.5 32.5q-213 0 -348 -176q-91 -118 -115 -266zM586 1489h49l137 -336h-41z" />
<glyph unicode="&#xe9;" horiz-adv-x="1126" d="M113 434q0 105 34.5 210.5t102.5 191.5q155 194 379 194q179 0 285 -115.5t106 -308.5q0 -49 -10 -108h-846q-4 -40 -4 -62q0 -192 121 -302.5t313 -110.5q147 0 283 73l-9 -51q-50 -29 -126 -48.5t-156 -19.5q-96 0 -181 30t-150.5 86.5t-103.5 144.5t-38 196zM170 543 h799q4 32 4 61q0 74 -19.5 140t-58.5 121t-106.5 87.5t-155.5 32.5q-213 0 -348 -176q-91 -118 -115 -266zM608 1153l256 336h54l-265 -336h-45z" />
<glyph unicode="&#xea;" horiz-adv-x="1126" d="M113 434q0 105 34.5 210.5t102.5 191.5q155 194 379 194q179 0 285 -115.5t106 -308.5q0 -49 -10 -108h-846q-4 -40 -4 -62q0 -192 121 -302.5t313 -110.5q147 0 283 73l-9 -51q-50 -29 -126 -48.5t-156 -19.5q-96 0 -181 30t-150.5 86.5t-103.5 144.5t-38 196zM170 543 h799q4 32 4 61q0 74 -19.5 140t-58.5 121t-106.5 87.5t-155.5 32.5q-213 0 -348 -176q-91 -118 -115 -266zM420 1153l305 336h49l187 -336h-41l-175 303l-276 -303h-49z" />
<glyph unicode="&#xeb;" horiz-adv-x="1126" d="M113 434q0 105 34.5 210.5t102.5 191.5q155 194 379 194q179 0 285 -115.5t106 -308.5q0 -49 -10 -108h-846q-4 -40 -4 -62q0 -192 121 -302.5t313 -110.5q147 0 283 73l-9 -51q-50 -29 -126 -48.5t-156 -19.5q-96 0 -181 30t-150.5 86.5t-103.5 144.5t-38 196zM170 543 h799q4 32 4 61q0 74 -19.5 140t-58.5 121t-106.5 87.5t-155.5 32.5q-213 0 -348 -176q-91 -118 -115 -266zM489 1319q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -37 14.5t-15 36.5zM842 1319q0 23 17 39t40 16q22 0 36.5 -14.5t14.5 -36.5 q0 -23 -17 -39t-40 -16q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#xec;" horiz-adv-x="442" d="M109 0l178 1008h47l-178 -1008h-47zM231 1489h50l137 -336h-41z" />
<glyph unicode="&#xed;" horiz-adv-x="442" d="M109 0l178 1008h47l-178 -1008h-47zM254 1153l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xee;" horiz-adv-x="442" d="M66 1153l305 336h49l186 -336h-41l-174 303l-276 -303h-49zM109 0l178 1008h47l-178 -1008h-47z" />
<glyph unicode="&#xef;" horiz-adv-x="442" d="M109 0l178 1008h47l-178 -1008h-47zM135 1319q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5zM487 1319q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -37 14.5t-15 36.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1257" d="M135 440q0 116 42 224t122 186q70 68 161.5 106.5t190.5 38.5q138 0 251.5 -71.5t156.5 -194.5l6 2q-37 331 -223 557l-318 -112l-4 45l291 102q-104 113 -238 195l31 32q154 -102 252 -211l221 78l4 -45l-194 -69q225 -277 225 -664q0 -327 -166 -504 q-148 -158 -366 -158q-195 0 -320 128.5t-125 334.5zM182 444q0 -182 108.5 -300.5t291.5 -118.5q196 0 329 139q71 75 107.5 174t36.5 199q0 176 -109 294.5t-293 118.5q-187 0 -319 -129q-74 -73 -113 -173t-39 -204z" />
<glyph unicode="&#xf1;" horiz-adv-x="1196" d="M109 0l178 1008h47l-33 -185l4 -2q234 209 440 209q133 0 211 -76t78 -209q0 -49 -22 -184l-99 -561h-47l101 567q20 114 20 176q0 107 -62.5 174.5t-183.5 67.5q-111 0 -220 -59.5t-232 -171.5l-133 -754h-47zM442 1260l11 61q63 88 153 88q39 0 72 -16t82 -50 q40 -28 67 -40.5t58 -12.5q91 0 155 109h4l-10 -64q-61 -86 -153 -86q-27 0 -54.5 10t-45.5 21t-52 35q-42 29 -69 41t-58 12q-89 0 -156 -108h-4z" />
<glyph unicode="&#xf2;" horiz-adv-x="1202" d="M113 449q0 114 42 228.5t124 201.5q144 151 358 151q203 0 329 -133.5t126 -337.5q0 -114 -42 -228.5t-124 -201.5q-145 -152 -359 -152q-203 0 -328.5 134t-125.5 338zM160 451q0 -184 110.5 -306t296.5 -122q192 0 324 135q73 76 113 183t40 216q0 184 -110.5 306 t-296.5 122q-192 0 -324 -135q-73 -76 -113 -183t-40 -216zM610 1489h49l138 -336h-41z" />
<glyph unicode="&#xf3;" horiz-adv-x="1202" d="M113 449q0 114 42 228.5t124 201.5q144 151 358 151q203 0 329 -133.5t126 -337.5q0 -114 -42 -228.5t-124 -201.5q-145 -152 -359 -152q-203 0 -328.5 134t-125.5 338zM160 451q0 -184 110.5 -306t296.5 -122q192 0 324 135q73 76 113 183t40 216q0 184 -110.5 306 t-296.5 122q-192 0 -324 -135q-73 -76 -113 -183t-40 -216zM633 1153l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xf4;" horiz-adv-x="1202" d="M113 449q0 114 42 228.5t124 201.5q144 151 358 151q203 0 329 -133.5t126 -337.5q0 -114 -42 -228.5t-124 -201.5q-145 -152 -359 -152q-203 0 -328.5 134t-125.5 338zM160 451q0 -184 110.5 -306t296.5 -122q192 0 324 135q73 76 113 183t40 216q0 184 -110.5 306 t-296.5 122q-192 0 -324 -135q-73 -76 -113 -183t-40 -216zM444 1153l306 336h49l186 -336h-41l-174 303l-276 -303h-50z" />
<glyph unicode="&#xf5;" horiz-adv-x="1202" d="M113 449q0 114 42 228.5t124 201.5q144 151 358 151q203 0 329 -133.5t126 -337.5q0 -114 -42 -228.5t-124 -201.5q-145 -152 -359 -152q-203 0 -328.5 134t-125.5 338zM160 451q0 -184 110.5 -306t296.5 -122q192 0 324 135q73 76 113 183t40 216q0 184 -110.5 306 t-296.5 122q-192 0 -324 -135q-73 -76 -113 -183t-40 -216zM444 1260l11 61q63 88 153 88q39 0 72 -16t82 -50q40 -28 67 -40.5t58 -12.5q91 0 155 109h5l-11 -64q-61 -86 -153 -86q-27 0 -54.5 10t-45.5 21t-52 35q-42 29 -69 41t-58 12q-88 0 -155 -108h-5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1202" d="M113 449q0 114 42 228.5t124 201.5q144 151 358 151q203 0 329 -133.5t126 -337.5q0 -114 -42 -228.5t-124 -201.5q-145 -152 -359 -152q-203 0 -328.5 134t-125.5 338zM160 451q0 -184 110.5 -306t296.5 -122q192 0 324 135q73 76 113 183t40 216q0 184 -110.5 306 t-296.5 122q-192 0 -324 -135q-73 -76 -113 -183t-40 -216zM514 1319q0 23 17 39t40 16q22 0 37 -14.5t15 -36.5q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5zM866 1319q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -37 14.5 t-15 36.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1167" d="M150 721l8 43h946l-8 -43h-946zM477 285q0 28 20 47.5t48 19.5t47.5 -19.5t19.5 -47.5t-19.5 -48t-47.5 -20t-48 20t-20 48zM639 1200q0 28 20 48t48 20t47.5 -20t19.5 -48t-19.5 -47.5t-47.5 -19.5t-48 19.5t-20 47.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1202" d="M78 10l133 133q-98 124 -98 306q0 114 42 228.5t124 201.5q144 151 358 151q201 0 330 -135l131 131l28 -29l-133 -133q99 -126 99 -305q0 -114 -42 -228.5t-124 -201.5q-145 -152 -359 -152q-199 0 -329 136l-132 -131zM160 451q0 -162 84 -275l690 686 q-113 123 -297 123q-192 0 -324 -135q-73 -76 -113 -183t-40 -216zM270 145q112 -122 297 -122q192 0 324 135q73 76 113 183t40 216q0 162 -83 274z" />
<glyph unicode="&#xf9;" horiz-adv-x="1196" d="M162 262q0 49 22 184l99 562h47l-101 -568q-20 -114 -20 -176q0 -107 62.5 -174t183.5 -67q111 0 220 59.5t232 171.5l133 754h47l-178 -1008h-47l33 184l-4 2q-110 -98 -216.5 -153.5t-223.5 -55.5q-133 0 -211 76t-78 209zM608 1489h49l138 -336h-41z" />
<glyph unicode="&#xfa;" horiz-adv-x="1196" d="M162 262q0 49 22 184l99 562h47l-101 -568q-20 -114 -20 -176q0 -107 62.5 -174t183.5 -67q111 0 220 59.5t232 171.5l133 754h47l-178 -1008h-47l33 184l-4 2q-110 -98 -216.5 -153.5t-223.5 -55.5q-133 0 -211 76t-78 209zM631 1153l256 336h53l-264 -336h-45z" />
<glyph unicode="&#xfb;" horiz-adv-x="1196" d="M162 262q0 49 22 184l99 562h47l-101 -568q-20 -114 -20 -176q0 -107 62.5 -174t183.5 -67q111 0 220 59.5t232 171.5l133 754h47l-178 -1008h-47l33 184l-4 2q-110 -98 -216.5 -153.5t-223.5 -55.5q-133 0 -211 76t-78 209zM442 1153l306 336h49l186 -336h-41l-174 303 l-276 -303h-50z" />
<glyph unicode="&#xfc;" horiz-adv-x="1196" d="M162 262q0 49 22 184l99 562h47l-101 -568q-20 -114 -20 -176q0 -107 62.5 -174t183.5 -67q111 0 220 59.5t232 171.5l133 754h47l-178 -1008h-47l33 184l-4 2q-110 -98 -216.5 -153.5t-223.5 -55.5q-133 0 -211 76t-78 209zM512 1319q0 23 17 39t40 16q22 0 37 -14.5 t15 -36.5q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5zM864 1319q0 23 17 39t41 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-41 -16q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1069" d="M66 -451l374 564l-274 895h47l258 -846h2l559 846h51l-964 -1459h-53zM567 1153l256 336h54l-265 -336h-45z" />
<glyph unicode="&#xfe;" horiz-adv-x="1177" d="M29 -451l356 2016h47l-119 -662l2 -2q160 129 340 129q177 0 294.5 -120t117.5 -322q0 -118 -42.5 -236t-125.5 -207q-155 -168 -399 -168q-176 0 -328 93l-4 -2l-92 -519h-47zM176 119q143 -96 324 -96q225 0 364 151q74 80 115 190.5t41 221.5q0 179 -102 289t-267 110 q-188 0 -348 -147z" />
<glyph unicode="&#xff;" horiz-adv-x="1069" d="M66 -451l374 564l-274 895h47l258 -846h2l559 846h51l-964 -1459h-53zM449 1319q0 23 17 39t40 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -36.5 14.5t-14.5 36.5zM801 1319q0 23 17 39t40 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16 q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1847" d="M156 670q0 135 49 274.5t147 255.5q242 283 684 283h844l-8 -45h-701l-114 -645h608l-8 -45h-608l-123 -703h716l-8 -45h-770q-109 0 -211.5 26t-193 81t-158 133t-106.5 188.5t-39 241.5zM203 674q0 -149 53 -270.5t144.5 -198.5t210.5 -118.5t255 -41.5h13l245 1393 h-90q-424 0 -649 -269q-84 -101 -133 -230.5t-49 -264.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1998" d="M113 446q0 115 41 229.5t122 201.5q143 153 351 153q167 0 281.5 -99t142.5 -257h4q58 152 181.5 254t283.5 102q169 0 270.5 -115.5t101.5 -308.5q0 -49 -10 -108h-821q-2 -20 -2 -62q0 -195 115.5 -304t302.5 -109q142 0 272 73l-8 -51q-48 -29 -122.5 -48.5 t-152.5 -19.5q-169 0 -292 91.5t-152 253.5h-4q-58 -154 -181 -249.5t-288 -95.5q-194 0 -315 133t-121 336zM160 449q0 -183 106 -304.5t285 -121.5q186 0 315 135q70 74 110 182.5t40 218.5q0 183 -106 304.5t-285 121.5q-185 0 -314 -135q-72 -76 -111.5 -183.5 t-39.5 -217.5zM1067 543h774q4 32 4 59q0 77 -19 144t-57 121.5t-102.5 86t-148.5 31.5q-204 0 -336 -176q-87 -113 -115 -266z" />
<glyph unicode="&#x178;" horiz-adv-x="1257" d="M215 1483h51l396 -768h4l665 768h58l-707 -813l-119 -670h-47l119 670zM629 1794q0 23 17 39t40 16q22 0 36.5 -14.5t14.5 -36.5q0 -23 -17 -39t-40 -16q-22 0 -36.5 14.5t-14.5 36.5zM981 1794q0 23 17 39t40 16q22 0 37 -14.5t15 -36.5q0 -23 -17 -39t-41 -16 q-22 0 -36.5 14.5t-14.5 36.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="950" d="M319 1153l306 336h49l186 -336h-41l-174 303l-276 -303h-50z" />
<glyph unicode="&#x2dc;" horiz-adv-x="950" d="M319 1260l11 61q63 88 153 88q39 0 72 -16t82 -50q40 -28 67 -40.5t58 -12.5q92 0 156 109h4l-11 -64q-61 -86 -153 -86q-27 0 -54.5 10t-45.5 21t-52 35q-42 29 -69 41t-58 12q-88 0 -155 -108h-5z" />
<glyph unicode="&#x2000;" horiz-adv-x="1028" />
<glyph unicode="&#x2001;" horiz-adv-x="2056" />
<glyph unicode="&#x2002;" horiz-adv-x="1028" />
<glyph unicode="&#x2003;" horiz-adv-x="2056" />
<glyph unicode="&#x2004;" horiz-adv-x="685" />
<glyph unicode="&#x2005;" horiz-adv-x="514" />
<glyph unicode="&#x2006;" horiz-adv-x="342" />
<glyph unicode="&#x2007;" horiz-adv-x="342" />
<glyph unicode="&#x2008;" horiz-adv-x="257" />
<glyph unicode="&#x2009;" horiz-adv-x="411" />
<glyph unicode="&#x200a;" horiz-adv-x="114" />
<glyph unicode="&#x2010;" horiz-adv-x="741" d="M150 545l8 45h456l-8 -45h-456z" />
<glyph unicode="&#x2011;" horiz-adv-x="741" d="M150 545l8 45h456l-8 -45h-456z" />
<glyph unicode="&#x2012;" horiz-adv-x="741" d="M150 545l8 45h456l-8 -45h-456z" />
<glyph unicode="&#x2013;" horiz-adv-x="1013" d="M123 547l8 43h782l-8 -43h-782z" />
<glyph unicode="&#x2014;" horiz-adv-x="1599" d="M90 547l8 43h1434l-8 -43h-1434z" />
<glyph unicode="&#x2018;" horiz-adv-x="544" d="M268 1001l228 519h39l-211 -519h-56z" />
<glyph unicode="&#x2019;" horiz-adv-x="544" d="M276 1001l211 519h56l-228 -519h-39z" />
<glyph unicode="&#x201a;" horiz-adv-x="544" d="M49 -182l211 518h55l-227 -518h-39z" />
<glyph unicode="&#x201c;" horiz-adv-x="815" d="M268 1001l228 519h39l-211 -519h-56zM539 1001l227 519h39l-211 -519h-55z" />
<glyph unicode="&#x201d;" horiz-adv-x="815" d="M276 1001l211 519h56l-228 -519h-39zM547 1001l211 519h55l-227 -519h-39z" />
<glyph unicode="&#x201e;" horiz-adv-x="815" d="M49 -182l211 518h55l-227 -518h-39zM319 -182l211 518h56l-228 -518h-39z" />
<glyph unicode="&#x2022;" horiz-adv-x="794" d="M231 608q0 76 54.5 130.5t130.5 54.5t130 -54t54 -131q0 -76 -54 -130t-130 -54q-77 0 -131 54t-54 130z" />
<glyph unicode="&#x2026;" horiz-adv-x="1720" d="M160 43q0 28 18.5 47t46.5 19t47 -19t19 -47t-19 -47t-47 -19t-46.5 19t-18.5 47zM713 43q0 28 18.5 47t46.5 19t47 -19t19 -47t-19 -47t-47 -19t-46.5 19t-18.5 47zM1266 43q0 28 18.5 47t46.5 19t47 -19t19 -47t-19 -47t-47 -19t-46.5 19t-18.5 47z" />
<glyph unicode="&#x202f;" horiz-adv-x="411" />
<glyph unicode="&#x2039;" horiz-adv-x="692" d="M98 520l422 432h58l-426 -436l268 -461h-51z" />
<glyph unicode="&#x203a;" horiz-adv-x="692" d="M115 55l426 437l-269 460h52l270 -465l-422 -432h-57z" />
<glyph unicode="&#x205f;" horiz-adv-x="514" />
<glyph unicode="&#x20ac;" horiz-adv-x="1185" d="M35 565l8 43h188v4q0 150 43 289h-180l8 43h187q65 191 194 326q109 116 247 175.5t290 59.5q133 0 252 -43l-8 -43q-121 41 -246 41q-146 0 -274 -57.5t-226 -159.5q-122 -128 -182 -299h504l-9 -43h-509q-43 -136 -43 -293h501l-8 -43h-493q15 -252 164 -397t407 -145 q118 0 190 22l-8 -47q-100 -21 -184 -21q-274 0 -438.5 159t-178.5 429h-196z" />
<glyph unicode="&#x2122;" horiz-adv-x="1263" d="M168 1444v39h442v-39h-200v-555h-41v555h-201zM721 889v594h45l227 -336l234 336h43v-594h-41v526h-4l-232 -334l-229 340h-4v-532h-39z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x2a;" u2="&#x2026;" k="219" />
<hkern u1="&#x2a;" u2="&#x2e;" k="219" />
<hkern u1="&#x2a;" u2="&#x2c;" k="219" />
<hkern u1="&#x2c;" u2="V" k="168" />
<hkern u1="&#x2c;" u2="&#x39;" k="147" />
<hkern u1="&#x2c;" u2="&#x38;" k="37" />
<hkern u1="&#x2c;" u2="&#x37;" k="147" />
<hkern u1="&#x2c;" u2="&#x2a;" k="219" />
<hkern u1="&#x2d;" u2="x" k="106" />
<hkern u1="&#x2d;" u2="X" k="86" />
<hkern u1="&#x2d;" u2="V" k="127" />
<hkern u1="&#x2d;" u2="&#x32;" k="78" />
<hkern u1="&#x2d;" u2="&#x31;" k="92" />
<hkern u1="&#x2e;" u2="V" k="168" />
<hkern u1="&#x2e;" u2="&#x39;" k="147" />
<hkern u1="&#x2e;" u2="&#x38;" k="37" />
<hkern u1="&#x2e;" u2="&#x37;" k="147" />
<hkern u1="&#x2e;" u2="&#x2a;" k="219" />
<hkern u1="&#x2f;" u2="&#x153;" k="100" />
<hkern u1="&#x2f;" u2="&#xf8;" k="100" />
<hkern u1="&#x2f;" u2="&#xf6;" k="100" />
<hkern u1="&#x2f;" u2="&#xf5;" k="100" />
<hkern u1="&#x2f;" u2="&#xf4;" k="100" />
<hkern u1="&#x2f;" u2="&#xf3;" k="100" />
<hkern u1="&#x2f;" u2="&#xf2;" k="100" />
<hkern u1="&#x2f;" u2="&#xf0;" k="100" />
<hkern u1="&#x2f;" u2="&#xeb;" k="100" />
<hkern u1="&#x2f;" u2="&#xea;" k="100" />
<hkern u1="&#x2f;" u2="&#xe9;" k="100" />
<hkern u1="&#x2f;" u2="&#xe8;" k="100" />
<hkern u1="&#x2f;" u2="&#xe7;" k="100" />
<hkern u1="&#x2f;" u2="&#xe6;" k="100" />
<hkern u1="&#x2f;" u2="&#xe5;" k="100" />
<hkern u1="&#x2f;" u2="&#xe4;" k="100" />
<hkern u1="&#x2f;" u2="&#xe3;" k="100" />
<hkern u1="&#x2f;" u2="&#xe2;" k="100" />
<hkern u1="&#x2f;" u2="&#xe1;" k="100" />
<hkern u1="&#x2f;" u2="&#xe0;" k="100" />
<hkern u1="&#x2f;" u2="&#xc5;" k="143" />
<hkern u1="&#x2f;" u2="&#xc4;" k="143" />
<hkern u1="&#x2f;" u2="&#xc3;" k="143" />
<hkern u1="&#x2f;" u2="&#xc2;" k="143" />
<hkern u1="&#x2f;" u2="&#xc1;" k="143" />
<hkern u1="&#x2f;" u2="&#xc0;" k="143" />
<hkern u1="&#x2f;" u2="q" k="100" />
<hkern u1="&#x2f;" u2="o" k="100" />
<hkern u1="&#x2f;" u2="g" k="100" />
<hkern u1="&#x2f;" u2="e" k="100" />
<hkern u1="&#x2f;" u2="d" k="100" />
<hkern u1="&#x2f;" u2="c" k="100" />
<hkern u1="&#x2f;" u2="a" k="100" />
<hkern u1="&#x2f;" u2="A" k="143" />
<hkern u1="&#x2f;" u2="&#x34;" k="104" />
<hkern u1="&#x30;" u2="_" k="41" />
<hkern u1="&#x36;" u2="&#x37;" k="29" />
<hkern u1="&#x36;" u2="&#x31;" k="82" />
<hkern u1="&#x37;" u2="&#x2026;" k="248" />
<hkern u1="&#x37;" u2="&#x34;" k="104" />
<hkern u1="&#x37;" u2="&#x2f;" k="182" />
<hkern u1="&#x37;" u2="&#x2e;" k="248" />
<hkern u1="&#x37;" u2="&#x2c;" k="248" />
<hkern u1="&#x38;" u2="&#x2026;" k="37" />
<hkern u1="&#x38;" u2="&#x2e;" k="37" />
<hkern u1="&#x38;" u2="&#x2c;" k="37" />
<hkern u1="&#x39;" u2="&#x2026;" k="158" />
<hkern u1="&#x39;" u2="&#x2e;" k="158" />
<hkern u1="&#x39;" u2="&#x2c;" k="158" />
<hkern u1="A" u2="x" k="-10" />
<hkern u1="A" u2="\" k="143" />
<hkern u1="A" u2="V" k="211" />
<hkern u1="B" u2="&#x203a;" k="37" />
<hkern u1="B" u2="&#x201c;" k="8" />
<hkern u1="B" u2="&#x2018;" k="8" />
<hkern u1="B" u2="&#x178;" k="47" />
<hkern u1="B" u2="&#xff;" k="37" />
<hkern u1="B" u2="&#xfd;" k="37" />
<hkern u1="B" u2="&#xdd;" k="47" />
<hkern u1="B" u2="&#xc6;" k="10" />
<hkern u1="B" u2="&#xc5;" k="18" />
<hkern u1="B" u2="&#xc4;" k="18" />
<hkern u1="B" u2="&#xc3;" k="18" />
<hkern u1="B" u2="&#xc2;" k="18" />
<hkern u1="B" u2="&#xc1;" k="18" />
<hkern u1="B" u2="&#xc0;" k="18" />
<hkern u1="B" u2="&#xbb;" k="37" />
<hkern u1="B" u2="z" k="10" />
<hkern u1="B" u2="y" k="37" />
<hkern u1="B" u2="w" k="27" />
<hkern u1="B" u2="v" k="37" />
<hkern u1="B" u2="Y" k="47" />
<hkern u1="B" u2="W" k="10" />
<hkern u1="B" u2="V" k="29" />
<hkern u1="B" u2="A" k="18" />
<hkern u1="D" u2="x" k="6" />
<hkern u1="D" u2="_" k="74" />
<hkern u1="D" u2="X" k="18" />
<hkern u1="D" u2="V" k="96" />
<hkern u1="F" u2="&#x203a;" k="45" />
<hkern u1="F" u2="&#x2039;" k="37" />
<hkern u1="F" u2="&#x2026;" k="193" />
<hkern u1="F" u2="&#x201e;" k="76" />
<hkern u1="F" u2="&#x201c;" k="31" />
<hkern u1="F" u2="&#x201a;" k="76" />
<hkern u1="F" u2="&#x2018;" k="31" />
<hkern u1="F" u2="&#x153;" k="76" />
<hkern u1="F" u2="&#xff;" k="41" />
<hkern u1="F" u2="&#xfd;" k="41" />
<hkern u1="F" u2="&#xfc;" k="76" />
<hkern u1="F" u2="&#xfb;" k="76" />
<hkern u1="F" u2="&#xfa;" k="76" />
<hkern u1="F" u2="&#xf9;" k="76" />
<hkern u1="F" u2="&#xf8;" k="76" />
<hkern u1="F" u2="&#xf6;" k="76" />
<hkern u1="F" u2="&#xf5;" k="76" />
<hkern u1="F" u2="&#xf4;" k="76" />
<hkern u1="F" u2="&#xf3;" k="76" />
<hkern u1="F" u2="&#xf2;" k="76" />
<hkern u1="F" u2="&#xf1;" k="104" />
<hkern u1="F" u2="&#xf0;" k="76" />
<hkern u1="F" u2="&#xef;" k="-51" />
<hkern u1="F" u2="&#xee;" k="-10" />
<hkern u1="F" u2="&#xeb;" k="76" />
<hkern u1="F" u2="&#xea;" k="76" />
<hkern u1="F" u2="&#xe9;" k="76" />
<hkern u1="F" u2="&#xe8;" k="76" />
<hkern u1="F" u2="&#xe7;" k="76" />
<hkern u1="F" u2="&#xe6;" k="76" />
<hkern u1="F" u2="&#xe5;" k="76" />
<hkern u1="F" u2="&#xe4;" k="76" />
<hkern u1="F" u2="&#xe3;" k="76" />
<hkern u1="F" u2="&#xe2;" k="76" />
<hkern u1="F" u2="&#xe1;" k="76" />
<hkern u1="F" u2="&#xe0;" k="76" />
<hkern u1="F" u2="&#xc5;" k="133" />
<hkern u1="F" u2="&#xc4;" k="133" />
<hkern u1="F" u2="&#xc3;" k="133" />
<hkern u1="F" u2="&#xc2;" k="133" />
<hkern u1="F" u2="&#xc1;" k="133" />
<hkern u1="F" u2="&#xc0;" k="133" />
<hkern u1="F" u2="&#xbb;" k="45" />
<hkern u1="F" u2="&#xab;" k="37" />
<hkern u1="F" u2="z" k="106" />
<hkern u1="F" u2="y" k="41" />
<hkern u1="F" u2="x" k="39" />
<hkern u1="F" u2="w" k="61" />
<hkern u1="F" u2="v" k="41" />
<hkern u1="F" u2="u" k="76" />
<hkern u1="F" u2="t" k="33" />
<hkern u1="F" u2="s" k="41" />
<hkern u1="F" u2="r" k="104" />
<hkern u1="F" u2="q" k="76" />
<hkern u1="F" u2="p" k="104" />
<hkern u1="F" u2="o" k="76" />
<hkern u1="F" u2="n" k="104" />
<hkern u1="F" u2="m" k="104" />
<hkern u1="F" u2="g" k="76" />
<hkern u1="F" u2="e" k="76" />
<hkern u1="F" u2="d" k="76" />
<hkern u1="F" u2="c" k="76" />
<hkern u1="F" u2="a" k="76" />
<hkern u1="F" u2="A" k="133" />
<hkern u1="F" u2="&#x2e;" k="193" />
<hkern u1="F" u2="&#x2c;" k="193" />
<hkern u1="K" u2="&#xf8;" k="41" />
<hkern u1="L" u2="V" k="158" />
<hkern u1="O" u2="x" k="6" />
<hkern u1="O" u2="_" k="74" />
<hkern u1="O" u2="X" k="18" />
<hkern u1="O" u2="V" k="96" />
<hkern u1="P" u2="&#x203a;" k="27" />
<hkern u1="P" u2="&#x2039;" k="55" />
<hkern u1="P" u2="&#x2026;" k="250" />
<hkern u1="P" u2="&#x201e;" k="152" />
<hkern u1="P" u2="&#x201a;" k="152" />
<hkern u1="P" u2="&#x153;" k="51" />
<hkern u1="P" u2="&#xfc;" k="29" />
<hkern u1="P" u2="&#xfb;" k="29" />
<hkern u1="P" u2="&#xfa;" k="29" />
<hkern u1="P" u2="&#xf9;" k="29" />
<hkern u1="P" u2="&#xf8;" k="51" />
<hkern u1="P" u2="&#xf6;" k="51" />
<hkern u1="P" u2="&#xf5;" k="51" />
<hkern u1="P" u2="&#xf4;" k="51" />
<hkern u1="P" u2="&#xf3;" k="51" />
<hkern u1="P" u2="&#xf2;" k="51" />
<hkern u1="P" u2="&#xf1;" k="20" />
<hkern u1="P" u2="&#xf0;" k="51" />
<hkern u1="P" u2="&#xef;" k="-47" />
<hkern u1="P" u2="&#xee;" k="-55" />
<hkern u1="P" u2="&#xeb;" k="51" />
<hkern u1="P" u2="&#xea;" k="51" />
<hkern u1="P" u2="&#xe9;" k="51" />
<hkern u1="P" u2="&#xe8;" k="51" />
<hkern u1="P" u2="&#xe7;" k="51" />
<hkern u1="P" u2="&#xe6;" k="51" />
<hkern u1="P" u2="&#xe5;" k="51" />
<hkern u1="P" u2="&#xe4;" k="51" />
<hkern u1="P" u2="&#xe3;" k="51" />
<hkern u1="P" u2="&#xe2;" k="51" />
<hkern u1="P" u2="&#xe1;" k="51" />
<hkern u1="P" u2="&#xe0;" k="51" />
<hkern u1="P" u2="&#xc5;" k="57" />
<hkern u1="P" u2="&#xc4;" k="57" />
<hkern u1="P" u2="&#xc3;" k="57" />
<hkern u1="P" u2="&#xc2;" k="57" />
<hkern u1="P" u2="&#xc1;" k="57" />
<hkern u1="P" u2="&#xc0;" k="57" />
<hkern u1="P" u2="&#xbb;" k="27" />
<hkern u1="P" u2="&#xab;" k="55" />
<hkern u1="P" u2="z" k="20" />
<hkern u1="P" u2="x" k="10" />
<hkern u1="P" u2="u" k="29" />
<hkern u1="P" u2="s" k="4" />
<hkern u1="P" u2="r" k="20" />
<hkern u1="P" u2="q" k="51" />
<hkern u1="P" u2="p" k="20" />
<hkern u1="P" u2="o" k="51" />
<hkern u1="P" u2="n" k="20" />
<hkern u1="P" u2="m" k="20" />
<hkern u1="P" u2="g" k="51" />
<hkern u1="P" u2="e" k="51" />
<hkern u1="P" u2="d" k="51" />
<hkern u1="P" u2="c" k="51" />
<hkern u1="P" u2="a" k="51" />
<hkern u1="P" u2="W" k="27" />
<hkern u1="P" u2="V" k="41" />
<hkern u1="P" u2="J" k="100" />
<hkern u1="P" u2="A" k="57" />
<hkern u1="P" u2="&#x2e;" k="250" />
<hkern u1="P" u2="&#x2c;" k="250" />
<hkern u1="Q" u2="x" k="6" />
<hkern u1="Q" u2="_" k="74" />
<hkern u1="Q" u2="X" k="18" />
<hkern u1="Q" u2="V" k="96" />
<hkern u1="R" u2="&#xf8;" k="78" />
<hkern u1="R" u2="V" k="68" />
<hkern u1="S" u2="_" k="27" />
<hkern u1="S" u2="V" k="27" />
<hkern u1="T" u2="&#xff;" k="133" />
<hkern u1="T" u2="&#xfc;" k="170" />
<hkern u1="T" u2="&#xf6;" k="238" />
<hkern u1="T" u2="&#xf1;" k="180" />
<hkern u1="T" u2="&#xeb;" k="182" />
<hkern u1="T" u2="&#xea;" k="201" />
<hkern u1="T" u2="&#xe4;" k="180" />
<hkern u1="T" u2="&#xe3;" k="231" />
<hkern u1="T" u2="&#xe2;" k="242" />
<hkern u1="T" u2="x" k="106" />
<hkern u1="T" u2="_" k="98" />
<hkern u1="T" u2="&#x2f;" k="193" />
<hkern u1="U" u2="x" k="10" />
<hkern u1="U" u2="a" k="8" />
<hkern u1="V" u2="&#x2026;" k="150" />
<hkern u1="V" u2="&#x2014;" k="113" />
<hkern u1="V" u2="&#x2013;" k="113" />
<hkern u1="V" u2="&#x153;" k="117" />
<hkern u1="V" u2="&#x152;" k="18" />
<hkern u1="V" u2="&#xff;" k="61" />
<hkern u1="V" u2="&#xfd;" k="61" />
<hkern u1="V" u2="&#xfc;" k="78" />
<hkern u1="V" u2="&#xfb;" k="78" />
<hkern u1="V" u2="&#xfa;" k="78" />
<hkern u1="V" u2="&#xf9;" k="78" />
<hkern u1="V" u2="&#xf8;" k="117" />
<hkern u1="V" u2="&#xf6;" k="117" />
<hkern u1="V" u2="&#xf5;" k="117" />
<hkern u1="V" u2="&#xf4;" k="117" />
<hkern u1="V" u2="&#xf3;" k="117" />
<hkern u1="V" u2="&#xf2;" k="117" />
<hkern u1="V" u2="&#xf1;" k="109" />
<hkern u1="V" u2="&#xf0;" k="117" />
<hkern u1="V" u2="&#xeb;" k="117" />
<hkern u1="V" u2="&#xea;" k="117" />
<hkern u1="V" u2="&#xe9;" k="117" />
<hkern u1="V" u2="&#xe8;" k="117" />
<hkern u1="V" u2="&#xe7;" k="117" />
<hkern u1="V" u2="&#xe6;" k="117" />
<hkern u1="V" u2="&#xe5;" k="117" />
<hkern u1="V" u2="&#xe4;" k="117" />
<hkern u1="V" u2="&#xe3;" k="117" />
<hkern u1="V" u2="&#xe2;" k="117" />
<hkern u1="V" u2="&#xe1;" k="117" />
<hkern u1="V" u2="&#xe0;" k="117" />
<hkern u1="V" u2="&#xd8;" k="18" />
<hkern u1="V" u2="&#xd6;" k="18" />
<hkern u1="V" u2="&#xd5;" k="18" />
<hkern u1="V" u2="&#xd4;" k="18" />
<hkern u1="V" u2="&#xd3;" k="18" />
<hkern u1="V" u2="&#xd2;" k="18" />
<hkern u1="V" u2="&#xc7;" k="18" />
<hkern u1="V" u2="&#xc6;" k="252" />
<hkern u1="V" u2="&#xc5;" k="133" />
<hkern u1="V" u2="&#xc4;" k="133" />
<hkern u1="V" u2="&#xc3;" k="133" />
<hkern u1="V" u2="&#xc2;" k="133" />
<hkern u1="V" u2="&#xc1;" k="133" />
<hkern u1="V" u2="&#xc0;" k="133" />
<hkern u1="V" u2="&#xad;" k="113" />
<hkern u1="V" u2="y" k="61" />
<hkern u1="V" u2="x" k="72" />
<hkern u1="V" u2="w" k="92" />
<hkern u1="V" u2="v" k="61" />
<hkern u1="V" u2="u" k="78" />
<hkern u1="V" u2="t" k="35" />
<hkern u1="V" u2="s" k="59" />
<hkern u1="V" u2="r" k="109" />
<hkern u1="V" u2="q" k="117" />
<hkern u1="V" u2="p" k="109" />
<hkern u1="V" u2="o" k="117" />
<hkern u1="V" u2="n" k="109" />
<hkern u1="V" u2="m" k="109" />
<hkern u1="V" u2="g" k="117" />
<hkern u1="V" u2="e" k="117" />
<hkern u1="V" u2="d" k="117" />
<hkern u1="V" u2="c" k="117" />
<hkern u1="V" u2="a" k="117" />
<hkern u1="V" u2="_" k="78" />
<hkern u1="V" u2="S" k="41" />
<hkern u1="V" u2="Q" k="18" />
<hkern u1="V" u2="O" k="18" />
<hkern u1="V" u2="J" k="100" />
<hkern u1="V" u2="G" k="18" />
<hkern u1="V" u2="C" k="18" />
<hkern u1="V" u2="A" k="133" />
<hkern u1="V" u2="&#x2f;" k="133" />
<hkern u1="V" u2="&#x2e;" k="150" />
<hkern u1="V" u2="&#x2d;" k="113" />
<hkern u1="V" u2="&#x2c;" k="150" />
<hkern u1="W" u2="x" k="35" />
<hkern u1="W" u2="_" k="92" />
<hkern u1="W" u2="&#x2f;" k="92" />
<hkern u1="X" u2="&#x203a;" k="45" />
<hkern u1="X" u2="&#x2039;" k="72" />
<hkern u1="X" u2="&#x201d;" k="39" />
<hkern u1="X" u2="&#x201c;" k="51" />
<hkern u1="X" u2="&#x2019;" k="39" />
<hkern u1="X" u2="&#x2018;" k="51" />
<hkern u1="X" u2="&#x2014;" k="96" />
<hkern u1="X" u2="&#x2013;" k="96" />
<hkern u1="X" u2="&#x153;" k="78" />
<hkern u1="X" u2="&#x152;" k="18" />
<hkern u1="X" u2="&#xff;" k="160" />
<hkern u1="X" u2="&#xfd;" k="160" />
<hkern u1="X" u2="&#xf8;" k="78" />
<hkern u1="X" u2="&#xf6;" k="78" />
<hkern u1="X" u2="&#xf5;" k="78" />
<hkern u1="X" u2="&#xf4;" k="78" />
<hkern u1="X" u2="&#xf3;" k="78" />
<hkern u1="X" u2="&#xf2;" k="78" />
<hkern u1="X" u2="&#xf0;" k="78" />
<hkern u1="X" u2="&#xeb;" k="78" />
<hkern u1="X" u2="&#xea;" k="78" />
<hkern u1="X" u2="&#xe9;" k="78" />
<hkern u1="X" u2="&#xe8;" k="78" />
<hkern u1="X" u2="&#xe7;" k="78" />
<hkern u1="X" u2="&#xe6;" k="78" />
<hkern u1="X" u2="&#xe5;" k="78" />
<hkern u1="X" u2="&#xe4;" k="78" />
<hkern u1="X" u2="&#xe3;" k="78" />
<hkern u1="X" u2="&#xe2;" k="78" />
<hkern u1="X" u2="&#xe1;" k="78" />
<hkern u1="X" u2="&#xe0;" k="78" />
<hkern u1="X" u2="&#xd8;" k="18" />
<hkern u1="X" u2="&#xd6;" k="18" />
<hkern u1="X" u2="&#xd5;" k="18" />
<hkern u1="X" u2="&#xd4;" k="18" />
<hkern u1="X" u2="&#xd3;" k="18" />
<hkern u1="X" u2="&#xd2;" k="18" />
<hkern u1="X" u2="&#xc7;" k="18" />
<hkern u1="X" u2="&#xbb;" k="45" />
<hkern u1="X" u2="&#xad;" k="96" />
<hkern u1="X" u2="&#xab;" k="72" />
<hkern u1="X" u2="y" k="160" />
<hkern u1="X" u2="v" k="160" />
<hkern u1="X" u2="q" k="78" />
<hkern u1="X" u2="o" k="78" />
<hkern u1="X" u2="g" k="78" />
<hkern u1="X" u2="e" k="78" />
<hkern u1="X" u2="d" k="78" />
<hkern u1="X" u2="c" k="78" />
<hkern u1="X" u2="a" k="78" />
<hkern u1="X" u2="Q" k="18" />
<hkern u1="X" u2="O" k="18" />
<hkern u1="X" u2="G" k="18" />
<hkern u1="X" u2="C" k="18" />
<hkern u1="X" u2="&#x2d;" k="96" />
<hkern u1="Y" u2="&#xff;" k="96" />
<hkern u1="Y" u2="&#xf6;" k="143" />
<hkern u1="Y" u2="&#xf5;" k="150" />
<hkern u1="Y" u2="&#xf4;" k="174" />
<hkern u1="Y" u2="&#xee;" k="-37" />
<hkern u1="Y" u2="&#xec;" k="-29" />
<hkern u1="Y" u2="&#xeb;" k="158" />
<hkern u1="Y" u2="&#xea;" k="174" />
<hkern u1="Y" u2="&#xe8;" k="178" />
<hkern u1="Y" u2="&#xe4;" k="158" />
<hkern u1="Y" u2="&#xe3;" k="174" />
<hkern u1="Y" u2="&#xe2;" k="184" />
<hkern u1="Y" u2="&#xe0;" k="178" />
<hkern u1="Y" u2="x" k="66" />
<hkern u1="Y" u2="_" k="106" />
<hkern u1="Y" u2="&#x34;" k="104" />
<hkern u1="Y" u2="&#x2f;" k="135" />
<hkern u1="\" u2="&#x178;" k="135" />
<hkern u1="\" u2="&#xdd;" k="135" />
<hkern u1="\" u2="Y" k="135" />
<hkern u1="\" u2="W" k="115" />
<hkern u1="\" u2="V" k="125" />
<hkern u1="\" u2="T" k="193" />
<hkern u1="\" u2="&#x37;" k="98" />
<hkern u1="_" u2="&#x178;" k="106" />
<hkern u1="_" u2="&#x153;" k="160" />
<hkern u1="_" u2="&#x152;" k="74" />
<hkern u1="_" u2="&#xff;" k="90" />
<hkern u1="_" u2="&#xfd;" k="90" />
<hkern u1="_" u2="&#xf8;" k="160" />
<hkern u1="_" u2="&#xf6;" k="160" />
<hkern u1="_" u2="&#xf5;" k="160" />
<hkern u1="_" u2="&#xf4;" k="160" />
<hkern u1="_" u2="&#xf3;" k="160" />
<hkern u1="_" u2="&#xf2;" k="160" />
<hkern u1="_" u2="&#xf0;" k="160" />
<hkern u1="_" u2="&#xeb;" k="160" />
<hkern u1="_" u2="&#xea;" k="160" />
<hkern u1="_" u2="&#xe9;" k="160" />
<hkern u1="_" u2="&#xe8;" k="160" />
<hkern u1="_" u2="&#xe7;" k="160" />
<hkern u1="_" u2="&#xe6;" k="160" />
<hkern u1="_" u2="&#xe5;" k="160" />
<hkern u1="_" u2="&#xe4;" k="160" />
<hkern u1="_" u2="&#xe3;" k="160" />
<hkern u1="_" u2="&#xe2;" k="160" />
<hkern u1="_" u2="&#xe1;" k="160" />
<hkern u1="_" u2="&#xe0;" k="160" />
<hkern u1="_" u2="&#xdd;" k="106" />
<hkern u1="_" u2="&#xd8;" k="74" />
<hkern u1="_" u2="&#xd6;" k="74" />
<hkern u1="_" u2="&#xd5;" k="74" />
<hkern u1="_" u2="&#xd4;" k="74" />
<hkern u1="_" u2="&#xd3;" k="74" />
<hkern u1="_" u2="&#xd2;" k="74" />
<hkern u1="_" u2="&#xc7;" k="74" />
<hkern u1="_" u2="y" k="90" />
<hkern u1="_" u2="w" k="82" />
<hkern u1="_" u2="v" k="90" />
<hkern u1="_" u2="q" k="160" />
<hkern u1="_" u2="o" k="160" />
<hkern u1="_" u2="g" k="160" />
<hkern u1="_" u2="e" k="160" />
<hkern u1="_" u2="d" k="160" />
<hkern u1="_" u2="c" k="160" />
<hkern u1="_" u2="a" k="160" />
<hkern u1="_" u2="Y" k="106" />
<hkern u1="_" u2="W" k="41" />
<hkern u1="_" u2="V" k="74" />
<hkern u1="_" u2="T" k="98" />
<hkern u1="_" u2="Q" k="74" />
<hkern u1="_" u2="O" k="74" />
<hkern u1="_" u2="G" k="74" />
<hkern u1="_" u2="C" k="74" />
<hkern u1="_" u2="&#x30;" k="41" />
<hkern u1="b" u2="x" k="55" />
<hkern u1="b" u2="_" k="57" />
<hkern u1="b" u2="X" k="47" />
<hkern u1="b" u2="V" k="160" />
<hkern u1="c" u2="V" k="29" />
<hkern u1="e" u2="x" k="33" />
<hkern u1="e" u2="V" k="86" />
<hkern u1="f" u2="_" k="20" />
<hkern u1="f" u2="&#x3f;" k="-37" />
<hkern u1="f" u2="&#x2a;" k="-57" />
<hkern u1="f" u2="&#x21;" k="-4" />
<hkern u1="g" u2="&#x201d;" k="68" />
<hkern u1="g" u2="&#x201c;" k="78" />
<hkern u1="g" u2="&#x2019;" k="68" />
<hkern u1="g" u2="&#x2018;" k="78" />
<hkern u1="g" u2="j" k="-14" />
<hkern u1="h" u2="y" k="90" />
<hkern u1="h" u2="V" k="195" />
<hkern u1="j" u2="j" k="-10" />
<hkern u1="k" u2="&#xf8;" k="45" />
<hkern u1="m" u2="y" k="90" />
<hkern u1="m" u2="V" k="195" />
<hkern u1="n" u2="y" k="90" />
<hkern u1="n" u2="V" k="195" />
<hkern u1="o" u2="x" k="55" />
<hkern u1="o" u2="_" k="57" />
<hkern u1="o" u2="X" k="47" />
<hkern u1="o" u2="V" k="160" />
<hkern u1="p" u2="&#x201c;" k="123" />
<hkern u1="p" u2="&#x2018;" k="123" />
<hkern u1="p" u2="x" k="55" />
<hkern u1="p" u2="_" k="57" />
<hkern u1="p" u2="X" k="47" />
<hkern u1="p" u2="V" k="160" />
<hkern u1="q" u2="&#x201c;" k="41" />
<hkern u1="q" u2="&#x2018;" k="41" />
<hkern u1="s" u2="x" k="20" />
<hkern u1="s" u2="V" k="55" />
<hkern u1="u" u2="V" k="82" />
<hkern u1="v" u2="_" k="102" />
<hkern u1="v" u2="X" k="160" />
<hkern u1="v" u2="V" k="61" />
<hkern u1="w" u2="V" k="102" />
<hkern u1="x" u2="&#x203a;" k="10" />
<hkern u1="x" u2="&#x2039;" k="92" />
<hkern u1="x" u2="&#x2014;" k="106" />
<hkern u1="x" u2="&#x2013;" k="106" />
<hkern u1="x" u2="&#x153;" k="45" />
<hkern u1="x" u2="&#xf8;" k="45" />
<hkern u1="x" u2="&#xf6;" k="45" />
<hkern u1="x" u2="&#xf5;" k="45" />
<hkern u1="x" u2="&#xf4;" k="45" />
<hkern u1="x" u2="&#xf3;" k="45" />
<hkern u1="x" u2="&#xf2;" k="45" />
<hkern u1="x" u2="&#xf0;" k="45" />
<hkern u1="x" u2="&#xeb;" k="45" />
<hkern u1="x" u2="&#xea;" k="45" />
<hkern u1="x" u2="&#xe9;" k="45" />
<hkern u1="x" u2="&#xe8;" k="45" />
<hkern u1="x" u2="&#xe7;" k="45" />
<hkern u1="x" u2="&#xe6;" k="45" />
<hkern u1="x" u2="&#xe5;" k="45" />
<hkern u1="x" u2="&#xe4;" k="45" />
<hkern u1="x" u2="&#xe3;" k="45" />
<hkern u1="x" u2="&#xe2;" k="45" />
<hkern u1="x" u2="&#xe1;" k="45" />
<hkern u1="x" u2="&#xe0;" k="45" />
<hkern u1="x" u2="&#xbb;" k="10" />
<hkern u1="x" u2="&#xad;" k="106" />
<hkern u1="x" u2="&#xab;" k="92" />
<hkern u1="x" u2="q" k="45" />
<hkern u1="x" u2="o" k="45" />
<hkern u1="x" u2="g" k="45" />
<hkern u1="x" u2="e" k="45" />
<hkern u1="x" u2="d" k="45" />
<hkern u1="x" u2="c" k="45" />
<hkern u1="x" u2="a" k="45" />
<hkern u1="x" u2="W" k="37" />
<hkern u1="x" u2="V" k="68" />
<hkern u1="x" u2="T" k="66" />
<hkern u1="x" u2="&#x2d;" k="106" />
<hkern u1="y" u2="_" k="102" />
<hkern u1="y" u2="X" k="160" />
<hkern u1="y" u2="V" k="61" />
<hkern u1="&#xab;" u2="x" k="10" />
<hkern u1="&#xab;" u2="X" k="41" />
<hkern u1="&#xab;" u2="V" k="61" />
<hkern u1="&#xad;" u2="x" k="106" />
<hkern u1="&#xad;" u2="X" k="86" />
<hkern u1="&#xad;" u2="V" k="127" />
<hkern u1="&#xad;" u2="&#x32;" k="78" />
<hkern u1="&#xad;" u2="&#x31;" k="92" />
<hkern u1="&#xbb;" u2="x" k="92" />
<hkern u1="&#xbb;" u2="X" k="82" />
<hkern u1="&#xbb;" u2="V" k="119" />
<hkern u1="&#xc0;" u2="x" k="-10" />
<hkern u1="&#xc0;" u2="\" k="143" />
<hkern u1="&#xc0;" u2="V" k="211" />
<hkern u1="&#xc1;" u2="x" k="-10" />
<hkern u1="&#xc1;" u2="\" k="143" />
<hkern u1="&#xc1;" u2="V" k="211" />
<hkern u1="&#xc2;" u2="x" k="-10" />
<hkern u1="&#xc2;" u2="\" k="143" />
<hkern u1="&#xc2;" u2="V" k="211" />
<hkern u1="&#xc3;" u2="x" k="-10" />
<hkern u1="&#xc3;" u2="\" k="143" />
<hkern u1="&#xc3;" u2="V" k="211" />
<hkern u1="&#xc4;" u2="x" k="-10" />
<hkern u1="&#xc4;" u2="\" k="143" />
<hkern u1="&#xc4;" u2="V" k="211" />
<hkern u1="&#xc5;" u2="x" k="-10" />
<hkern u1="&#xc5;" u2="\" k="143" />
<hkern u1="&#xc5;" u2="V" k="211" />
<hkern u1="&#xd2;" u2="x" k="6" />
<hkern u1="&#xd2;" u2="_" k="74" />
<hkern u1="&#xd2;" u2="X" k="18" />
<hkern u1="&#xd2;" u2="V" k="96" />
<hkern u1="&#xd3;" u2="x" k="6" />
<hkern u1="&#xd3;" u2="_" k="74" />
<hkern u1="&#xd3;" u2="X" k="18" />
<hkern u1="&#xd3;" u2="V" k="96" />
<hkern u1="&#xd4;" u2="x" k="6" />
<hkern u1="&#xd4;" u2="_" k="74" />
<hkern u1="&#xd4;" u2="X" k="18" />
<hkern u1="&#xd4;" u2="V" k="96" />
<hkern u1="&#xd5;" u2="x" k="6" />
<hkern u1="&#xd5;" u2="_" k="74" />
<hkern u1="&#xd5;" u2="X" k="18" />
<hkern u1="&#xd5;" u2="V" k="96" />
<hkern u1="&#xd6;" u2="x" k="6" />
<hkern u1="&#xd6;" u2="_" k="74" />
<hkern u1="&#xd6;" u2="X" k="18" />
<hkern u1="&#xd6;" u2="V" k="96" />
<hkern u1="&#xd8;" u2="x" k="6" />
<hkern u1="&#xd8;" u2="_" k="74" />
<hkern u1="&#xd8;" u2="X" k="18" />
<hkern u1="&#xd8;" u2="V" k="96" />
<hkern u1="&#xd9;" u2="x" k="10" />
<hkern u1="&#xd9;" u2="a" k="8" />
<hkern u1="&#xda;" u2="x" k="10" />
<hkern u1="&#xda;" u2="a" k="8" />
<hkern u1="&#xdb;" u2="x" k="10" />
<hkern u1="&#xdb;" u2="a" k="8" />
<hkern u1="&#xdc;" u2="x" k="10" />
<hkern u1="&#xdc;" u2="a" k="8" />
<hkern u1="&#xdd;" u2="&#xff;" k="96" />
<hkern u1="&#xdd;" u2="&#xf6;" k="143" />
<hkern u1="&#xdd;" u2="&#xf5;" k="150" />
<hkern u1="&#xdd;" u2="&#xf4;" k="174" />
<hkern u1="&#xdd;" u2="&#xee;" k="-37" />
<hkern u1="&#xdd;" u2="&#xec;" k="-29" />
<hkern u1="&#xdd;" u2="&#xeb;" k="158" />
<hkern u1="&#xdd;" u2="&#xea;" k="174" />
<hkern u1="&#xdd;" u2="&#xe8;" k="178" />
<hkern u1="&#xdd;" u2="&#xe4;" k="158" />
<hkern u1="&#xdd;" u2="&#xe3;" k="174" />
<hkern u1="&#xdd;" u2="&#xe2;" k="184" />
<hkern u1="&#xdd;" u2="&#xe0;" k="178" />
<hkern u1="&#xdd;" u2="x" k="66" />
<hkern u1="&#xdd;" u2="_" k="106" />
<hkern u1="&#xdd;" u2="&#x34;" k="104" />
<hkern u1="&#xdd;" u2="&#x2f;" k="135" />
<hkern u1="&#xde;" u2="&#x178;" k="94" />
<hkern u1="&#xde;" u2="&#xdd;" k="94" />
<hkern u1="&#xde;" u2="&#xc5;" k="29" />
<hkern u1="&#xde;" u2="&#xc4;" k="29" />
<hkern u1="&#xde;" u2="&#xc3;" k="29" />
<hkern u1="&#xde;" u2="&#xc2;" k="29" />
<hkern u1="&#xde;" u2="&#xc1;" k="29" />
<hkern u1="&#xde;" u2="&#xc0;" k="29" />
<hkern u1="&#xde;" u2="z" k="18" />
<hkern u1="&#xde;" u2="_" k="115" />
<hkern u1="&#xde;" u2="Y" k="94" />
<hkern u1="&#xde;" u2="W" k="61" />
<hkern u1="&#xde;" u2="V" k="102" />
<hkern u1="&#xde;" u2="T" k="115" />
<hkern u1="&#xde;" u2="A" k="29" />
<hkern u1="&#xdf;" u2="&#xff;" k="20" />
<hkern u1="&#xdf;" u2="&#xfd;" k="20" />
<hkern u1="&#xdf;" u2="y" k="20" />
<hkern u1="&#xdf;" u2="w" k="20" />
<hkern u1="&#xdf;" u2="v" k="20" />
<hkern u1="&#xe6;" u2="x" k="33" />
<hkern u1="&#xe6;" u2="V" k="86" />
<hkern u1="&#xe7;" u2="V" k="29" />
<hkern u1="&#xe8;" u2="x" k="33" />
<hkern u1="&#xe8;" u2="V" k="86" />
<hkern u1="&#xe9;" u2="x" k="33" />
<hkern u1="&#xe9;" u2="V" k="86" />
<hkern u1="&#xea;" u2="x" k="33" />
<hkern u1="&#xea;" u2="V" k="86" />
<hkern u1="&#xeb;" u2="x" k="33" />
<hkern u1="&#xeb;" u2="V" k="86" />
<hkern u1="&#xee;" u2="&#x178;" k="-8" />
<hkern u1="&#xee;" u2="&#xdd;" k="-8" />
<hkern u1="&#xee;" u2="Y" k="-8" />
<hkern u1="&#xee;" u2="W" k="-57" />
<hkern u1="&#xf2;" u2="x" k="55" />
<hkern u1="&#xf2;" u2="_" k="57" />
<hkern u1="&#xf2;" u2="X" k="47" />
<hkern u1="&#xf2;" u2="V" k="160" />
<hkern u1="&#xf3;" u2="x" k="55" />
<hkern u1="&#xf3;" u2="_" k="57" />
<hkern u1="&#xf3;" u2="X" k="47" />
<hkern u1="&#xf3;" u2="V" k="160" />
<hkern u1="&#xf4;" u2="x" k="55" />
<hkern u1="&#xf4;" u2="_" k="57" />
<hkern u1="&#xf4;" u2="X" k="47" />
<hkern u1="&#xf4;" u2="V" k="160" />
<hkern u1="&#xf5;" u2="x" k="55" />
<hkern u1="&#xf5;" u2="_" k="57" />
<hkern u1="&#xf5;" u2="X" k="47" />
<hkern u1="&#xf5;" u2="V" k="160" />
<hkern u1="&#xf6;" u2="x" k="55" />
<hkern u1="&#xf6;" u2="_" k="57" />
<hkern u1="&#xf6;" u2="X" k="47" />
<hkern u1="&#xf6;" u2="V" k="160" />
<hkern u1="&#xf8;" u2="x" k="55" />
<hkern u1="&#xf8;" u2="_" k="57" />
<hkern u1="&#xf8;" u2="X" k="47" />
<hkern u1="&#xf8;" u2="V" k="160" />
<hkern u1="&#xf9;" u2="V" k="82" />
<hkern u1="&#xfa;" u2="V" k="82" />
<hkern u1="&#xfb;" u2="V" k="82" />
<hkern u1="&#xfc;" u2="V" k="82" />
<hkern u1="&#xfd;" u2="_" k="102" />
<hkern u1="&#xfd;" u2="X" k="160" />
<hkern u1="&#xfd;" u2="V" k="61" />
<hkern u1="&#xfe;" u2="x" k="55" />
<hkern u1="&#xfe;" u2="_" k="57" />
<hkern u1="&#xfe;" u2="X" k="47" />
<hkern u1="&#xfe;" u2="V" k="160" />
<hkern u1="&#xff;" u2="_" k="102" />
<hkern u1="&#xff;" u2="X" k="160" />
<hkern u1="&#xff;" u2="V" k="61" />
<hkern u1="&#x153;" u2="x" k="33" />
<hkern u1="&#x153;" u2="V" k="86" />
<hkern u1="&#x178;" u2="&#xff;" k="96" />
<hkern u1="&#x178;" u2="&#xf6;" k="143" />
<hkern u1="&#x178;" u2="&#xf5;" k="150" />
<hkern u1="&#x178;" u2="&#xf4;" k="174" />
<hkern u1="&#x178;" u2="&#xee;" k="-37" />
<hkern u1="&#x178;" u2="&#xec;" k="-29" />
<hkern u1="&#x178;" u2="&#xeb;" k="158" />
<hkern u1="&#x178;" u2="&#xea;" k="174" />
<hkern u1="&#x178;" u2="&#xe8;" k="178" />
<hkern u1="&#x178;" u2="&#xe4;" k="158" />
<hkern u1="&#x178;" u2="&#xe3;" k="174" />
<hkern u1="&#x178;" u2="&#xe2;" k="184" />
<hkern u1="&#x178;" u2="&#xe0;" k="178" />
<hkern u1="&#x178;" u2="x" k="66" />
<hkern u1="&#x178;" u2="_" k="106" />
<hkern u1="&#x178;" u2="&#x34;" k="104" />
<hkern u1="&#x178;" u2="&#x2f;" k="135" />
<hkern u1="&#x2013;" u2="x" k="106" />
<hkern u1="&#x2013;" u2="X" k="86" />
<hkern u1="&#x2013;" u2="V" k="127" />
<hkern u1="&#x2013;" u2="&#x32;" k="78" />
<hkern u1="&#x2013;" u2="&#x31;" k="123" />
<hkern u1="&#x2014;" u2="x" k="106" />
<hkern u1="&#x2014;" u2="X" k="86" />
<hkern u1="&#x2014;" u2="V" k="127" />
<hkern u1="&#x2014;" u2="&#x32;" k="78" />
<hkern u1="&#x2014;" u2="&#x31;" k="92" />
<hkern u1="&#x2018;" u2="x" k="57" />
<hkern u1="&#x2018;" u2="j" k="16" />
<hkern u1="&#x2018;" u2="g" k="125" />
<hkern u1="&#x2018;" u2="X" k="10" />
<hkern u1="&#x2019;" u2="x" k="51" />
<hkern u1="&#x2019;" u2="j" k="16" />
<hkern u1="&#x2019;" u2="X" k="41" />
<hkern u1="&#x201a;" u2="V" k="86" />
<hkern u1="&#x201c;" u2="x" k="57" />
<hkern u1="&#x201c;" u2="j" k="16" />
<hkern u1="&#x201c;" u2="g" k="125" />
<hkern u1="&#x201c;" u2="X" k="10" />
<hkern u1="&#x201d;" u2="x" k="51" />
<hkern u1="&#x201d;" u2="j" k="16" />
<hkern u1="&#x201d;" u2="X" k="41" />
<hkern u1="&#x201e;" u2="V" k="86" />
<hkern u1="&#x2026;" u2="V" k="168" />
<hkern u1="&#x2026;" u2="&#x39;" k="147" />
<hkern u1="&#x2026;" u2="&#x38;" k="37" />
<hkern u1="&#x2026;" u2="&#x37;" k="147" />
<hkern u1="&#x2026;" u2="&#x2a;" k="219" />
<hkern u1="&#x2039;" u2="x" k="10" />
<hkern u1="&#x2039;" u2="X" k="41" />
<hkern u1="&#x2039;" u2="V" k="61" />
<hkern u1="&#x203a;" u2="x" k="92" />
<hkern u1="&#x203a;" u2="X" k="82" />
<hkern u1="&#x203a;" u2="V" k="119" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="117" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="145" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="47" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="61" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,y,yacute,ydieresis" 	k="61" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="10" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="27" />
<hkern g1="G" 	g2="w" 	k="78" />
<hkern g1="G" 	g2="v,y,yacute,ydieresis" 	k="88" />
<hkern g1="G" 	g2="t" 	k="16" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="39" />
<hkern g1="G" 	g2="z" 	k="10" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="72" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="94" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="84" />
<hkern g1="K" 	g2="w" 	k="115" />
<hkern g1="K" 	g2="v,y,yacute,ydieresis" 	k="125" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="190" />
<hkern g1="K" 	g2="t" 	k="84" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="145" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="119" />
<hkern g1="K" 	g2="s" 	k="31" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="305" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="156" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="94" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="182" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="45" />
<hkern g1="L" 	g2="w" 	k="133" />
<hkern g1="L" 	g2="v,y,yacute,ydieresis" 	k="154" />
<hkern g1="L" 	g2="t" 	k="55" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="307" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="209" />
<hkern g1="L" 	g2="T" 	k="238" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="76" />
<hkern g1="L" 	g2="W" 	k="133" />
<hkern g1="L" 	g2="s" 	k="37" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="154" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="150" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="66" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="49" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="68" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="27" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="37" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="R" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="88" />
<hkern g1="R" 	g2="W" 	k="47" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="57" />
<hkern g1="S" 	g2="w" 	k="63" />
<hkern g1="S" 	g2="v,y,yacute,ydieresis" 	k="78" />
<hkern g1="S" 	g2="t" 	k="10" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="S" 	g2="W" 	k="16" />
<hkern g1="S" 	g2="z" 	k="20" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="47" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="211" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="246" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="59" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="188" />
<hkern g1="T" 	g2="w" 	k="188" />
<hkern g1="T" 	g2="v,y,yacute,ydieresis" 	k="184" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="221" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="182" />
<hkern g1="T" 	g2="s" 	k="178" />
<hkern g1="T" 	g2="z" 	k="139" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="154" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="248" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="211" />
<hkern g1="T" 	g2="AE" 	k="258" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="143" />
<hkern g1="T" 	g2="idieresis" 	k="-39" />
<hkern g1="T" 	g2="S" 	k="16" />
<hkern g1="T" 	g2="J" 	k="121" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="49" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="49" />
<hkern g1="W" 	g2="w" 	k="68" />
<hkern g1="W" 	g2="v,y,yacute,ydieresis" 	k="31" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="61" />
<hkern g1="W" 	g2="t" 	k="27" />
<hkern g1="W" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="78" />
<hkern g1="W" 	g2="s" 	k="39" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="76" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="W" 	g2="AE" 	k="195" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="61" />
<hkern g1="W" 	g2="S" 	k="27" />
<hkern g1="W" 	g2="J" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="121" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute,ydieresis" 	k="174" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="178" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="84" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="135" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="201" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="276" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="111" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="idieresis" 	k="-37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="111" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="111" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="37" />
<hkern g1="Z" 	g2="w" 	k="70" />
<hkern g1="Z" 	g2="v,y,yacute,ydieresis" 	k="94" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="115" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="74" />
<hkern g1="Z" 	g2="s" 	k="18" />
<hkern g1="Z" 	g2="idieresis" 	k="-45" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="33" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v,y,yacute,ydieresis" 	k="39" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="72" />
<hkern g1="comma,period,ellipsis" 	g2="quoteright,quotedblright" 	k="279" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="68" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="49" />
<hkern g1="comma,period,ellipsis" 	g2="quotedbl,quotesingle" 	k="182" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="199" />
<hkern g1="comma,period,ellipsis" 	g2="v,y,yacute,ydieresis" 	k="240" />
<hkern g1="comma,period,ellipsis" 	g2="quoteleft,quotedblleft" 	k="229" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="201" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="211" />
<hkern g1="comma,period,ellipsis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="139" />
<hkern g1="comma,period,ellipsis" 	g2="s" 	k="6" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="104" />
<hkern g1="comma,period,ellipsis" 	g2="parenright,bracketright,braceright" 	k="37" />
<hkern g1="comma,period,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="125" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="guillemotright,guilsinglright" 	k="43" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="80" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v,y,yacute,ydieresis" 	k="80" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="104" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="160" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="197" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="39" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotesinglbase,quotedblbase" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f,uniFB01,uniFB02" 	k="16" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="55" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="57" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="104" />
<hkern g1="f" 	g2="parenright,bracketright,braceright" 	k="-68" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="w" 	k="29" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v,y,yacute,ydieresis" 	k="66" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="246" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="55" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="s" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="z" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Z" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="S" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="92" />
<hkern g1="guillemotright,guilsinglright" 	g2="v,y,yacute,ydieresis" 	k="98" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="154" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="211" />
<hkern g1="guillemotright,guilsinglright" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="51" />
<hkern g1="guillemotright,guilsinglright" 	g2="s" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="104" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="45" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="37" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="63" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="w" 	k="57" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="v,y,yacute,ydieresis" 	k="68" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="180" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="221" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="86" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="39" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="29" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="78" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="166" />
<hkern g1="idieresis" 	g2="W" 	k="-68" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="29" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="29" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="k" 	g2="w" 	k="14" />
<hkern g1="k" 	g2="v,y,yacute,ydieresis" 	k="14" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="164" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="k" 	g2="s" 	k="20" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="4" />
<hkern g1="h,m,n" 	g2="quoteright,quotedblright" 	k="182" />
<hkern g1="h,m,n" 	g2="quotedbl,quotesingle" 	k="86" />
<hkern g1="h,m,n" 	g2="w" 	k="76" />
<hkern g1="h,m,n" 	g2="v,y,yacute,ydieresis" 	k="74" />
<hkern g1="h,m,n" 	g2="t" 	k="14" />
<hkern g1="h,m,n" 	g2="quoteleft,quotedblleft" 	k="143" />
<hkern g1="h,m,n" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="h,m,n" 	g2="T" 	k="162" />
<hkern g1="h,m,n" 	g2="W" 	k="150" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="115" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="guillemotright,guilsinglright" 	k="55" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="86" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="74" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v,y,yacute,ydieresis" 	k="80" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="76" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="258" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="182" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="W" 	k="84" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="35" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotesinglbase,quotedblbase" 	k="29" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,ellipsis" 	k="37" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Z" 	k="18" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="parenright,bracketright,braceright" 	k="61" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="119" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="v,y,yacute,ydieresis" 	k="57" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="18" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="221" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="102" />
<hkern g1="quoteleft,quotedblleft" 	g2="z" 	k="78" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="258" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,ellipsis" 	k="240" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde" 	k="123" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="29" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="68" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="109" />
<hkern g1="quoteright,quotedblright" 	g2="w" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="v,y,yacute,ydieresis" 	k="57" />
<hkern g1="quoteright,quotedblright" 	g2="t" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="29" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="174" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="115" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="78" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="246" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,ellipsis" 	k="354" />
<hkern g1="quoteright,quotedblright" 	g2="Z" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="125" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB01,uniFB02" 	k="68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="47" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="76" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v,y,yacute,ydieresis" 	k="117" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="66" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="145" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="96" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="10" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="quotedbl,quotesingle" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="86" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="154" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,ellipsis" 	k="182" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,p,r,ntilde" 	k="41" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="86" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="86" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="154" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="154" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="78" />
<hkern g1="s" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="s" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="s" 	g2="w" 	k="27" />
<hkern g1="s" 	g2="v,y,yacute,ydieresis" 	k="31" />
<hkern g1="s" 	g2="hyphen,uni00AD,endash,emdash" 	k="61" />
<hkern g1="s" 	g2="t" 	k="16" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="88" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="236" />
<hkern g1="s" 	g2="T" 	k="150" />
<hkern g1="s" 	g2="W" 	k="29" />
<hkern g1="s" 	g2="z" 	k="20" />
<hkern g1="s" 	g2="quotesinglbase,quotedblbase" 	k="6" />
<hkern g1="s" 	g2="Z" 	k="16" />
<hkern g1="s" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="39" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="76" />
<hkern g1="t" 	g2="quotedbl,quotesingle" 	k="39" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="53" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="147" />
<hkern g1="w" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="w" 	g2="W" 	k="31" />
<hkern g1="w" 	g2="s" 	k="14" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="113" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="88" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="195" />
<hkern g1="zero" 	g2="comma,period,ellipsis" 	k="104" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="51" />
<hkern g1="v,y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="184" />
<hkern g1="v,y,yacute,ydieresis" 	g2="T" 	k="211" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="v,y,yacute,ydieresis" 	g2="W" 	k="41" />
<hkern g1="v,y,yacute,ydieresis" 	g2="s" 	k="10" />
<hkern g1="v,y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="102" />
<hkern g1="v,y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="106" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="215" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="84" />
<hkern g1="z" 	g2="guillemotright,guilsinglright" 	k="29" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="74" />
<hkern g1="c,ccedilla" 	g2="w" 	k="18" />
<hkern g1="c,ccedilla" 	g2="v,y,yacute,ydieresis" 	k="25" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="94" />
<hkern g1="c,ccedilla" 	g2="T" 	k="160" />
<hkern g1="c,ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="c,ccedilla" 	g2="W" 	k="29" />
</font>
</defs></svg> 