<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="azo_sansbold_italic" horiz-adv-x="1224" >
<font-face units-per-em="2048" ascent="1581" descent="-467" />
<missing-glyph horiz-adv-x="475" />
<glyph unicode="&#xfb01;" horiz-adv-x="1333" d="M90 750l45 258h144l34 198q38 210 158 299q107 82 299 82q91 0 170 -20l-47 -264q-72 20 -125 20q-58 0 -96 -27q-39 -31 -56 -135l-26 -153h231l-45 -258h-231l-133 -750h-312l133 750h-143zM805 0l178 1008h311l-178 -1008h-311zM1016 1319q0 79 55.5 132.5t134.5 53.5 q72 0 121 -50t49 -120q0 -79 -55.5 -132.5t-134.5 -53.5q-72 0 -121 50t-49 120z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1335" d="M90 750l45 258h144l34 198q38 210 158 299q107 82 299 82q91 0 170 -20l-47 -264q-72 20 -125 20q-58 0 -96 -27q-39 -31 -56 -135l-26 -153h231l-45 -258h-231l-133 -750h-312l133 750h-143zM805 0l276 1565h312l-277 -1565h-311z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode="&#xd;" horiz-adv-x="716" />
<glyph unicode=" "  horiz-adv-x="475" />
<glyph unicode="&#x09;" horiz-adv-x="475" />
<glyph unicode="&#xa0;" horiz-adv-x="475" />
<glyph unicode="!" horiz-adv-x="589" d="M53 145q0 79 56 134t135 55q73 0 121.5 -49t48.5 -119q0 -79 -56 -134t-135 -55q-73 0 -121.5 49t-48.5 119zM186 494l99 989h362l-250 -989h-211z" />
<glyph unicode="&#x22;" horiz-adv-x="772" d="M184 942l62 578h260l-144 -578h-178zM561 942l62 578h260l-144 -578h-178z" />
<glyph unicode="#" horiz-adv-x="1277" d="M0 422l88 190h217l121 258h-217l88 191h217l195 422h204l-194 -422h221l195 422h204l-194 -422h217l-88 -191h-217l-121 -258h217l-88 -190h-217l-195 -422h-204l194 422h-221l-195 -422h-204l194 422h-217zM510 612h221l121 258h-221z" />
<glyph unicode="$" horiz-adv-x="1204" d="M113 217l53 301q101 -74 217 -117t213 -43q71 0 116.5 26.5t45.5 82.5q0 22 -9 39.5t-20.5 29.5t-39 27t-48 24t-63.5 27t-72 31q-55 25 -92.5 45.5t-77.5 51t-63.5 62.5t-38.5 77.5t-15 99.5q0 166 122.5 273.5t309.5 125.5l45 258h240l-47 -270q120 -22 244 -82 l-52 -289q-93 58 -201 90.5t-188 32.5q-66 0 -106.5 -24.5t-40.5 -77.5q0 -21 8.5 -38.5t19 -29t36.5 -25.5t45 -22.5t61 -25.5t69 -29q57 -25 96.5 -46t80.5 -52.5t65.5 -65.5t40.5 -82t16 -106q0 -168 -117 -271.5t-302 -124.5l-46 -256h-239l47 265q-168 25 -313 108z " />
<glyph unicode="%" horiz-adv-x="1912" d="M182 1075q0 79 26.5 155t74.5 137.5t124 99.5t166 38q155 0 250 -91.5t95 -244.5q0 -79 -26.5 -155t-75 -137.5t-124.5 -99.5t-166 -38q-155 0 -249.5 91.5t-94.5 244.5zM240 0l1292 1483h227l-1292 -1483h-227zM391 1092q0 -67 36 -114.5t103 -47.5q78 0 128.5 68 t50.5 155q0 67 -36.5 114.5t-103.5 47.5q-78 0 -128 -68t-50 -155zM1081 313q0 79 26.5 155t75 137.5t124.5 99.5t166 38q155 0 249.5 -91t94.5 -244q0 -79 -26.5 -155t-75 -138t-124.5 -100t-166 -38q-155 0 -249.5 91.5t-94.5 244.5zM1290 330q0 -67 36.5 -114.5 t103.5 -47.5q78 0 128 68t50 155q0 67 -36.5 114.5t-103.5 47.5q-78 0 -128 -68t-50 -155z" />
<glyph unicode="&#x26;" horiz-adv-x="1419" d="M72 377q0 275 329 434l-14 18q-96 125 -96 263q0 117 67 213.5t175.5 149t232.5 52.5q166 0 281.5 -94t115.5 -246q0 -122 -76 -221t-198 -168l-66 -37l158 -211l209 359h309l-342 -590l221 -295l-2 -4h-362l-70 92q-145 -115 -369 -115t-363.5 109t-139.5 291zM385 412 q0 -75 57.5 -122.5t155.5 -47.5q115 0 182 67l-227 301l-21 -8q-62 -25 -104.5 -75t-42.5 -115zM596 1098q0 -54 49 -121l33 -43l61 39q115 73 115 157q0 50 -31 80.5t-82 30.5q-61 0 -103 -41t-42 -102z" />
<glyph unicode="'" horiz-adv-x="395" d="M184 942l62 578h260l-144 -578h-178z" />
<glyph unicode="(" horiz-adv-x="724" d="M104 248q0 393 154 766q121 298 334 508h307q-236 -225 -375 -568q-137 -342 -137 -710q0 -382 152 -680h-265q-170 288 -170 684z" />
<glyph unicode=")" horiz-adv-x="724" d="M-160 -436q237 226 375 567q137 342 137 711q0 384 -151 680h264q170 -288 170 -684q0 -393 -154 -766q-121 -298 -334 -508h-307z" />
<glyph unicode="*" horiz-adv-x="905" d="M154 1122l90 181l245 -121l-57 -113zM195 827l239 213l86 -69l-192 -258zM514 1196l16 291h191l-88 -291h-119zM547 971l110 67l160 -215l-174 -110zM653 1180l287 123l25 -181l-293 -57z" />
<glyph unicode="+" horiz-adv-x="1208" d="M131 629l39 225h375l69 391h242l-70 -391h375l-39 -225h-374l-70 -391h-242l70 391h-375z" />
<glyph unicode="," horiz-adv-x="614" d="M-47 -301l219 614h293l-297 -614h-215z" />
<glyph unicode="-" horiz-adv-x="669" d="M76 440l45 254h495l-45 -254h-495z" />
<glyph unicode="." horiz-adv-x="616" d="M61 154q0 82 58 137t139 55q75 0 125.5 -51.5t50.5 -124.5q0 -83 -57 -138t-139 -55q-75 0 -126 52t-51 125z" />
<glyph unicode="/" horiz-adv-x="940" d="M-115 -78l1002 1639h248l-1002 -1639h-248z" />
<glyph unicode="0" horiz-adv-x="1288" d="M127 512q0 151 40.5 334.5t115.5 327.5q173 331 505 331q219 0 338 -140t119 -394q0 -152 -40 -335t-115 -327q-174 -332 -506 -332q-219 0 -338 140.5t-119 394.5zM455 526q0 -258 157 -258q136 0 215 207q38 102 64.5 237t26.5 244q0 258 -158 258q-137 0 -215 -206 q-38 -102 -64 -237.5t-26 -244.5z" />
<glyph unicode="1" horiz-adv-x="999" d="M190 995l54 299l667 211l-266 -1505h-326l193 1096z" />
<glyph unicode="2" horiz-adv-x="1153" d="M-66 4l562 596q168 178 231.5 267t63.5 177q0 83 -55.5 125.5t-151.5 42.5q-168 0 -342 -114l57 327q149 80 336 80q101 0 187.5 -26.5t153.5 -77t105.5 -131.5t38.5 -183q0 -75 -21 -146.5t-64 -143t-94.5 -136t-127.5 -145.5l-213 -227l2 -4h438l-51 -285h-1052z" />
<glyph unicode="3" horiz-adv-x="1136" d="M25 98l57 320q92 -70 194.5 -112t200.5 -42q110 0 176 51q68 59 68 136q0 76 -65 120.5t-175 44.5h-143l43 246h158q133 0 200 70q49 49 49 123q0 83 -60 123.5t-153 40.5q-75 0 -162.5 -26t-156.5 -67l55 312q141 65 307 65q73 0 143.5 -14t135.5 -44.5t113.5 -75 t78 -110.5t29.5 -145q0 -142 -82 -240q-67 -80 -186 -124v-5q197 -93 197 -299q0 -101 -49 -193.5t-138 -158.5q-157 -117 -395 -117q-241 0 -440 121z" />
<glyph unicode="4" horiz-adv-x="1314" d="M-23 322l947 1161h301l-162 -918h219l-43 -248h-221l-55 -317h-326l55 317h-712zM475 569l2 -4h260l27 152q15 86 53 272l-4 2q-35 -46 -182 -231z" />
<glyph unicode="5" horiz-adv-x="1146" d="M35 86l76 307q203 -129 391 -129q123 0 188 53q62 52 62 125q0 135 -226 199q-126 36 -342 43l197 799h793l-70 -285h-465l-74 -303q173 -18 297 -84q99 -54 157 -142.5t58 -207.5q0 -111 -50 -204.5t-138 -158.5q-162 -121 -410 -121q-221 0 -444 109z" />
<glyph unicode="6" horiz-adv-x="1245" d="M74 444q0 83 20.5 159t63 149.5t85 130.5t109.5 135l393 465h400l-504 -565l4 -5q47 19 113 19q76 0 150.5 -29.5t135.5 -82.5t98.5 -136t37.5 -182q0 -115 -48.5 -215t-134.5 -172q-165 -138 -403 -138q-81 0 -155.5 17t-141.5 54t-116 90.5t-78 132t-29 173.5zM408 449 q0 -85 57.5 -142t150.5 -57q101 0 170 69q68 68 68 162q0 91 -58 146t-149 55q-102 0 -170 -68q-69 -69 -69 -165z" />
<glyph unicode="7" horiz-adv-x="1110" d="M88 0l680 1198h-592l51 285h1047l2 -4l-840 -1479h-348z" />
<glyph unicode="8" horiz-adv-x="1302" d="M74 389q0 140 87.5 249t233.5 155v8q-151 87 -151 268q0 98 47.5 183.5t136.5 146.5q150 106 358 106q128 0 236 -42.5t177 -128.5t69 -201q0 -118 -70 -214.5t-186 -140.5v-6q93 -50 142.5 -130t49.5 -183q0 -108 -52.5 -202t-147.5 -161q-166 -119 -394 -119 q-144 0 -264.5 47.5t-196 143t-75.5 221.5zM406 432q0 -83 60.5 -134.5t158.5 -51.5q109 0 176 61q71 64 71 154q0 83 -64.5 134.5t-158.5 51.5q-108 0 -178 -67q-65 -62 -65 -148zM553 1051q0 -74 54.5 -120t135.5 -46q94 0 158 57q60 57 60 133q0 73 -55 118.5t-136 45.5 q-92 0 -154 -53q-63 -53 -63 -135z" />
<glyph unicode="9" horiz-adv-x="1234" d="M152 981q0 113 48.5 213.5t135.5 173.5q163 135 395 135q104 0 198 -30.5t167 -87t116 -144t43 -194.5q0 -143 -71.5 -279t-198.5 -293l-387 -475h-401v4l462 553l-2 2q-46 -8 -86 -8q-78 0 -153 31t-134 85.5t-95.5 136.5t-36.5 177zM475 1004q0 -89 56 -144t147 -55 q105 0 176 74q66 66 66 157q0 82 -56.5 138.5t-150.5 56.5q-99 0 -170 -68q-68 -65 -68 -159z" />
<glyph unicode=":" horiz-adv-x="616" d="M66 145q0 79 55.5 132t134.5 53q73 0 121.5 -49t48.5 -119q0 -79 -55.5 -132t-134.5 -53q-73 0 -121.5 49t-48.5 119zM190 846q0 79 56 131.5t135 52.5q73 0 121.5 -49t48.5 -119q0 -79 -56 -131.5t-135 -52.5q-73 0 -121.5 49t-48.5 119z" />
<glyph unicode=";" horiz-adv-x="616" d="M-47 -301l221 614h291l-297 -614h-215zM190 846q0 79 56 131.5t135 52.5q73 0 121.5 -49t48.5 -119q0 -79 -56 -131.5t-135 -52.5q-73 0 -121.5 49t-48.5 119z" />
<glyph unicode="&#x3c;" horiz-adv-x="1441" d="M184 659l29 164l1204 434l-41 -237l-768 -277v-4l668 -278l-41 -236z" />
<glyph unicode="=" horiz-adv-x="1208" d="M92 406l39 223h991l-39 -223h-991zM170 854l39 223h991l-39 -223h-991z" />
<glyph unicode="&#x3e;" horiz-adv-x="1441" d="M106 225l41 238l768 276v4l-667 279l41 235l1050 -434l-28 -164z" />
<glyph unicode="?" horiz-adv-x="985" d="M141 145q0 79 56 134t135 55q73 0 121.5 -49t48.5 -119q0 -79 -56 -134t-135 -55q-73 0 -121.5 49t-48.5 119zM231 457l66 377q86 0 157.5 10t134.5 32.5t98.5 65t35.5 100.5q0 80 -61.5 124t-161.5 44q-128 0 -256 -67l57 321q103 41 219 41q81 0 157.5 -17.5 t144.5 -53.5t118 -87t79.5 -123t29.5 -157q0 -190 -136.5 -314.5t-345.5 -148.5l-55 -147h-281z" />
<glyph unicode="@" horiz-adv-x="1597" d="M41 426q0 163 61.5 316t168.5 269.5t261.5 186.5t329.5 70q149 0 279 -51t223 -140t147 -216t54 -273q0 -94 -22 -177.5t-65 -151.5t-115 -108t-165 -40q-157 0 -233 98h-4q-49 -46 -116 -73t-140 -27q-138 0 -222 91.5t-84 239.5q0 86 30.5 167.5t85 147t138.5 105 t183 39.5q173 0 340 -117l-56 -315q-10 -58 -10 -90q0 -127 115 -127q54 0 93.5 29t61 77.5t31.5 101.5t10 111q0 155 -66.5 282.5t-195.5 205.5t-297 78q-141 0 -267.5 -56t-216 -149.5t-142 -221t-52.5 -266.5q0 -161 67.5 -290t197 -204t299.5 -75q210 0 389 117l53 -119 q-193 -131 -445 -131q-150 0 -280 50t-223.5 139t-147 217.5t-53.5 279.5zM606 444q0 -75 36.5 -120t107.5 -45q94 0 161 73v31q0 35 13 115l34 196q-61 35 -124 35q-56 0 -101 -27t-72 -70.5t-41 -92t-14 -95.5z" />
<glyph unicode="A" horiz-adv-x="1503" d="M-84 0l846 1483h317l328 -1483h-336l-63 311h-576l-174 -311h-342zM573 567h381l-36 178q-35 167 -62 332h-4q-40 -79 -180 -334z" />
<glyph unicode="B" horiz-adv-x="1296" d="M45 0l262 1483h445q246 0 372 -76q154 -93 154 -272q0 -122 -74 -213t-203 -131v-5q107 -43 163.5 -121t56.5 -186q0 -111 -56 -210t-155 -163q-165 -106 -465 -106h-500zM420 279h188q88 0 136.5 12.5t82.5 40.5q66 56 66 145q0 86 -63 127t-173 41h-172zM524 868h187 q45 0 84 8.5t75.5 27.5t58 56t21.5 89q0 79 -57 118q-55 37 -170 37h-139z" />
<glyph unicode="C" horiz-adv-x="1361" d="M111 637q0 174 67.5 341.5t192.5 291.5q238 235 600 235q216 0 434 -98l-55 -315q-204 120 -394 120q-215 0 -358 -143q-76 -76 -118 -184.5t-42 -218.5q0 -188 118 -292t321 -104q94 0 195.5 26t187.5 73l-58 -326q-171 -66 -371 -66q-153 0 -284.5 44.5t-228.5 127 t-152 208t-55 280.5z" />
<glyph unicode="D" horiz-adv-x="1507" d="M45 0l262 1483h375q352 0 557 -152q115 -86 178.5 -217.5t63.5 -294.5q0 -170 -70 -331t-207 -279q-246 -209 -637 -209h-522zM422 285h168q275 0 420 151q69 72 106 168.5t37 196.5q0 200 -139 305q-122 92 -342 92h-88z" />
<glyph unicode="E" horiz-adv-x="1126" d="M45 0l262 1483h899l-51 -285h-573l-52 -291h488l-47 -270h-488l-61 -352h594l-51 -285h-920z" />
<glyph unicode="F" horiz-adv-x="1136" d="M45 0l262 1483h916l-52 -285h-587l-58 -319h510l-47 -271h-512l-106 -608h-326z" />
<glyph unicode="G" horiz-adv-x="1458" d="M109 631q0 176 69.5 347t200.5 298q240 229 596 229q241 0 471 -114l-57 -324q-219 145 -431 145q-222 0 -362 -143q-77 -78 -118.5 -186.5t-41.5 -220.5q0 -183 109 -291.5t305 -108.5q57 0 131 17l43 239h-236l48 270h561l-127 -720q-200 -91 -445 -91 q-206 0 -366.5 74t-255 223.5t-94.5 356.5z" />
<glyph unicode="H" horiz-adv-x="1540" d="M45 0l262 1483h326l-101 -574h621l100 574h326l-262 -1483h-326l111 625h-621l-110 -625h-326z" />
<glyph unicode="I" horiz-adv-x="593" d="M45 0l262 1483h326l-262 -1483h-326z" />
<glyph unicode="J" horiz-adv-x="821" d="M-57 23l51 290q108 -53 194 -53q75 0 111.5 47t52.5 144l183 1032h325l-188 -1067q-35 -200 -140 -310q-126 -129 -342 -129q-130 0 -247 46z" />
<glyph unicode="K" horiz-adv-x="1370" d="M45 0l262 1483h326l-111 -631l4 -2l609 633h393l-672 -688l430 -795h-379l-397 762h-4l-135 -762h-326z" />
<glyph unicode="L" horiz-adv-x="1122" d="M45 0l262 1483h326l-211 -1198h635l-51 -285h-961z" />
<glyph unicode="M" horiz-adv-x="1746" d="M45 0l262 1483h297l320 -625l538 625h324l-262 -1483h-326l105 594q52 291 71 385h-4q-170 -205 -270 -320l-260 -301l-146 289q-94 185 -164 334h-4q-28 -195 -61 -377l-107 -604h-313z" />
<glyph unicode="N" horiz-adv-x="1605" d="M45 0l262 1483h301l342 -619q116 -211 205 -379l4 2q29 190 68 406l104 590h314l-263 -1483h-301l-342 618q-92 168 -204 379l-5 -2q-32 -215 -67 -405l-105 -590h-313z" />
<glyph unicode="O" horiz-adv-x="1644" d="M111 664q0 153 53.5 304.5t157.5 270.5q109 127 260 196.5t329 69.5q194 0 352.5 -81.5t254.5 -239.5t96 -365q0 -153 -53.5 -304.5t-157.5 -270.5q-109 -127 -260.5 -197t-329.5 -70q-144 0 -272 48t-223.5 135t-151 217.5t-55.5 286.5zM438 688q0 -116 45.5 -210.5 t134.5 -151t207 -56.5q200 0 328 156q61 74 97 171t36 198q0 179 -105 298t-282 119q-201 0 -328 -155q-61 -74 -97 -171t-36 -198z" />
<glyph unicode="P" horiz-adv-x="1296" d="M45 0l262 1483h420q298 0 442 -113q154 -121 154 -315q0 -104 -46 -206t-136 -175q-173 -144 -477 -144h-199l-94 -530h-326zM512 795h188q155 0 224 63q71 61 71 162q0 87 -65 133q-64 45 -207 45h-139z" />
<glyph unicode="Q" horiz-adv-x="1644" d="M111 664q0 153 53.5 304.5t157.5 270.5q109 127 260 196.5t329 69.5q194 0 352.5 -81.5t254.5 -239.5t96 -365q0 -153 -53.5 -304.5t-157.5 -270.5q-116 -134 -275 -203v-4q200 -125 402 -207l-297 -174q-257 117 -481 324q-134 10 -250.5 62t-203.5 138.5t-137 211 t-50 272.5zM438 688q0 -116 45.5 -210.5t134.5 -151t207 -56.5q200 0 328 156q61 74 97 171t36 198q0 179 -105 298t-282 119q-201 0 -328 -155q-61 -74 -97 -171t-36 -198z" />
<glyph unicode="R" horiz-adv-x="1296" d="M45 0l262 1483h430q273 0 412 -99q70 -49 110.5 -126.5t40.5 -174.5q0 -170 -104 -299q-87 -107 -252 -163l295 -621h-367l-251 586h-146l-104 -586h-326zM516 829h191q136 0 202 58q64 54 64 141q0 83 -62 127q-61 43 -188 43h-141z" />
<glyph unicode="S" horiz-adv-x="1150" d="M39 125l59 338q94 -86 223 -145.5t242 -59.5q87 0 139 41q58 46 58 117q0 23 -6 42t-21.5 36t-30.5 30t-43 28.5t-49.5 26t-60.5 29t-64 31.5q-60 30 -101.5 54.5t-85 60t-69.5 73t-42.5 89t-16.5 112.5q0 114 53 213.5t148 165.5q140 98 342 98q116 0 234 -34t218 -95 l-57 -321q-99 73 -214 120.5t-208 47.5q-80 0 -129 -35q-57 -42 -57 -119q0 -21 5 -38.5t20.5 -34.5t26.5 -28t41 -27.5t45.5 -24.5t58 -28t61.5 -30q66 -32 110 -57.5t89.5 -61.5t72 -74t43.5 -90.5t17 -115.5q0 -110 -45.5 -204t-124.5 -157q-151 -121 -383 -121 q-129 0 -258.5 40t-239.5 108z" />
<glyph unicode="T" horiz-adv-x="1363" d="M168 1198l51 285h1272l-51 -285h-473l-211 -1198h-326l211 1198h-473z" />
<glyph unicode="U" horiz-adv-x="1511" d="M133 483q0 100 23 232l135 768h325l-135 -770q-18 -108 -18 -172q0 -132 67.5 -201.5t204.5 -69.5q173 0 266 113q75 92 113 309l139 791h314l-142 -803q-32 -181 -77.5 -294t-118.5 -193q-92 -102 -229 -159t-298 -57q-255 0 -412 135.5t-157 370.5z" />
<glyph unicode="V" horiz-adv-x="1429" d="M172 1483h336l119 -639q28 -150 73 -420h5q167 322 221 424l338 635h338l-811 -1483h-324z" />
<glyph unicode="W" horiz-adv-x="2166" d="M193 1483h335l76 -637q31 -264 41 -383h4q66 148 180 385l308 635h237l84 -635q31 -244 45 -385h4q62 142 179 389l301 631h333l-716 -1483h-344l-70 524q-25 199 -41 353h-4q-51 -113 -168 -357l-252 -520h-340z" />
<glyph unicode="X" horiz-adv-x="1392" d="M-125 0l666 793l-330 690h364l107 -232q53 -114 96 -215h4q52 68 170 213l191 234h379l-576 -690l385 -793h-373l-149 326q-51 108 -102 223h-5q-62 -83 -176 -221l-268 -328h-383z" />
<glyph unicode="Y" horiz-adv-x="1398" d="M154 1483h362l123 -254q67 -140 131 -285h4q113 141 244 301l196 238h377l-694 -811l-119 -672h-325l116 668z" />
<glyph unicode="Z" horiz-adv-x="1271" d="M-59 4l876 1190l-2 4h-614l51 285h1155l2 -4l-877 -1190l3 -4h649l-51 -285h-1190z" />
<glyph unicode="[" horiz-adv-x="718" d="M-16 -436l346 1958h499l-38 -224h-213l-267 -1511h211l-39 -223h-499z" />
<glyph unicode="\" horiz-adv-x="940" d="M199 1561h227l395 -1639h-227z" />
<glyph unicode="]" horiz-adv-x="718" d="M-98 -436l39 223h213l266 1511h-211l39 224h500l-347 -1958h-499z" />
<glyph unicode="^" horiz-adv-x="1122" d="M139 741l494 764h207l227 -764h-240l-141 451h-2l-301 -451h-244z" />
<glyph unicode="_" horiz-adv-x="1005" d="M-143 -305l24 135h1006l-25 -135h-1005z" />
<glyph unicode="`" horiz-adv-x="1073" d="M457 1489h268l94 -336h-205z" />
<glyph unicode="a" horiz-adv-x="1222" d="M70 412q0 124 48 243.5t138 208.5q171 166 436 166q220 0 471 -119l-159 -911h-306l13 68l-4 2q-108 -93 -248 -93q-169 0 -279 121t-110 314zM383 446q0 -101 50.5 -159t135.5 -58q93 0 176 74l76 434q-76 35 -157 35q-124 0 -203 -94q-37 -44 -57.5 -107t-20.5 -125z " />
<glyph unicode="b" horiz-adv-x="1218" d="M47 35l270 1530h312l-109 -607l4 -2q95 74 228 74q171 0 284 -122.5t113 -315.5q0 -119 -46.5 -236.5t-137.5 -205.5q-179 -173 -498 -173q-190 0 -420 58zM395 246q58 -15 119 -15q158 0 242 99q37 43 58.5 105t21.5 124q0 97 -53 155t-142 58q-86 0 -162 -51z" />
<glyph unicode="c" horiz-adv-x="1040" d="M63 430q0 123 52.5 246t156.5 211q81 69 189 106t223 37q169 0 324 -76l-50 -282q-145 96 -286 96q-119 0 -197 -72q-48 -44 -73 -105.5t-25 -125.5q0 -100 64.5 -161.5t183.5 -61.5q143 0 290 75l-51 -286q-114 -51 -278 -51q-100 0 -191 27.5t-166.5 80.5t-120.5 141.5 t-45 200.5z" />
<glyph unicode="d" horiz-adv-x="1222" d="M70 414q0 125 48 245t138 207q167 164 414 164q97 0 198 -33l101 568h311l-276 -1565h-306l13 66l-4 2q-101 -91 -246 -91q-170 0 -280.5 121.5t-110.5 315.5zM383 449q0 -99 51 -158.5t135 -59.5q93 0 176 72l76 428q-77 43 -159 43q-124 0 -203 -96q-76 -95 -76 -229z " />
<glyph unicode="e" horiz-adv-x="1140" d="M66 434q0 118 45.5 232.5t130.5 199.5q164 164 397 164q122 0 221.5 -47t161 -144.5t61.5 -230.5q0 -100 -24 -186h-686q10 -101 78.5 -152t175.5 -51q81 0 173.5 26.5t166.5 65.5l-52 -270q-148 -64 -329 -64q-105 0 -197.5 28.5t-165 83.5t-115 144t-42.5 201zM399 618 h402q-1 81 -49 128t-127 47q-77 0 -135.5 -47.5t-90.5 -127.5z" />
<glyph unicode="f" horiz-adv-x="768" d="M90 750l45 258h144l34 198q38 210 158 299q107 82 299 82q91 0 170 -20l-47 -264q-72 20 -125 20q-58 0 -96 -27q-39 -31 -56 -135l-26 -153h231l-45 -258h-231l-133 -750h-312l133 750h-143z" />
<glyph unicode="g" horiz-adv-x="1204" d="M23 -387l51 291q177 -111 338 -111q222 0 264 230l10 57l-4 2q-103 -70 -219 -70q-165 0 -279 117t-114 307q0 118 46.5 234t133.5 200q163 160 422 160q118 0 245 -32t228 -85l-152 -858q-46 -260 -197.5 -394t-404.5 -134q-195 0 -368 86zM377 467q0 -94 52 -149.5 t140 -55.5q84 0 158 49l76 430q-68 31 -152 31q-119 0 -196 -88q-78 -94 -78 -217z" />
<glyph unicode="h" horiz-adv-x="1228" d="M41 0l276 1565h312l-117 -647l4 -3q133 115 305 115q145 0 228.5 -86t83.5 -227q0 -43 -13 -123l-104 -594h-311l100 569q8 46 8 74q0 54 -31 86.5t-94 32.5q-112 0 -217 -84l-119 -678h-311z" />
<glyph unicode="i" horiz-adv-x="569" d="M41 0l178 1008h311l-178 -1008h-311zM252 1319q0 79 55.5 132.5t134.5 53.5q72 0 121 -50t49 -120q0 -79 -55.5 -132.5t-134.5 -53.5q-72 0 -121 50t-49 120z" />
<glyph unicode="j" horiz-adv-x="575" d="M-266 -453l47 265q81 -23 121 -23q65 0 93.5 38.5t41.5 119.5l186 1061h312l-193 -1096q-34 -189 -125 -277q-111 -108 -311 -108q-93 0 -172 20zM256 1319q0 79 55.5 132.5t134.5 53.5q72 0 121 -50t49 -120q0 -79 -55.5 -132.5t-134.5 -53.5q-72 0 -121 50t-49 120z " />
<glyph unicode="k" horiz-adv-x="1167" d="M41 0l276 1565h312l-170 -957h4l426 400h360l-487 -451l293 -557h-340l-266 524h-5l-92 -524h-311z" />
<glyph unicode="l" horiz-adv-x="571" d="M41 0l276 1565h312l-277 -1565h-311z" />
<glyph unicode="m" horiz-adv-x="1863" d="M41 0l178 1008h305l-16 -90l4 -3q139 115 289 115q89 0 160.5 -38.5t111.5 -106.5q162 145 359 145q155 0 245 -86t90 -229q0 -53 -12 -121l-104 -594h-312l101 573q8 48 8 76q0 53 -33.5 83t-93.5 30q-105 0 -205 -84q-3 -45 -10 -84l-105 -594h-311l101 573q8 43 8 64 q0 64 -33.5 94.5t-89.5 30.5q-52 0 -108 -23t-97 -61l-119 -678h-311z" />
<glyph unicode="n" horiz-adv-x="1228" d="M41 0l178 1008h305l-16 -93l4 -2q142 117 309 117q145 0 228.5 -86t83.5 -227q0 -43 -13 -123l-104 -594h-311l100 569q8 46 8 74q0 54 -31 86.5t-94 32.5q-112 0 -217 -84l-119 -678h-311z" />
<glyph unicode="o" d="M68 444q0 113 43.5 224t124.5 194q165 168 417 168q218 0 360 -127t142 -340q0 -113 -43.5 -224t-124.5 -194q-165 -168 -418 -168q-142 0 -255 55.5t-179.5 162.5t-66.5 249zM379 465q0 -98 54.5 -159.5t152.5 -61.5q112 0 180 78q37 43 57.5 102t20.5 119 q0 98 -54.5 159.5t-152.5 61.5q-112 0 -180 -78q-37 -43 -57.5 -102t-20.5 -119z" />
<glyph unicode="p" horiz-adv-x="1218" d="M-39 -451l258 1459h305l-10 -56l4 -2q92 80 232 80q175 0 286 -124.5t111 -319.5q0 -123 -45.5 -241t-126.5 -202q-73 -77 -179 -121.5t-231 -44.5q-109 0 -207 37l-4 -2l-82 -463h-311zM399 272q64 -41 160 -41q126 0 201 91q74 90 74 223q0 103 -54 164t-147 61 q-83 0 -154 -49z" />
<glyph unicode="q" horiz-adv-x="1220" d="M70 420q0 124 48.5 241.5t137.5 202.5q172 166 457 166q220 0 452 -94l-243 -1387h-312l88 502l-4 2q-95 -76 -227 -76q-171 0 -284 123.5t-113 319.5zM383 449q0 -99 52.5 -158.5t139.5 -59.5q82 0 164 56l82 463q-63 24 -139 24q-140 0 -217 -94q-39 -46 -60.5 -108 t-21.5 -123z" />
<glyph unicode="r" horiz-adv-x="858" d="M41 0l178 1008h305l-22 -136l4 -2q133 150 289 150q44 0 82 -14l-46 -291q-51 14 -88 14q-81 0 -157 -33t-127 -86l-107 -610h-311z" />
<glyph unicode="s" horiz-adv-x="1015" d="M41 92l49 275q87 -64 202.5 -105t201.5 -41q54 0 86 19t32 55q0 20 -11 34t-36 24.5t-49 17.5t-68 19t-75 22q-123 40 -186.5 101t-63.5 163q0 84 43 157t121 123q118 74 282 74q209 0 396 -96l-45 -260q-83 52 -186.5 82t-186.5 30q-47 0 -74 -12q-37 -16 -37 -55 q0 -17 8.5 -29.5t21 -20.5t36 -16t45 -13t57.5 -14.5t64 -17.5q126 -38 192 -100t66 -166q0 -75 -32.5 -144.5t-88.5 -117.5q-120 -103 -328 -103q-235 0 -436 115z" />
<glyph unicode="t" horiz-adv-x="845" d="M90 750l45 258h164l49 278l316 19l-54 -297h264l-45 -258h-264l-57 -330q-8 -46 -8 -72q0 -108 112 -108q67 0 133 32l-47 -268q-76 -27 -168 -27q-69 0 -130 17.5t-111.5 53t-79.5 96t-29 139.5q0 39 15 131l59 336h-164z" />
<glyph unicode="u" d="M94 289q0 59 12 129l105 590h311l-100 -566q-8 -44 -8 -80q0 -56 34.5 -87t98.5 -31q114 0 207 90l118 674h312l-178 -1008h-306l17 94l-4 2q-63 -58 -143.5 -88.5t-161.5 -30.5q-140 0 -227 86.5t-87 225.5z" />
<glyph unicode="v" horiz-adv-x="1122" d="M96 1008h318l65 -334q43 -225 56 -303h4q67 128 168 311l180 326h319l-592 -1008h-297z" />
<glyph unicode="w" horiz-adv-x="1658" d="M96 1008h316l47 -318q23 -157 37 -291h4q71 149 145 291l164 318h231l52 -318q21 -132 41 -291h4q70 153 139 291l158 318h309l-522 -1008h-291l-43 268q-23 143 -41 285h-4q-56 -118 -142 -283l-139 -270h-299z" />
<glyph unicode="x" horiz-adv-x="1081" d="M-111 0l478 545l-258 463h325l62 -123q14 -29 82 -172h4q50 66 141 178l96 117h334l-424 -476l293 -532h-334l-90 178q-17 33 -47.5 96.5t-36.5 75.5h-4q-29 -38 -137 -172l-148 -178h-336z" />
<glyph unicode="y" horiz-adv-x="1112" d="M-33 -451l369 572l-250 887h322l63 -263q44 -179 70 -307h4q108 187 186 312l158 258h315l-911 -1459h-326z" />
<glyph unicode="z" horiz-adv-x="999" d="M-55 4l571 741l-2 5h-387l45 258h881l2 -4l-572 -742l2 -4h416l-45 -258h-909z" />
<glyph unicode="{" horiz-adv-x="786" d="M57 430l39 225h66q73 0 106 32.5t47 111.5l76 426q27 150 124.5 234.5t254.5 84.5q80 0 166 -22l-39 -217q-37 12 -86 12q-61 0 -93.5 -31.5t-45.5 -109.5l-66 -367q-21 -115 -62.5 -175.5t-123.5 -84.5v-4q104 -42 104 -166q0 -39 -12 -109l-61 -352q-7 -35 -7 -57 q0 -53 29.5 -76t87.5 -23q21 0 66 5l-39 -218q-46 -8 -101 -8q-152 0 -244.5 69t-92.5 202q0 40 8 86l67 385q6 41 6 63q0 44 -26 64t-86 20h-62z" />
<glyph unicode="|" horiz-adv-x="647" d="M35 -451l368 2089h240l-369 -2089h-239z" />
<glyph unicode="}" horiz-adv-x="786" d="M-135 -436l39 217q37 -12 86 -12q61 0 93.5 31.5t45.5 109.5l66 366q21 115 62.5 176t123.5 85v4q-105 42 -105 166q0 32 13 108l61 352q6 30 6 58q0 53 -29 75.5t-87 22.5q-30 0 -66 -4l39 217q46 8 100 8q152 0 245 -68.5t93 -201.5q0 -40 -8 -86l-68 -385 q-6 -41 -6 -64q0 -44 26.5 -64t86.5 -20h61l-38 -225h-66q-73 0 -106.5 -32.5t-47.5 -110.5l-75 -426q-27 -150 -124.5 -235t-254.5 -85q-76 0 -166 23z" />
<glyph unicode="~" horiz-adv-x="1134" d="M174 575l47 273q37 35 91.5 56.5t115.5 21.5q57 0 103 -16t118 -52q45 -23 64 -32.5t54 -20t67 -10.5q117 0 204 116l6 -4l-47 -272q-37 -35 -91 -56.5t-115 -21.5q-40 0 -79.5 10t-67 22t-75.5 36q-46 24 -64 32.5t-53 19.5t-67 11q-118 0 -205 -117z" />
<glyph unicode="&#xa1;" horiz-adv-x="589" d="M-57 -475l250 989h210l-98 -989h-362zM176 842q0 79 56 133.5t135 54.5q73 0 121.5 -49t48.5 -119q0 -79 -56 -133.5t-135 -54.5q-73 0 -121.5 49t-48.5 119z" />
<glyph unicode="&#xa2;" horiz-adv-x="1036" d="M84 668q0 123 52.5 245.5t156.5 210.5q114 99 280 131l48 265h239l-45 -260q104 -17 213 -68l-49 -283q-147 97 -287 97q-118 0 -196 -72q-48 -44 -73.5 -106t-25.5 -126q0 -100 64.5 -161.5t183.5 -61.5q142 0 291 76l-51 -287q-104 -48 -254 -51l-45 -254h-240l51 287 q-140 46 -226.5 152t-86.5 266z" />
<glyph unicode="&#xa3;" horiz-adv-x="1171" d="M41 0l41 238q103 56 164 149.5t65 216.5h-164l41 238h95q-13 102 -13 147q0 239 158.5 377.5t407.5 138.5q226 0 387 -110l-52 -301q-173 120 -342 120q-104 0 -166.5 -63.5t-62.5 -183.5q0 -43 8 -125h328l-41 -238h-270q-7 -202 -160 -315v-4h614l-51 -285h-987z" />
<glyph unicode="&#xa4;" horiz-adv-x="1193" d="M25 354l200 170q-22 64 -22 140q0 145 78 264l-125 153l217 187l123 -152q88 37 182 37q128 0 219 -55l201 170l151 -189l-200 -170q22 -64 22 -139q0 -145 -78 -264l125 -154l-217 -186l-123 151q-86 -36 -182 -36q-128 0 -219 55l-201 -170zM434 684q0 -79 49 -132.5 t133 -53.5q95 0 159.5 75.5t64.5 176.5q0 79 -49.5 132.5t-133.5 53.5q-95 0 -159 -75.5t-64 -176.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1402" d="M176 420l37 211h352l17 92l-39 76h-299l37 209h155l-241 475h360l96 -199q67 -140 131 -285h4q113 141 244 301l152 183h374l-407 -475h162l-37 -209h-303l-62 -72l-18 -96h352l-37 -211h-352l-74 -420h-325l73 420h-352z" />
<glyph unicode="&#xa6;" horiz-adv-x="647" d="M35 -451l141 803h240l-142 -803h-239zM262 836l141 802h240l-141 -802h-240z" />
<glyph unicode="&#xa7;" horiz-adv-x="1177" d="M94 -223l47 272q177 -115 310 -115q70 0 118.5 30t48.5 87q0 22 -9.5 42t-22.5 35t-41 33.5t-49.5 31t-64.5 36t-71 39.5q-82 48 -131.5 89.5t-82.5 103.5t-33 139q0 112 61.5 194.5t171.5 131.5v4q-63 73 -63 188q0 110 64 199t170 136.5t231 47.5q174 0 325 -84 l-51 -262q-149 94 -266 94q-67 0 -114.5 -29.5t-47.5 -84.5q0 -18 4.5 -33.5t19 -31.5t25 -26.5t38.5 -28.5t42.5 -26.5t54.5 -30.5t58 -32q67 -38 109 -67.5t81.5 -70t57.5 -89.5t18 -111q0 -115 -62.5 -200.5t-169.5 -129.5v-4q60 -77 60 -188q0 -172 -135.5 -277.5 t-333.5 -105.5q-96 0 -195.5 25.5t-171.5 68.5zM414 629q0 -15 3 -28.5t11.5 -26.5t16 -23t23 -22t26.5 -19.5t33.5 -20.5t35 -19.5t40.5 -21t42 -22.5q64 7 110 41t46 90q0 13 -3.5 25.5t-7.5 22.5t-14 21t-17.5 19t-23.5 19t-25.5 17.5t-30 18t-31.5 17.5t-36 19.5 t-37 19.5q-71 -6 -116 -39t-45 -88z" />
<glyph unicode="&#xa8;" horiz-adv-x="1073" d="M289 1321q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5q0 -68 -49 -116t-117 -48q-65 0 -107 44.5t-42 107.5zM760 1321q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5q0 -68 -49 -116t-117 -48q-65 0 -107 44.5t-42 107.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1636" d="M94 741q0 213 100 388t276 275.5t392 100.5t392 -100.5t276 -275.5t100 -388t-100 -388t-276 -275.5t-392 -100.5t-392 100.5t-276 275.5t-100 388zM225 741q0 -179 79.5 -325t225.5 -231t332 -85t332 85t225.5 231t79.5 325t-79.5 325t-225.5 231t-332 85t-332 -85 t-225.5 -231t-79.5 -325zM453 750q0 183 125.5 301t314.5 118q145 0 248 -63v-199q-118 78 -232 78q-107 0 -175 -65.5t-68 -167.5q0 -100 67.5 -166t177.5 -66q130 0 240 80v-199q-104 -67 -264 -67q-188 0 -311 118.5t-123 297.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="792" d="M158 1116q0 160 110 274.5t279 114.5q81 0 169.5 -27.5t155.5 -76.5l-98 -565h-211l6 34l-4 2q-60 -51 -141 -51q-116 0 -191 81.5t-75 213.5zM379 1137q0 -60 31.5 -94.5t85.5 -34.5q53 0 98 30l45 258q-61 21 -102 21q-71 0 -114.5 -54t-43.5 -126z" />
<glyph unicode="&#xab;" horiz-adv-x="1159" d="M57 524l402 428h260l-416 -444l244 -453h-240zM520 524l402 428h260l-416 -444l244 -453h-240z" />
<glyph unicode="&#xac;" horiz-adv-x="1208" d="M129 629l39 225h985l-109 -616h-239l69 391h-745z" />
<glyph unicode="&#xad;" horiz-adv-x="669" d="M76 440l45 254h495l-45 -254h-495z" />
<glyph unicode="&#xae;" horiz-adv-x="911" d="M180 1124q0 163 110 272t275 109t275 -109t110 -272t-110 -272t-275 -109t-275 109t-110 272zM258 1124q0 -132 85.5 -219.5t221.5 -87.5t221.5 87.5t85.5 219.5t-85.5 220t-221.5 88t-221.5 -88t-85.5 -220zM414 918v413h161q68 0 114 -36t46 -99q0 -83 -80 -117 l99 -161h-117l-80 145h-41v-145h-102zM516 1137h49q66 0 66 53q0 51 -66 51h-49v-104z" />
<glyph unicode="&#xaf;" horiz-adv-x="1073" d="M354 1219l37 204h615l-37 -204h-615z" />
<glyph unicode="&#xb0;" horiz-adv-x="694" d="M160 1204q0 127 88 214t221 87t221 -87t88 -214t-88 -214t-221 -87t-221 87t-88 214zM293 1204q0 -71 51 -122.5t125 -51.5t125 51.5t51 122.5t-51 122.5t-125 51.5t-125 -51.5t-51 -122.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="1208" d="M74 131l39 223h927l-39 -223h-927zM195 817l38 225h345l57 330h242l-58 -330h344l-39 -225h-344l-57 -330h-242l58 330h-344z" />
<glyph unicode="&#xb2;" horiz-adv-x="749" d="M86 868l332 359q101 109 138.5 160.5t37.5 89.5q0 36 -29.5 55.5t-79.5 19.5q-105 0 -225 -75l47 239q108 51 221 51q130 0 220 -69.5t90 -196.5q0 -151 -154 -311l-102 -111l2 -4h217l-37 -211h-676z" />
<glyph unicode="&#xb3;" horiz-adv-x="727" d="M152 934l38 225q136 -100 244 -100q56 0 90 24q31 22 31 58q0 37 -35 58.5t-96 21.5h-90l31 172h96q76 0 112 32q27 24 27 56q0 39 -34 58.5t-87 19.5q-92 0 -203 -52l39 224q95 36 203 36q56 0 110.5 -13t104 -39t80.5 -73.5t31 -109.5q0 -81 -51 -139q-43 -49 -117 -74 v-4q123 -63 123 -182q0 -62 -31.5 -118t-89.5 -95q-103 -70 -250 -70q-154 0 -276 84z" />
<glyph unicode="&#xb4;" horiz-adv-x="1073" d="M483 1153l226 336h295l-299 -336h-222z" />
<glyph unicode="&#xb5;" horiz-adv-x="1245" d="M145 -451v1459h312v-574q0 -103 39 -145q39 -45 110 -45q55 0 110 26t95 64v674h311v-1008h-307v94l-4 2q-56 -53 -131.5 -86t-144.5 -33q-51 0 -89 15l-4 -2l35 -441h-332z" />
<glyph unicode="&#xb6;" horiz-adv-x="1226" d="M178 1067q0 104 57.5 200.5t155 156t207.5 59.5h643l-340 -1934h-242l306 1727h-144l-305 -1727h-242l211 1192q-134 7 -220.5 100t-86.5 226z" />
<glyph unicode="&#xb7;" horiz-adv-x="616" d="M141 610q0 82 58 137.5t139 55.5q75 0 125.5 -51.5t50.5 -124.5q0 -83 -57 -138t-140 -55q-75 0 -125.5 51.5t-50.5 124.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1073" d="M190 -393l62 121q71 -52 133 -52q33 0 55.5 14t22.5 40q0 69 -162 96l131 260l109 -39l-64 -129q141 -46 141 -172q0 -86 -64.5 -142.5t-164.5 -56.5q-108 0 -199 60z" />
<glyph unicode="&#xb9;" horiz-adv-x="634" d="M229 1430l39 217l443 120l-166 -903h-236l109 619z" />
<glyph unicode="&#xba;" horiz-adv-x="813" d="M164 1122q0 162 108 272.5t275 110.5q141 0 236.5 -82t95.5 -219q0 -162 -108 -272.5t-275 -110.5q-141 0 -236.5 82t-95.5 219zM385 1137q0 -55 31.5 -90t89.5 -35q68 0 109.5 50.5t41.5 127.5q0 55 -31 90t-89 35q-68 0 -110 -51t-42 -127z" />
<glyph unicode="&#xbb;" horiz-adv-x="1159" d="M-23 55l416 445l-243 452h239l250 -469l-401 -428h-261zM440 55l416 445l-244 452h240l250 -469l-402 -428h-260z" />
<glyph unicode="&#xbc;" horiz-adv-x="1949" d="M215 1167l39 217l442 121l-166 -903h-235l108 619zM266 -27l1225 1510h278l-1224 -1510h-279zM1008 180l585 709h224l-95 -537h117l-31 -176h-116l-31 -176h-236l31 176h-446zM1352 356l2 -4h133l14 80q6 40 29 141l-4 2q-46 -65 -105 -135z" />
<glyph unicode="&#xbd;" horiz-adv-x="2011" d="M215 1167l39 217l442 121l-166 -903h-235l108 619zM266 -27l1225 1510h278l-1224 -1510h-279zM1149 4l332 358q101 109 138.5 160.5t37.5 89.5q0 36 -29.5 56t-79.5 20q-104 0 -225 -76l47 240q108 51 221 51q130 0 220 -69.5t90 -196.5q0 -151 -154 -311l-102 -111l2 -4 h217l-37 -211h-676z" />
<glyph unicode="&#xbe;" horiz-adv-x="2043" d="M135 672l39 225q136 -100 244 -100q55 0 90 24q31 22 31 58q0 37 -35 58t-96 21h-91l31 172h96q76 0 113 33q27 24 27 56q0 39 -34 58t-87 19q-94 0 -203 -51l39 223q98 37 203 37q56 0 110 -13t104 -39t80.5 -73.5t30.5 -109.5q0 -82 -51 -140q-42 -47 -117 -73v-4 q123 -63 123 -183q0 -62 -31 -118t-89 -95q-101 -69 -250 -69q-155 0 -277 84zM360 -27l1225 1510h279l-1225 -1510h-279zM1102 180l586 709h223l-94 -537h116l-30 -176h-117l-31 -176h-235l30 176h-446zM1446 356l2 -4h133l14 80q6 40 29 141l-4 2q-53 -74 -104 -135z" />
<glyph unicode="&#xbf;" horiz-adv-x="1030" d="M-61 -59q0 190 136 314t345 148l55 148h281l-66 -377q-85 0 -157 -10.5t-135 -33t-98.5 -65t-35.5 -100.5q0 -80 61.5 -124t161.5 -44q126 0 256 68l-57 -322q-103 -41 -219 -41q-102 0 -195.5 28.5t-168.5 81.5t-119.5 138.5t-44.5 190.5zM485 842q0 79 56 133.5 t135 54.5q73 0 121.5 -49t48.5 -119q0 -79 -56 -133.5t-135 -54.5q-73 0 -121.5 49t-48.5 119z" />
<glyph unicode="&#xc0;" horiz-adv-x="1503" d="M-84 0l846 1483h317l328 -1483h-336l-63 311h-576l-174 -311h-342zM573 567h381l-36 178q-35 167 -62 332h-4q-40 -79 -180 -334zM756 1964h268l94 -336h-205z" />
<glyph unicode="&#xc1;" horiz-adv-x="1503" d="M-84 0l846 1483h317l328 -1483h-336l-63 311h-576l-174 -311h-342zM573 567h381l-36 178q-35 167 -62 332h-4q-40 -79 -180 -334zM782 1628l226 336h295l-299 -336h-222z" />
<glyph unicode="&#xc2;" horiz-adv-x="1503" d="M-84 0l846 1483h317l328 -1483h-336l-63 311h-576l-174 -311h-342zM573 567h381l-36 178q-35 167 -62 332h-4q-40 -79 -180 -334zM586 1628l311 336h244l170 -336h-203l-115 189l-188 -189h-219z" />
<glyph unicode="&#xc3;" horiz-adv-x="1503" d="M-84 0l846 1483h317l328 -1483h-336l-63 311h-576l-174 -311h-342zM573 567h381l-36 178q-35 167 -62 332h-4q-40 -79 -180 -334zM621 1645l41 229q72 76 182 76q64 0 170 -52q37 -17 53.5 -24.5t41.5 -14t48 -6.5q91 0 180 101l4 -2l-41 -230q-75 -75 -182 -75 q-63 0 -172 51q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-54 0 -95.5 -27t-84.5 -74z" />
<glyph unicode="&#xc4;" horiz-adv-x="1503" d="M-84 0l846 1483h317l328 -1483h-336l-63 311h-576l-174 -311h-342zM573 567h381l-36 178q-35 167 -62 332h-4q-40 -79 -180 -334zM588 1796q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5q0 -68 -49 -115.5t-117 -47.5q-65 0 -107 44t-42 107zM1059 1796q0 68 49 116 t117 48q65 0 107 -44.5t42 -107.5q0 -68 -49 -115.5t-117 -47.5q-65 0 -107 44t-42 107z" />
<glyph unicode="&#xc5;" horiz-adv-x="1503" d="M-84 0l846 1483h317l328 -1483h-336l-63 311h-576l-174 -311h-342zM573 567h381l-36 178q-35 167 -62 332h-4q-40 -79 -180 -334zM723 1815q0 111 79.5 193.5t196.5 82.5q105 0 176.5 -63.5t71.5 -167.5q0 -111 -79.5 -194t-196.5 -83q-104 0 -176 64t-72 168zM868 1821 q0 -43 28.5 -75t78.5 -32q53 0 90 40.5t37 98.5q0 43 -28.5 75t-78.5 32q-53 0 -90 -40.5t-37 -98.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1828" d="M-82 0l920 1483h1071l-51 -285h-539l-51 -291h452l-47 -270h-452l-62 -352h559l-51 -285h-885l56 311h-387l-191 -311h-342zM606 567h277l47 262q58 318 71 385h-4l-211 -352z" />
<glyph unicode="&#xc7;" horiz-adv-x="1361" d="M111 637q0 174 67.5 341.5t192.5 291.5q238 235 600 235q216 0 434 -98l-55 -315q-204 120 -394 120q-215 0 -358 -143q-76 -76 -118 -184.5t-42 -218.5q0 -188 118 -292t321 -104q94 0 195.5 26t187.5 73l-58 -326q-171 -66 -371 -66q-19 0 -40 3l-29 -62 q141 -46 141 -172q0 -86 -64.5 -142.5t-164.5 -56.5q-108 0 -199 60l62 121q71 -52 133 -52q33 0 55.5 14t22.5 40q0 69 -162 96l84 166q-252 41 -405.5 210t-153.5 435z" />
<glyph unicode="&#xc8;" horiz-adv-x="1126" d="M45 0l262 1483h899l-51 -285h-573l-52 -291h488l-47 -270h-488l-61 -352h594l-51 -285h-920zM584 1964h268l94 -336h-205z" />
<glyph unicode="&#xc9;" horiz-adv-x="1126" d="M45 0l262 1483h899l-51 -285h-573l-52 -291h488l-47 -270h-488l-61 -352h594l-51 -285h-920zM610 1628l226 336h294l-299 -336h-221z" />
<glyph unicode="&#xca;" horiz-adv-x="1126" d="M45 0l262 1483h899l-51 -285h-573l-52 -291h488l-47 -270h-488l-61 -352h594l-51 -285h-920zM414 1628l311 336h244l170 -336h-203l-115 189l-188 -189h-219z" />
<glyph unicode="&#xcb;" horiz-adv-x="1126" d="M45 0l262 1483h899l-51 -285h-573l-52 -291h488l-47 -270h-488l-61 -352h594l-51 -285h-920zM416 1796q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5q0 -68 -49 -115.5t-117 -47.5q-65 0 -107 44t-42 107zM887 1796q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5 q0 -68 -49 -115.5t-117 -47.5q-65 0 -107 44t-42 107z" />
<glyph unicode="&#xcc;" horiz-adv-x="593" d="M45 0l262 1483h326l-262 -1483h-326zM301 1964h268l95 -336h-205z" />
<glyph unicode="&#xcd;" horiz-adv-x="593" d="M45 0l262 1483h326l-262 -1483h-326zM328 1628l225 336h295l-299 -336h-221z" />
<glyph unicode="&#xce;" horiz-adv-x="593" d="M45 0l262 1483h326l-262 -1483h-326zM131 1628l311 336h244l170 -336h-203l-114 189l-189 -189h-219z" />
<glyph unicode="&#xcf;" horiz-adv-x="593" d="M45 0l262 1483h326l-262 -1483h-326zM133 1796q0 68 49 116t117 48q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -115.5t-117 -47.5q-65 0 -107.5 44t-42.5 107zM604 1796q0 68 49 116t117 48q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -115.5t-117 -47.5q-65 0 -107.5 44 t-42.5 107z" />
<glyph unicode="&#xd0;" horiz-adv-x="1523" d="M25 641l41 227h149l109 615h374q352 0 557 -152q115 -86 178.5 -217.5t63.5 -294.5q0 -171 -70 -331.5t-206 -278.5q-246 -209 -637 -209h-523l113 641h-149zM438 285h168q275 0 420 151q69 72 106 168.5t37 196.5q0 200 -139 305q-122 92 -342 92h-88l-59 -330h272 l-41 -227h-270z" />
<glyph unicode="&#xd1;" horiz-adv-x="1605" d="M45 0l262 1483h301l342 -619q116 -211 205 -379l4 2q29 190 68 406l104 590h314l-263 -1483h-301l-342 618q-92 168 -204 379l-5 -2q-32 -215 -67 -405l-105 -590h-313zM672 1645l41 229q72 76 182 76q64 0 170 -52q37 -17 53.5 -24.5t41.5 -14t48 -6.5q92 0 181 101 l4 -2l-41 -230q-75 -75 -183 -75q-63 0 -172 51q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-54 0 -95.5 -27t-84.5 -74z" />
<glyph unicode="&#xd2;" horiz-adv-x="1644" d="M111 664q0 153 53.5 304.5t157.5 270.5q109 127 260 196.5t329 69.5q194 0 352.5 -81.5t254.5 -239.5t96 -365q0 -153 -53.5 -304.5t-157.5 -270.5q-109 -127 -260.5 -197t-329.5 -70q-144 0 -272 48t-223.5 135t-151 217.5t-55.5 286.5zM438 688q0 -116 45.5 -210.5 t134.5 -151t207 -56.5q200 0 328 156q61 74 97 171t36 198q0 179 -105 298t-282 119q-201 0 -328 -155q-61 -74 -97 -171t-36 -198zM825 1964h269l94 -336h-205z" />
<glyph unicode="&#xd3;" horiz-adv-x="1644" d="M111 664q0 153 53.5 304.5t157.5 270.5q109 127 260 196.5t329 69.5q194 0 352.5 -81.5t254.5 -239.5t96 -365q0 -153 -53.5 -304.5t-157.5 -270.5q-109 -127 -260.5 -197t-329.5 -70q-144 0 -272 48t-223.5 135t-151 217.5t-55.5 286.5zM438 688q0 -116 45.5 -210.5 t134.5 -151t207 -56.5q200 0 328 156q61 74 97 171t36 198q0 179 -105 298t-282 119q-201 0 -328 -155q-61 -74 -97 -171t-36 -198zM852 1628l225 336h295l-299 -336h-221z" />
<glyph unicode="&#xd4;" horiz-adv-x="1644" d="M111 664q0 153 53.5 304.5t157.5 270.5q109 127 260 196.5t329 69.5q194 0 352.5 -81.5t254.5 -239.5t96 -365q0 -153 -53.5 -304.5t-157.5 -270.5q-109 -127 -260.5 -197t-329.5 -70q-144 0 -272 48t-223.5 135t-151 217.5t-55.5 286.5zM438 688q0 -116 45.5 -210.5 t134.5 -151t207 -56.5q200 0 328 156q61 74 97 171t36 198q0 179 -105 298t-282 119q-201 0 -328 -155q-61 -74 -97 -171t-36 -198zM655 1628l312 336h243l170 -336h-202l-115 189l-189 -189h-219z" />
<glyph unicode="&#xd5;" horiz-adv-x="1644" d="M111 664q0 153 53.5 304.5t157.5 270.5q109 127 260 196.5t329 69.5q194 0 352.5 -81.5t254.5 -239.5t96 -365q0 -153 -53.5 -304.5t-157.5 -270.5q-109 -127 -260.5 -197t-329.5 -70q-144 0 -272 48t-223.5 135t-151 217.5t-55.5 286.5zM438 688q0 -116 45.5 -210.5 t134.5 -151t207 -56.5q200 0 328 156q61 74 97 171t36 198q0 179 -105 298t-282 119q-201 0 -328 -155q-61 -74 -97 -171t-36 -198zM690 1645l41 229q72 76 182 76q64 0 170 -52q36 -17 53.5 -24.5t42.5 -14t48 -6.5q91 0 180 101l4 -2l-41 -230q-75 -75 -182 -75 q-63 0 -172 51q-34 17 -52 24.5t-42.5 14t-47.5 6.5q-54 0 -95.5 -27t-84.5 -74z" />
<glyph unicode="&#xd6;" horiz-adv-x="1644" d="M111 664q0 153 53.5 304.5t157.5 270.5q109 127 260 196.5t329 69.5q194 0 352.5 -81.5t254.5 -239.5t96 -365q0 -153 -53.5 -304.5t-157.5 -270.5q-109 -127 -260.5 -197t-329.5 -70q-144 0 -272 48t-223.5 135t-151 217.5t-55.5 286.5zM438 688q0 -116 45.5 -210.5 t134.5 -151t207 -56.5q200 0 328 156q61 74 97 171t36 198q0 179 -105 298t-282 119q-201 0 -328 -155q-61 -74 -97 -171t-36 -198zM657 1796q0 68 49 116t117 48q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -115.5t-117 -47.5q-65 0 -107.5 44t-42.5 107zM1128 1796 q0 68 49 116t117 48q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -115.5t-117 -47.5q-65 0 -107.5 44t-42.5 107z" />
<glyph unicode="&#xd7;" horiz-adv-x="1208" d="M150 457l333 284l-233 285l192 166l234 -285l332 285l137 -166l-334 -285l233 -284l-192 -166l-234 284l-331 -284z" />
<glyph unicode="&#xd8;" horiz-adv-x="1644" d="M59 72l181 176q-129 176 -129 416q0 153 53.5 304.5t157.5 270.5q109 127 260 196.5t329 69.5q273 0 463 -155l178 174l113 -113l-180 -176q129 -176 129 -416q0 -153 -53.5 -304.5t-157.5 -270.5q-109 -127 -260.5 -197t-329.5 -70q-272 0 -463 156l-178 -174zM438 688 q0 -117 43 -207l664 647q-100 84 -246 84q-201 0 -328 -155q-61 -74 -97 -171t-36 -198zM580 354q100 -84 245 -84q200 0 328 156q61 74 97 171t36 198q0 116 -43 206z" />
<glyph unicode="&#xd9;" horiz-adv-x="1511" d="M133 483q0 100 23 232l135 768h325l-135 -770q-18 -108 -18 -172q0 -132 67.5 -201.5t204.5 -69.5q173 0 266 113q75 92 113 309l139 791h314l-142 -803q-32 -181 -77.5 -294t-118.5 -193q-92 -102 -229 -159t-298 -57q-255 0 -412 135.5t-157 370.5zM766 1964h268 l94 -336h-204z" />
<glyph unicode="&#xda;" horiz-adv-x="1511" d="M133 483q0 100 23 232l135 768h325l-135 -770q-18 -108 -18 -172q0 -132 67.5 -201.5t204.5 -69.5q173 0 266 113q75 92 113 309l139 791h314l-142 -803q-32 -181 -77.5 -294t-118.5 -193q-92 -102 -229 -159t-298 -57q-255 0 -412 135.5t-157 370.5zM793 1628l225 336 h295l-299 -336h-221z" />
<glyph unicode="&#xdb;" horiz-adv-x="1511" d="M133 483q0 100 23 232l135 768h325l-135 -770q-18 -108 -18 -172q0 -132 67.5 -201.5t204.5 -69.5q173 0 266 113q75 92 113 309l139 791h314l-142 -803q-32 -181 -77.5 -294t-118.5 -193q-92 -102 -229 -159t-298 -57q-255 0 -412 135.5t-157 370.5zM596 1628l311 336 h244l170 -336h-203l-114 189l-189 -189h-219z" />
<glyph unicode="&#xdc;" horiz-adv-x="1511" d="M133 483q0 100 23 232l135 768h325l-135 -770q-18 -108 -18 -172q0 -132 67.5 -201.5t204.5 -69.5q173 0 266 113q75 92 113 309l139 791h314l-142 -803q-32 -181 -77.5 -294t-118.5 -193q-92 -102 -229 -159t-298 -57q-255 0 -412 135.5t-157 370.5zM598 1796 q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5q0 -68 -48.5 -115.5t-116.5 -47.5q-65 0 -107.5 44t-42.5 107zM1069 1796q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5q0 -68 -48.5 -115.5t-116.5 -47.5q-65 0 -107.5 44t-42.5 107z" />
<glyph unicode="&#xdd;" horiz-adv-x="1398" d="M154 1483h362l123 -254q67 -140 131 -285h4q113 141 244 301l196 238h377l-694 -811l-119 -672h-325l116 668zM731 1628l225 336h295l-299 -336h-221z" />
<glyph unicode="&#xde;" horiz-adv-x="1296" d="M45 0l262 1483h326l-45 -260h94q296 0 442 -115q72 -55 113 -135t41 -178q0 -104 -46 -206t-136 -177q-174 -142 -478 -142h-200l-47 -270h-326zM465 535h190q153 0 224 65q69 58 69 158q0 86 -63 131q-66 49 -207 49h-141z" />
<glyph unicode="&#xdf;" horiz-adv-x="1368" d="M41 0l205 1155q41 230 196 336q140 96 369 96q237 0 367 -104q110 -89 110 -244q0 -81 -31 -178.5t-90 -192.5q-46 4 -84 4q-32 0 -60.5 -4t-59 -13.5t-48.5 -31t-18 -51.5q0 -15 6 -28.5t13.5 -23t26.5 -22.5t31 -20t41.5 -22.5t45.5 -24.5q55 -31 92 -58t71.5 -64 t51 -83.5t16.5 -103.5q0 -158 -122 -251.5t-306 -93.5q-179 0 -370 91l47 270q74 -52 159.5 -84.5t149.5 -32.5q53 0 88 23t35 67q0 18 -6.5 32.5t-25.5 29t-35 24t-53 29.5t-62 35q-104 62 -156.5 128t-52.5 165q0 73 29 132.5t79.5 99t113 65t134.5 36.5q25 65 25 113 q0 57 -41 90q-45 35 -131 35q-103 0 -160 -43q-61 -47 -80 -158l-199 -1124h-311z" />
<glyph unicode="&#xe0;" horiz-adv-x="1222" d="M70 412q0 124 48 243.5t138 208.5q171 166 436 166q220 0 471 -119l-159 -911h-306l13 68l-4 2q-108 -93 -248 -93q-169 0 -279 121t-110 314zM383 446q0 -101 50.5 -159t135.5 -58q93 0 176 74l76 434q-76 35 -157 35q-124 0 -203 -94q-37 -44 -57.5 -107t-20.5 -125z M553 1489h268l94 -336h-204z" />
<glyph unicode="&#xe1;" horiz-adv-x="1222" d="M70 412q0 124 48 243.5t138 208.5q171 166 436 166q220 0 471 -119l-159 -911h-306l13 68l-4 2q-108 -93 -248 -93q-169 0 -279 121t-110 314zM383 446q0 -101 50.5 -159t135.5 -58q93 0 176 74l76 434q-76 35 -157 35q-124 0 -203 -94q-37 -44 -57.5 -107t-20.5 -125z M580 1153l225 336h295l-299 -336h-221z" />
<glyph unicode="&#xe2;" horiz-adv-x="1222" d="M70 412q0 124 48 243.5t138 208.5q171 166 436 166q220 0 471 -119l-159 -911h-306l13 68l-4 2q-108 -93 -248 -93q-169 0 -279 121t-110 314zM383 446q0 -101 50.5 -159t135.5 -58q93 0 176 74l76 434q-76 35 -157 35q-124 0 -203 -94q-37 -44 -57.5 -107t-20.5 -125z M383 1153l311 336h244l170 -336h-203l-114 188l-189 -188h-219z" />
<glyph unicode="&#xe3;" horiz-adv-x="1222" d="M70 412q0 124 48 243.5t138 208.5q171 166 436 166q220 0 471 -119l-159 -911h-306l13 68l-4 2q-108 -93 -248 -93q-169 0 -279 121t-110 314zM383 446q0 -101 50.5 -159t135.5 -58q93 0 176 74l76 434q-76 35 -157 35q-124 0 -203 -94q-37 -44 -57.5 -107t-20.5 -125z M418 1169l41 230q72 76 182 76q64 0 170 -52q37 -17 53.5 -24.5t41.5 -14t48 -6.5q92 0 181 101l4 -2l-41 -230q-76 -76 -183 -76q-39 0 -76.5 12.5t-95.5 39.5q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-54 0 -95.5 -27t-84.5 -74z" />
<glyph unicode="&#xe4;" horiz-adv-x="1222" d="M70 412q0 124 48 243.5t138 208.5q171 166 436 166q220 0 471 -119l-159 -911h-306l13 68l-4 2q-108 -93 -248 -93q-169 0 -279 121t-110 314zM383 446q0 -101 50.5 -159t135.5 -58q93 0 176 74l76 434q-76 35 -157 35q-124 0 -203 -94q-37 -44 -57.5 -107t-20.5 -125z M385 1321q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5q0 -69 -48.5 -116.5t-116.5 -47.5q-65 0 -107.5 44.5t-42.5 107.5zM856 1321q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5q0 -69 -48.5 -116.5t-116.5 -47.5q-65 0 -107.5 44.5t-42.5 107.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1222" d="M70 412q0 124 48 243.5t138 208.5q171 166 436 166q220 0 471 -119l-159 -911h-306l13 68l-4 2q-108 -93 -248 -93q-169 0 -279 121t-110 314zM383 446q0 -101 50.5 -159t135.5 -58q93 0 176 74l76 434q-76 35 -157 35q-124 0 -203 -94q-37 -44 -57.5 -107t-20.5 -125z M520 1339q0 111 80 194t197 83q104 0 175.5 -64t71.5 -168q0 -111 -79.5 -193.5t-196.5 -82.5q-105 0 -176.5 63.5t-71.5 167.5zM666 1346q0 -43 28 -75t78 -32q53 0 90 40.5t37 98.5q0 43 -28 75t-78 32q-53 0 -90 -40.5t-37 -98.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1804" d="M70 420q0 119 45.5 237.5t142.5 210.5q173 162 447 162q179 0 364 -55l-4 -25l2 -2q114 82 258 82q82 0 157 -27.5t134.5 -78.5t95 -132.5t35.5 -181.5q0 -83 -25 -188h-675q10 -100 77 -151.5t176 -51.5q72 0 162.5 20.5t163.5 49.5l-49 -258q-62 -24 -148.5 -39 t-162.5 -15q-125 0 -235.5 41t-182.5 121q-159 -162 -361 -162q-182 0 -299.5 120.5t-117.5 322.5zM381 451q0 -99 52.5 -158.5t139.5 -59.5q106 0 181 93q-11 43 -11 102q0 47 11 111l39 219q-54 14 -115 14q-141 0 -217 -92q-38 -45 -59 -106.5t-21 -122.5zM1071 618h393 q-3 77 -50 127t-124 50q-75 0 -132.5 -48.5t-86.5 -128.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1040" d="M63 430q0 123 52.5 246t156.5 211q81 69 189 106t223 37q169 0 324 -76l-50 -282q-145 96 -286 96q-119 0 -197 -72q-48 -44 -73 -105.5t-25 -125.5q0 -100 64.5 -161.5t183.5 -61.5q143 0 290 75l-51 -286q-110 -48 -270 -51l-31 -66q142 -47 142 -172q0 -86 -65 -142.5 t-165 -56.5q-108 0 -199 60l62 121q71 -52 133 -52q33 0 55.5 14t22.5 40q0 69 -162 96l84 168q-84 14 -155.5 47t-129 86t-90.5 132t-33 175z" />
<glyph unicode="&#xe8;" horiz-adv-x="1140" d="M66 434q0 118 45.5 232.5t130.5 199.5q164 164 397 164q122 0 221.5 -47t161 -144.5t61.5 -230.5q0 -100 -24 -186h-686q10 -101 78.5 -152t175.5 -51q81 0 173.5 26.5t166.5 65.5l-52 -270q-148 -64 -329 -64q-105 0 -197.5 28.5t-165 83.5t-115 144t-42.5 201zM399 618 h402q-1 81 -49 128t-127 47q-77 0 -135.5 -47.5t-90.5 -127.5zM481 1489h269l94 -336h-205z" />
<glyph unicode="&#xe9;" horiz-adv-x="1140" d="M66 434q0 118 45.5 232.5t130.5 199.5q164 164 397 164q122 0 221.5 -47t161 -144.5t61.5 -230.5q0 -100 -24 -186h-686q10 -101 78.5 -152t175.5 -51q81 0 173.5 26.5t166.5 65.5l-52 -270q-148 -64 -329 -64q-105 0 -197.5 28.5t-165 83.5t-115 144t-42.5 201zM399 618 h402q-1 81 -49 128t-127 47q-77 0 -135.5 -47.5t-90.5 -127.5zM508 1153l225 336h295l-299 -336h-221z" />
<glyph unicode="&#xea;" horiz-adv-x="1140" d="M66 434q0 118 45.5 232.5t130.5 199.5q164 164 397 164q122 0 221.5 -47t161 -144.5t61.5 -230.5q0 -100 -24 -186h-686q10 -101 78.5 -152t175.5 -51q81 0 173.5 26.5t166.5 65.5l-52 -270q-148 -64 -329 -64q-105 0 -197.5 28.5t-165 83.5t-115 144t-42.5 201z M311 1153l312 336h243l170 -336h-202l-115 188l-189 -188h-219zM399 618h402q-1 81 -49 128t-127 47q-77 0 -135.5 -47.5t-90.5 -127.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="1140" d="M66 434q0 118 45.5 232.5t130.5 199.5q164 164 397 164q122 0 221.5 -47t161 -144.5t61.5 -230.5q0 -100 -24 -186h-686q10 -101 78.5 -152t175.5 -51q81 0 173.5 26.5t166.5 65.5l-52 -270q-148 -64 -329 -64q-105 0 -197.5 28.5t-165 83.5t-115 144t-42.5 201z M313 1321q0 68 49 116t117 48q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -116t-117 -48q-65 0 -107.5 44.5t-42.5 107.5zM399 618h402q-1 81 -49 128t-127 47q-77 0 -135.5 -47.5t-90.5 -127.5zM784 1321q0 68 49 116t117 48q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -116 t-117 -48q-65 0 -107.5 44.5t-42.5 107.5z" />
<glyph unicode="&#xec;" horiz-adv-x="569" d="M41 0l178 1008h311l-178 -1008h-311zM205 1489h268l94 -336h-205z" />
<glyph unicode="&#xed;" horiz-adv-x="569" d="M41 0l178 1008h311l-178 -1008h-311zM231 1153l226 336h295l-299 -336h-222z" />
<glyph unicode="&#xee;" horiz-adv-x="569" d="M35 1153l311 336h244l170 -336h-203l-115 188l-188 -188h-219zM41 0l178 1008h311l-178 -1008h-311z" />
<glyph unicode="&#xef;" horiz-adv-x="569" d="M37 1321q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5q0 -68 -49 -116t-117 -48q-65 0 -107 44.5t-42 107.5zM41 0l178 1008h311l-178 -1008h-311zM508 1321q0 68 49 116t117 48q65 0 107 -44.5t42 -107.5q0 -68 -49 -116t-117 -48q-65 0 -107 44.5t-42 107.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1226" d="M70 432q0 110 43 218t127 186q73 69 167 106t191 37q125 0 178 -53l2 4q-36 96 -108 192l-332 -86l-4 191l190 49q-56 51 -135 106l219 166q123 -87 221 -192l236 61l4 -190l-113 -31q187 -281 187 -590q0 -291 -164 -461q-76 -79 -184 -123.5t-232 -44.5 q-213 0 -353 124t-140 331zM383 453q0 -95 51.5 -153t147.5 -58q103 0 170 73q77 86 77 211q0 93 -52.5 151t-145.5 58q-102 0 -172 -73q-35 -38 -55.5 -93.5t-20.5 -115.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1228" d="M41 0l178 1008h305l-16 -93l4 -2q142 117 309 117q145 0 228.5 -86t83.5 -227q0 -43 -13 -123l-104 -594h-311l100 569q8 46 8 74q0 54 -31 86.5t-94 32.5q-112 0 -217 -84l-119 -678h-311zM401 1169l41 230q32 33 80 54.5t103 21.5q64 0 170 -52q37 -17 53.5 -24.5 t41.5 -14t48 -6.5q91 0 180 101l4 -2l-41 -230q-76 -76 -182 -76q-39 0 -76.5 12.5t-95.5 39.5q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-54 0 -95.5 -27t-84.5 -74z" />
<glyph unicode="&#xf2;" d="M68 444q0 113 43.5 224t124.5 194q165 168 417 168q218 0 360 -127t142 -340q0 -113 -43.5 -224t-124.5 -194q-165 -168 -418 -168q-142 0 -255 55.5t-179.5 162.5t-66.5 249zM379 465q0 -98 54.5 -159.5t152.5 -61.5q112 0 180 78q37 43 57.5 102t20.5 119 q0 98 -54.5 159.5t-152.5 61.5q-112 0 -180 -78q-37 -43 -57.5 -102t-20.5 -119zM532 1489h269l94 -336h-205z" />
<glyph unicode="&#xf3;" d="M68 444q0 113 43.5 224t124.5 194q165 168 417 168q218 0 360 -127t142 -340q0 -113 -43.5 -224t-124.5 -194q-165 -168 -418 -168q-142 0 -255 55.5t-179.5 162.5t-66.5 249zM379 465q0 -98 54.5 -159.5t152.5 -61.5q112 0 180 78q37 43 57.5 102t20.5 119 q0 98 -54.5 159.5t-152.5 61.5q-112 0 -180 -78q-37 -43 -57.5 -102t-20.5 -119zM559 1153l225 336h295l-299 -336h-221z" />
<glyph unicode="&#xf4;" d="M68 444q0 113 43.5 224t124.5 194q165 168 417 168q218 0 360 -127t142 -340q0 -113 -43.5 -224t-124.5 -194q-165 -168 -418 -168q-142 0 -255 55.5t-179.5 162.5t-66.5 249zM362 1153l312 336h244l169 -336h-202l-115 188l-188 -188h-220zM379 465q0 -98 54.5 -159.5 t152.5 -61.5q112 0 180 78q37 43 57.5 102t20.5 119q0 98 -54.5 159.5t-152.5 61.5q-112 0 -180 -78q-37 -43 -57.5 -102t-20.5 -119z" />
<glyph unicode="&#xf5;" d="M68 444q0 113 43.5 224t124.5 194q165 168 417 168q218 0 360 -127t142 -340q0 -113 -43.5 -224t-124.5 -194q-165 -168 -418 -168q-142 0 -255 55.5t-179.5 162.5t-66.5 249zM379 465q0 -98 54.5 -159.5t152.5 -61.5q112 0 180 78q37 43 57.5 102t20.5 119 q0 98 -54.5 159.5t-152.5 61.5q-112 0 -180 -78q-37 -43 -57.5 -102t-20.5 -119zM397 1169l41 230q32 33 80 54.5t103 21.5q64 0 170 -52q37 -17 53.5 -24.5t41.5 -14t48 -6.5q91 0 180 101l4 -2l-41 -230q-76 -76 -182 -76q-39 0 -76.5 12.5t-95.5 39.5q-34 17 -51.5 24.5 t-42 14t-47.5 6.5q-54 0 -96 -27.5t-85 -73.5z" />
<glyph unicode="&#xf6;" d="M68 444q0 113 43.5 224t124.5 194q165 168 417 168q218 0 360 -127t142 -340q0 -113 -43.5 -224t-124.5 -194q-165 -168 -418 -168q-142 0 -255 55.5t-179.5 162.5t-66.5 249zM365 1321q0 69 48.5 116.5t116.5 47.5q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -116 t-117 -48q-65 0 -107 44.5t-42 107.5zM379 465q0 -98 54.5 -159.5t152.5 -61.5q112 0 180 78q37 43 57.5 102t20.5 119q0 98 -54.5 159.5t-152.5 61.5q-112 0 -180 -78q-37 -43 -57.5 -102t-20.5 -119zM836 1321q0 69 48.5 116.5t116.5 47.5q65 0 107.5 -44.5t42.5 -107.5 q0 -68 -49 -116t-117 -48q-65 0 -107 44.5t-42 107.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1208" d="M131 629l39 225h991l-39 -225h-991zM412 311q0 71 49 119.5t119 48.5q65 0 108 -42.5t43 -104.5q0 -71 -49 -119.5t-119 -48.5q-65 0 -108 42.5t-43 104.5zM561 1151q0 71 49 119.5t119 48.5q65 0 108.5 -43t43.5 -105q0 -71 -49 -119t-119 -48q-65 0 -108.5 42.5 t-43.5 104.5z" />
<glyph unicode="&#xf8;" d="M37 45l121 115q-90 118 -90 284q0 113 43.5 224t124.5 194q165 168 417 168q187 0 322 -96l123 117l88 -88l-121 -115q90 -118 90 -285q0 -113 -43.5 -224t-124.5 -194q-165 -168 -418 -168q-184 0 -321 97l-123 -117zM379 465q0 -45 12 -86l367 350q-47 35 -121 35 q-112 0 -180 -78q-37 -43 -57.5 -102t-20.5 -119zM465 279q47 -35 121 -35q112 0 180 78q37 43 57.5 102t20.5 119q0 42 -13 86z" />
<glyph unicode="&#xf9;" d="M94 289q0 59 12 129l105 590h311l-100 -566q-8 -44 -8 -80q0 -56 34.5 -87t98.5 -31q114 0 207 90l118 674h312l-178 -1008h-306l17 94l-4 2q-63 -58 -143.5 -88.5t-161.5 -30.5q-140 0 -227 86.5t-87 225.5zM532 1489h269l94 -336h-205z" />
<glyph unicode="&#xfa;" d="M94 289q0 59 12 129l105 590h311l-100 -566q-8 -44 -8 -80q0 -56 34.5 -87t98.5 -31q114 0 207 90l118 674h312l-178 -1008h-306l17 94l-4 2q-63 -58 -143.5 -88.5t-161.5 -30.5q-140 0 -227 86.5t-87 225.5zM559 1153l225 336h295l-299 -336h-221z" />
<glyph unicode="&#xfb;" d="M94 289q0 59 12 129l105 590h311l-100 -566q-8 -44 -8 -80q0 -56 34.5 -87t98.5 -31q114 0 207 90l118 674h312l-178 -1008h-306l17 94l-4 2q-63 -58 -143.5 -88.5t-161.5 -30.5q-140 0 -227 86.5t-87 225.5zM362 1153l312 336h244l169 -336h-202l-115 188l-188 -188 h-220z" />
<glyph unicode="&#xfc;" d="M94 289q0 59 12 129l105 590h311l-100 -566q-8 -44 -8 -80q0 -56 34.5 -87t98.5 -31q114 0 207 90l118 674h312l-178 -1008h-306l17 94l-4 2q-63 -58 -143.5 -88.5t-161.5 -30.5q-140 0 -227 86.5t-87 225.5zM365 1321q0 69 48.5 116.5t116.5 47.5q65 0 107.5 -44.5 t42.5 -107.5q0 -68 -49 -116t-117 -48q-65 0 -107 44.5t-42 107.5zM836 1321q0 69 48.5 116.5t116.5 47.5q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -116t-117 -48q-65 0 -107 44.5t-42 107.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1112" d="M-33 -451l369 572l-250 887h322l63 -263q44 -179 70 -307h4q108 187 186 312l158 258h315l-911 -1459h-326zM504 1153l225 336h295l-299 -336h-221z" />
<glyph unicode="&#xfe;" horiz-adv-x="1220" d="M-39 -451l358 2016h312l-107 -607l2 -2q97 74 228 74q171 0 283 -124.5t112 -319.5q0 -122 -46.5 -240t-129.5 -201q-168 -168 -434 -168q-89 0 -183 27l-4 -2l-80 -453h-311zM399 262q64 -31 148 -31q134 0 213 93q76 95 76 225q0 102 -53.5 162.5t-145.5 60.5 q-84 0 -158 -51z" />
<glyph unicode="&#xff;" horiz-adv-x="1112" d="M-33 -451l369 572l-250 887h322l63 -263q44 -179 70 -307h4q108 187 186 312l158 258h315l-911 -1459h-326zM309 1321q0 68 49 116t117 48q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -116t-117 -48q-65 0 -107.5 44.5t-42.5 107.5zM780 1321q0 68 49 116t117 48 q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -116t-117 -48q-65 0 -107.5 44.5t-42.5 107.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1861" d="M111 655q0 175 69 339t203 280q245 209 627 209h932l-52 -285h-538l-52 -291h453l-47 -270h-453l-61 -352h559l-51 -285h-807q-167 0 -308 41t-247.5 120t-166.5 205.5t-60 288.5zM438 686q0 -123 58.5 -215.5t154 -139t213.5 -46.5l162 913h-39q-262 0 -405 -149 q-69 -72 -106.5 -169t-37.5 -194z" />
<glyph unicode="&#x153;" horiz-adv-x="1880" d="M68 438q0 110 38.5 218.5t114.5 193.5q77 85 183.5 132.5t230.5 47.5q122 0 221 -45.5t154 -122.5h4q69 79 166 123.5t209 44.5q87 0 164.5 -27.5t138 -79t96 -133.5t35.5 -184q0 -92 -25 -184h-674q10 -102 76.5 -153.5t175.5 -51.5q72 0 163.5 21.5t162.5 50.5 l-49 -258q-62 -24 -149.5 -39t-162.5 -15q-122 0 -226.5 41t-170.5 121h-4q-73 -79 -174.5 -120.5t-216.5 -41.5q-136 0 -245 54.5t-172.5 160.5t-63.5 246zM379 465q0 -97 52.5 -159t146.5 -62q111 0 180 90q67 89 67 209q0 94 -51.5 157.5t-144.5 63.5q-116 0 -185 -90 q-65 -86 -65 -209zM1149 618h391q-3 78 -49.5 127.5t-122.5 49.5t-133 -48t-86 -129z" />
<glyph unicode="&#x178;" horiz-adv-x="1398" d="M154 1483h362l123 -254q67 -140 131 -285h4q113 141 244 301l196 238h377l-694 -811l-119 -672h-325l116 668zM537 1796q0 69 48.5 116.5t116.5 47.5q65 0 107.5 -44.5t42.5 -107.5q0 -68 -49 -115.5t-117 -47.5q-65 0 -107 44t-42 107zM1008 1796q0 68 49 116t117 48 q65 0 107 -44.5t42 -107.5q0 -68 -49 -115.5t-117 -47.5q-65 0 -107 44t-42 107z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1073" d="M287 1153l311 336h244l170 -336h-203l-115 188l-188 -188h-219z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1073" d="M322 1169l40 230q32 33 80 54.5t103 21.5q64 0 170 -52q37 -17 53.5 -24.5t41.5 -14t48 -6.5q91 0 180 101l4 -2l-41 -230q-76 -76 -182 -76q-39 0 -76.5 12.5t-95.5 39.5q-34 17 -51.5 24.5t-42 14t-47.5 6.5q-54 0 -95.5 -27t-84.5 -74z" />
<glyph unicode="&#x2000;" horiz-adv-x="1045" />
<glyph unicode="&#x2001;" horiz-adv-x="2091" />
<glyph unicode="&#x2002;" horiz-adv-x="1045" />
<glyph unicode="&#x2003;" horiz-adv-x="2091" />
<glyph unicode="&#x2004;" horiz-adv-x="697" />
<glyph unicode="&#x2005;" horiz-adv-x="522" />
<glyph unicode="&#x2006;" horiz-adv-x="348" />
<glyph unicode="&#x2007;" horiz-adv-x="348" />
<glyph unicode="&#x2008;" horiz-adv-x="261" />
<glyph unicode="&#x2009;" horiz-adv-x="418" />
<glyph unicode="&#x200a;" horiz-adv-x="116" />
<glyph unicode="&#x2010;" horiz-adv-x="669" d="M76 440l45 254h495l-45 -254h-495z" />
<glyph unicode="&#x2011;" horiz-adv-x="669" d="M76 440l45 254h495l-45 -254h-495z" />
<glyph unicode="&#x2012;" horiz-adv-x="669" d="M76 440l45 254h495l-45 -254h-495z" />
<glyph unicode="&#x2013;" horiz-adv-x="980" d="M86 455l41 227h791l-41 -227h-791z" />
<glyph unicode="&#x2014;" horiz-adv-x="1570" d="M59 473l33 191h1440l-33 -191h-1440z" />
<glyph unicode="&#x2018;" horiz-adv-x="536" d="M131 942l293 578h199l-220 -578h-272z" />
<glyph unicode="&#x2019;" horiz-adv-x="536" d="M168 942l219 578h272l-292 -578h-199z" />
<glyph unicode="&#x201a;" horiz-adv-x="536" d="M-49 -242l219 578h272l-292 -578h-199z" />
<glyph unicode="&#x201c;" horiz-adv-x="925" d="M131 942l293 578h199l-220 -578h-272zM520 942l293 578h199l-219 -578h-273z" />
<glyph unicode="&#x201d;" horiz-adv-x="925" d="M168 942l219 578h272l-292 -578h-199zM557 942l219 578h273l-293 -578h-199z" />
<glyph unicode="&#x201e;" horiz-adv-x="925" d="M-49 -242l219 578h272l-292 -578h-199zM340 -242l219 578h272l-292 -578h-199z" />
<glyph unicode="&#x2022;" horiz-adv-x="796" d="M154 608q0 109 76.5 185.5t185.5 76.5t185.5 -76.5t76.5 -185.5t-76.5 -185.5t-185.5 -76.5t-185.5 76.5t-76.5 185.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1726" d="M61 154q0 82 58 137t139 55q75 0 125.5 -51.5t50.5 -124.5q0 -83 -57 -138t-139 -55q-75 0 -126 52t-51 125zM616 154q0 82 58 137t139 55q75 0 125.5 -51.5t50.5 -124.5q0 -83 -57 -138t-139 -55q-75 0 -126 52t-51 125zM1171 154q0 82 58 137t139 55q75 0 125.5 -51.5 t50.5 -124.5q0 -83 -57 -138t-139 -55q-75 0 -126 52t-51 125z" />
<glyph unicode="&#x202f;" horiz-adv-x="418" />
<glyph unicode="&#x2039;" horiz-adv-x="696" d="M57 524l402 428h260l-416 -444l244 -453h-240z" />
<glyph unicode="&#x203a;" horiz-adv-x="696" d="M-23 55l416 445l-243 452h239l250 -469l-401 -428h-261z" />
<glyph unicode="&#x205f;" horiz-adv-x="522" />
<glyph unicode="&#x20ac;" horiz-adv-x="1228" d="M8 471l37 209h154q3 58 16 129h-147l36 209h177q100 223 287.5 355t428.5 132q164 0 322 -63l-55 -309q-133 77 -267 77q-107 0 -201.5 -50t-160.5 -142h313l-37 -209h-368q-16 -70 -19 -129h365l-37 -209h-291q44 -98 135.5 -148.5t221.5 -50.5q114 0 229 41l-53 -299 q-95 -37 -244 -37q-247 0 -418.5 133.5t-218.5 360.5h-205z" />
<glyph unicode="&#x2122;" horiz-adv-x="1327" d="M170 1339v144h492v-144h-166v-450h-160v450h-166zM741 889v594h148l160 -219l157 219h150v-594h-158v201q0 67 4 141l-4 2q-27 -47 -76 -113l-75 -104l-76 104q-49 66 -76 113l-4 -2q4 -74 4 -141v-201h-154z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x22;" u2="_" k="84" />
<hkern u1="&#x27;" u2="_" k="84" />
<hkern u1="&#x2a;" u2="&#x2026;" k="246" />
<hkern u1="&#x2a;" u2="&#x2e;" k="246" />
<hkern u1="&#x2a;" u2="&#x2c;" k="246" />
<hkern u1="&#x2c;" u2="V" k="193" />
<hkern u1="&#x2c;" u2="&#x39;" k="84" />
<hkern u1="&#x2c;" u2="&#x38;" k="72" />
<hkern u1="&#x2c;" u2="&#x37;" k="18" />
<hkern u1="&#x2c;" u2="&#x2a;" k="246" />
<hkern u1="&#x2d;" u2="x" k="104" />
<hkern u1="&#x2d;" u2="X" k="66" />
<hkern u1="&#x2d;" u2="V" k="117" />
<hkern u1="&#x2d;" u2="&#x32;" k="70" />
<hkern u1="&#x2d;" u2="&#x31;" k="123" />
<hkern u1="&#x2e;" u2="V" k="193" />
<hkern u1="&#x2e;" u2="&#x39;" k="84" />
<hkern u1="&#x2e;" u2="&#x38;" k="72" />
<hkern u1="&#x2e;" u2="&#x37;" k="18" />
<hkern u1="&#x2e;" u2="&#x2a;" k="246" />
<hkern u1="&#x2f;" u2="&#x153;" k="147" />
<hkern u1="&#x2f;" u2="&#xf8;" k="147" />
<hkern u1="&#x2f;" u2="&#xf6;" k="147" />
<hkern u1="&#x2f;" u2="&#xf5;" k="147" />
<hkern u1="&#x2f;" u2="&#xf4;" k="147" />
<hkern u1="&#x2f;" u2="&#xf3;" k="147" />
<hkern u1="&#x2f;" u2="&#xf2;" k="147" />
<hkern u1="&#x2f;" u2="&#xf0;" k="147" />
<hkern u1="&#x2f;" u2="&#xeb;" k="147" />
<hkern u1="&#x2f;" u2="&#xea;" k="147" />
<hkern u1="&#x2f;" u2="&#xe9;" k="147" />
<hkern u1="&#x2f;" u2="&#xe8;" k="147" />
<hkern u1="&#x2f;" u2="&#xe7;" k="147" />
<hkern u1="&#x2f;" u2="&#xe6;" k="147" />
<hkern u1="&#x2f;" u2="&#xe5;" k="147" />
<hkern u1="&#x2f;" u2="&#xe4;" k="147" />
<hkern u1="&#x2f;" u2="&#xe3;" k="147" />
<hkern u1="&#x2f;" u2="&#xe2;" k="147" />
<hkern u1="&#x2f;" u2="&#xe1;" k="147" />
<hkern u1="&#x2f;" u2="&#xe0;" k="147" />
<hkern u1="&#x2f;" u2="&#xc5;" k="172" />
<hkern u1="&#x2f;" u2="&#xc4;" k="172" />
<hkern u1="&#x2f;" u2="&#xc3;" k="172" />
<hkern u1="&#x2f;" u2="&#xc2;" k="172" />
<hkern u1="&#x2f;" u2="&#xc1;" k="172" />
<hkern u1="&#x2f;" u2="&#xc0;" k="172" />
<hkern u1="&#x2f;" u2="q" k="147" />
<hkern u1="&#x2f;" u2="o" k="147" />
<hkern u1="&#x2f;" u2="g" k="147" />
<hkern u1="&#x2f;" u2="e" k="147" />
<hkern u1="&#x2f;" u2="d" k="147" />
<hkern u1="&#x2f;" u2="c" k="147" />
<hkern u1="&#x2f;" u2="a" k="147" />
<hkern u1="&#x2f;" u2="A" k="172" />
<hkern u1="&#x2f;" u2="&#x34;" k="158" />
<hkern u1="&#x30;" u2="_" k="86" />
<hkern u1="&#x36;" u2="&#x37;" k="18" />
<hkern u1="&#x36;" u2="&#x31;" k="37" />
<hkern u1="&#x37;" u2="&#x2026;" k="150" />
<hkern u1="&#x37;" u2="&#x34;" k="121" />
<hkern u1="&#x37;" u2="&#x2f;" k="188" />
<hkern u1="&#x37;" u2="&#x2e;" k="150" />
<hkern u1="&#x37;" u2="&#x2c;" k="150" />
<hkern u1="&#x38;" u2="&#x2026;" k="10" />
<hkern u1="&#x38;" u2="&#x2e;" k="10" />
<hkern u1="&#x38;" u2="&#x2c;" k="10" />
<hkern u1="&#x39;" u2="&#x2026;" k="150" />
<hkern u1="&#x39;" u2="&#x2e;" k="150" />
<hkern u1="&#x39;" u2="&#x2c;" k="150" />
<hkern u1="A" u2="x" k="-51" />
<hkern u1="A" u2="\" k="172" />
<hkern u1="A" u2="V" k="143" />
<hkern u1="B" u2="&#x203a;" k="10" />
<hkern u1="B" u2="&#x201c;" k="25" />
<hkern u1="B" u2="&#x2018;" k="25" />
<hkern u1="B" u2="&#x178;" k="111" />
<hkern u1="B" u2="&#xff;" k="25" />
<hkern u1="B" u2="&#xfd;" k="25" />
<hkern u1="B" u2="&#xdd;" k="111" />
<hkern u1="B" u2="&#xc6;" k="18" />
<hkern u1="B" u2="&#xc5;" k="4" />
<hkern u1="B" u2="&#xc4;" k="4" />
<hkern u1="B" u2="&#xc3;" k="4" />
<hkern u1="B" u2="&#xc2;" k="4" />
<hkern u1="B" u2="&#xc1;" k="4" />
<hkern u1="B" u2="&#xc0;" k="4" />
<hkern u1="B" u2="&#xbb;" k="10" />
<hkern u1="B" u2="z" k="20" />
<hkern u1="B" u2="y" k="25" />
<hkern u1="B" u2="w" k="10" />
<hkern u1="B" u2="v" k="25" />
<hkern u1="B" u2="Y" k="111" />
<hkern u1="B" u2="W" k="72" />
<hkern u1="B" u2="V" k="72" />
<hkern u1="B" u2="A" k="4" />
<hkern u1="D" u2="x" k="20" />
<hkern u1="D" u2="_" k="121" />
<hkern u1="D" u2="X" k="59" />
<hkern u1="D" u2="V" k="94" />
<hkern u1="E" u2="x" k="-14" />
<hkern u1="F" u2="&#x203a;" k="29" />
<hkern u1="F" u2="&#x2039;" k="37" />
<hkern u1="F" u2="&#x2026;" k="207" />
<hkern u1="F" u2="&#x201e;" k="160" />
<hkern u1="F" u2="&#x201a;" k="160" />
<hkern u1="F" u2="&#x153;" k="41" />
<hkern u1="F" u2="&#xff;" k="47" />
<hkern u1="F" u2="&#xfd;" k="47" />
<hkern u1="F" u2="&#xfc;" k="45" />
<hkern u1="F" u2="&#xfb;" k="45" />
<hkern u1="F" u2="&#xfa;" k="45" />
<hkern u1="F" u2="&#xf9;" k="45" />
<hkern u1="F" u2="&#xf8;" k="41" />
<hkern u1="F" u2="&#xf6;" k="41" />
<hkern u1="F" u2="&#xf5;" k="41" />
<hkern u1="F" u2="&#xf4;" k="41" />
<hkern u1="F" u2="&#xf3;" k="41" />
<hkern u1="F" u2="&#xf2;" k="41" />
<hkern u1="F" u2="&#xf1;" k="39" />
<hkern u1="F" u2="&#xf0;" k="41" />
<hkern u1="F" u2="&#xef;" k="-72" />
<hkern u1="F" u2="&#xee;" k="-27" />
<hkern u1="F" u2="&#xeb;" k="41" />
<hkern u1="F" u2="&#xea;" k="41" />
<hkern u1="F" u2="&#xe9;" k="41" />
<hkern u1="F" u2="&#xe8;" k="41" />
<hkern u1="F" u2="&#xe7;" k="41" />
<hkern u1="F" u2="&#xe6;" k="41" />
<hkern u1="F" u2="&#xe5;" k="41" />
<hkern u1="F" u2="&#xe4;" k="41" />
<hkern u1="F" u2="&#xe3;" k="41" />
<hkern u1="F" u2="&#xe2;" k="41" />
<hkern u1="F" u2="&#xe1;" k="41" />
<hkern u1="F" u2="&#xe0;" k="41" />
<hkern u1="F" u2="&#xc5;" k="111" />
<hkern u1="F" u2="&#xc4;" k="111" />
<hkern u1="F" u2="&#xc3;" k="111" />
<hkern u1="F" u2="&#xc2;" k="111" />
<hkern u1="F" u2="&#xc1;" k="111" />
<hkern u1="F" u2="&#xc0;" k="111" />
<hkern u1="F" u2="&#xbb;" k="29" />
<hkern u1="F" u2="&#xab;" k="37" />
<hkern u1="F" u2="z" k="37" />
<hkern u1="F" u2="y" k="47" />
<hkern u1="F" u2="x" k="72" />
<hkern u1="F" u2="w" k="33" />
<hkern u1="F" u2="v" k="47" />
<hkern u1="F" u2="u" k="45" />
<hkern u1="F" u2="t" k="41" />
<hkern u1="F" u2="s" k="45" />
<hkern u1="F" u2="r" k="39" />
<hkern u1="F" u2="q" k="41" />
<hkern u1="F" u2="p" k="39" />
<hkern u1="F" u2="o" k="41" />
<hkern u1="F" u2="n" k="39" />
<hkern u1="F" u2="m" k="39" />
<hkern u1="F" u2="g" k="41" />
<hkern u1="F" u2="e" k="41" />
<hkern u1="F" u2="d" k="41" />
<hkern u1="F" u2="c" k="41" />
<hkern u1="F" u2="a" k="41" />
<hkern u1="F" u2="A" k="111" />
<hkern u1="F" u2="&#x2e;" k="207" />
<hkern u1="F" u2="&#x2c;" k="207" />
<hkern u1="K" u2="&#xf8;" k="41" />
<hkern u1="K" u2="x" k="-72" />
<hkern u1="L" u2="&#x2019;" k="244" />
<hkern u1="L" u2="x" k="-35" />
<hkern u1="L" u2="V" k="199" />
<hkern u1="O" u2="x" k="20" />
<hkern u1="O" u2="_" k="121" />
<hkern u1="O" u2="X" k="59" />
<hkern u1="O" u2="V" k="94" />
<hkern u1="P" u2="&#x203a;" k="4" />
<hkern u1="P" u2="&#x2039;" k="35" />
<hkern u1="P" u2="&#x2026;" k="242" />
<hkern u1="P" u2="&#x201e;" k="178" />
<hkern u1="P" u2="&#x201a;" k="178" />
<hkern u1="P" u2="&#x153;" k="33" />
<hkern u1="P" u2="&#xff;" k="-25" />
<hkern u1="P" u2="&#xfd;" k="-25" />
<hkern u1="P" u2="&#xfc;" k="16" />
<hkern u1="P" u2="&#xfb;" k="16" />
<hkern u1="P" u2="&#xfa;" k="16" />
<hkern u1="P" u2="&#xf9;" k="16" />
<hkern u1="P" u2="&#xf8;" k="33" />
<hkern u1="P" u2="&#xf6;" k="33" />
<hkern u1="P" u2="&#xf5;" k="33" />
<hkern u1="P" u2="&#xf4;" k="33" />
<hkern u1="P" u2="&#xf3;" k="33" />
<hkern u1="P" u2="&#xf2;" k="33" />
<hkern u1="P" u2="&#xf1;" k="14" />
<hkern u1="P" u2="&#xf0;" k="33" />
<hkern u1="P" u2="&#xef;" k="-27" />
<hkern u1="P" u2="&#xee;" k="-59" />
<hkern u1="P" u2="&#xeb;" k="33" />
<hkern u1="P" u2="&#xea;" k="33" />
<hkern u1="P" u2="&#xe9;" k="33" />
<hkern u1="P" u2="&#xe8;" k="33" />
<hkern u1="P" u2="&#xe7;" k="33" />
<hkern u1="P" u2="&#xe6;" k="33" />
<hkern u1="P" u2="&#xe5;" k="33" />
<hkern u1="P" u2="&#xe4;" k="33" />
<hkern u1="P" u2="&#xe3;" k="33" />
<hkern u1="P" u2="&#xe2;" k="33" />
<hkern u1="P" u2="&#xe1;" k="33" />
<hkern u1="P" u2="&#xe0;" k="33" />
<hkern u1="P" u2="&#xc5;" k="78" />
<hkern u1="P" u2="&#xc4;" k="78" />
<hkern u1="P" u2="&#xc3;" k="78" />
<hkern u1="P" u2="&#xc2;" k="78" />
<hkern u1="P" u2="&#xc1;" k="78" />
<hkern u1="P" u2="&#xc0;" k="78" />
<hkern u1="P" u2="&#xbb;" k="4" />
<hkern u1="P" u2="&#xab;" k="35" />
<hkern u1="P" u2="z" k="4" />
<hkern u1="P" u2="y" k="-25" />
<hkern u1="P" u2="x" k="-25" />
<hkern u1="P" u2="w" k="-18" />
<hkern u1="P" u2="v" k="-25" />
<hkern u1="P" u2="u" k="16" />
<hkern u1="P" u2="s" k="16" />
<hkern u1="P" u2="r" k="14" />
<hkern u1="P" u2="q" k="33" />
<hkern u1="P" u2="p" k="14" />
<hkern u1="P" u2="o" k="33" />
<hkern u1="P" u2="n" k="14" />
<hkern u1="P" u2="m" k="14" />
<hkern u1="P" u2="g" k="33" />
<hkern u1="P" u2="e" k="33" />
<hkern u1="P" u2="d" k="33" />
<hkern u1="P" u2="c" k="33" />
<hkern u1="P" u2="a" k="33" />
<hkern u1="P" u2="W" k="27" />
<hkern u1="P" u2="V" k="31" />
<hkern u1="P" u2="J" k="139" />
<hkern u1="P" u2="A" k="78" />
<hkern u1="P" u2="&#x2e;" k="242" />
<hkern u1="P" u2="&#x2c;" k="242" />
<hkern u1="Q" u2="x" k="20" />
<hkern u1="Q" u2="_" k="121" />
<hkern u1="Q" u2="X" k="59" />
<hkern u1="Q" u2="V" k="94" />
<hkern u1="R" u2="V" k="51" />
<hkern u1="S" u2="_" k="4" />
<hkern u1="S" u2="V" k="31" />
<hkern u1="T" u2="&#xff;" k="47" />
<hkern u1="T" u2="&#xfc;" k="98" />
<hkern u1="T" u2="&#xf6;" k="158" />
<hkern u1="T" u2="&#xf1;" k="199" />
<hkern u1="T" u2="&#xee;" k="-66" />
<hkern u1="T" u2="&#xec;" k="-8" />
<hkern u1="T" u2="&#xeb;" k="94" />
<hkern u1="T" u2="&#xea;" k="137" />
<hkern u1="T" u2="&#xe4;" k="176" />
<hkern u1="T" u2="&#xe3;" k="168" />
<hkern u1="T" u2="&#xe2;" k="150" />
<hkern u1="T" u2="x" k="162" />
<hkern u1="T" u2="_" k="139" />
<hkern u1="T" u2="&#x2f;" k="186" />
<hkern u1="U" u2="x" k="27" />
<hkern u1="V" u2="&#x203a;" k="47" />
<hkern u1="V" u2="&#x2039;" k="113" />
<hkern u1="V" u2="&#x2026;" k="215" />
<hkern u1="V" u2="&#x2014;" k="82" />
<hkern u1="V" u2="&#x2013;" k="82" />
<hkern u1="V" u2="&#x178;" k="-41" />
<hkern u1="V" u2="&#x153;" k="117" />
<hkern u1="V" u2="&#x152;" k="25" />
<hkern u1="V" u2="&#xff;" k="61" />
<hkern u1="V" u2="&#xfd;" k="61" />
<hkern u1="V" u2="&#xfc;" k="74" />
<hkern u1="V" u2="&#xfb;" k="74" />
<hkern u1="V" u2="&#xfa;" k="74" />
<hkern u1="V" u2="&#xf9;" k="74" />
<hkern u1="V" u2="&#xf8;" k="117" />
<hkern u1="V" u2="&#xf6;" k="117" />
<hkern u1="V" u2="&#xf5;" k="117" />
<hkern u1="V" u2="&#xf4;" k="117" />
<hkern u1="V" u2="&#xf3;" k="117" />
<hkern u1="V" u2="&#xf2;" k="117" />
<hkern u1="V" u2="&#xf1;" k="117" />
<hkern u1="V" u2="&#xf0;" k="117" />
<hkern u1="V" u2="&#xeb;" k="117" />
<hkern u1="V" u2="&#xea;" k="117" />
<hkern u1="V" u2="&#xe9;" k="117" />
<hkern u1="V" u2="&#xe8;" k="117" />
<hkern u1="V" u2="&#xe7;" k="117" />
<hkern u1="V" u2="&#xe6;" k="117" />
<hkern u1="V" u2="&#xe5;" k="117" />
<hkern u1="V" u2="&#xe4;" k="117" />
<hkern u1="V" u2="&#xe3;" k="117" />
<hkern u1="V" u2="&#xe2;" k="117" />
<hkern u1="V" u2="&#xe1;" k="117" />
<hkern u1="V" u2="&#xe0;" k="117" />
<hkern u1="V" u2="&#xdd;" k="-41" />
<hkern u1="V" u2="&#xd8;" k="25" />
<hkern u1="V" u2="&#xd6;" k="25" />
<hkern u1="V" u2="&#xd5;" k="25" />
<hkern u1="V" u2="&#xd4;" k="25" />
<hkern u1="V" u2="&#xd3;" k="25" />
<hkern u1="V" u2="&#xd2;" k="25" />
<hkern u1="V" u2="&#xc7;" k="25" />
<hkern u1="V" u2="&#xc6;" k="133" />
<hkern u1="V" u2="&#xc5;" k="106" />
<hkern u1="V" u2="&#xc4;" k="106" />
<hkern u1="V" u2="&#xc3;" k="106" />
<hkern u1="V" u2="&#xc2;" k="106" />
<hkern u1="V" u2="&#xc1;" k="106" />
<hkern u1="V" u2="&#xc0;" k="106" />
<hkern u1="V" u2="&#xbb;" k="47" />
<hkern u1="V" u2="&#xad;" k="82" />
<hkern u1="V" u2="&#xab;" k="113" />
<hkern u1="V" u2="y" k="61" />
<hkern u1="V" u2="x" k="68" />
<hkern u1="V" u2="w" k="57" />
<hkern u1="V" u2="v" k="61" />
<hkern u1="V" u2="u" k="74" />
<hkern u1="V" u2="t" k="61" />
<hkern u1="V" u2="s" k="133" />
<hkern u1="V" u2="r" k="117" />
<hkern u1="V" u2="q" k="117" />
<hkern u1="V" u2="p" k="117" />
<hkern u1="V" u2="o" k="117" />
<hkern u1="V" u2="n" k="117" />
<hkern u1="V" u2="m" k="117" />
<hkern u1="V" u2="g" k="117" />
<hkern u1="V" u2="e" k="117" />
<hkern u1="V" u2="d" k="117" />
<hkern u1="V" u2="c" k="117" />
<hkern u1="V" u2="a" k="117" />
<hkern u1="V" u2="_" k="154" />
<hkern u1="V" u2="Y" k="-41" />
<hkern u1="V" u2="V" k="-20" />
<hkern u1="V" u2="S" k="10" />
<hkern u1="V" u2="Q" k="25" />
<hkern u1="V" u2="O" k="25" />
<hkern u1="V" u2="J" k="113" />
<hkern u1="V" u2="G" k="25" />
<hkern u1="V" u2="C" k="25" />
<hkern u1="V" u2="A" k="106" />
<hkern u1="V" u2="&#x2f;" k="154" />
<hkern u1="V" u2="&#x2e;" k="215" />
<hkern u1="V" u2="&#x2d;" k="82" />
<hkern u1="V" u2="&#x2c;" k="215" />
<hkern u1="W" u2="&#xe4;" k="82" />
<hkern u1="W" u2="x" k="72" />
<hkern u1="W" u2="_" k="133" />
<hkern u1="W" u2="&#x2f;" k="102" />
<hkern u1="X" u2="&#x203a;" k="-31" />
<hkern u1="X" u2="&#x2039;" k="61" />
<hkern u1="X" u2="&#x201d;" k="14" />
<hkern u1="X" u2="&#x201c;" k="37" />
<hkern u1="X" u2="&#x2019;" k="14" />
<hkern u1="X" u2="&#x2018;" k="37" />
<hkern u1="X" u2="&#x2014;" k="88" />
<hkern u1="X" u2="&#x2013;" k="88" />
<hkern u1="X" u2="&#x153;" k="47" />
<hkern u1="X" u2="&#x152;" k="70" />
<hkern u1="X" u2="&#xff;" k="129" />
<hkern u1="X" u2="&#xfd;" k="129" />
<hkern u1="X" u2="&#xf8;" k="47" />
<hkern u1="X" u2="&#xf6;" k="47" />
<hkern u1="X" u2="&#xf5;" k="47" />
<hkern u1="X" u2="&#xf4;" k="47" />
<hkern u1="X" u2="&#xf3;" k="47" />
<hkern u1="X" u2="&#xf2;" k="47" />
<hkern u1="X" u2="&#xf0;" k="47" />
<hkern u1="X" u2="&#xeb;" k="47" />
<hkern u1="X" u2="&#xea;" k="47" />
<hkern u1="X" u2="&#xe9;" k="47" />
<hkern u1="X" u2="&#xe8;" k="47" />
<hkern u1="X" u2="&#xe7;" k="47" />
<hkern u1="X" u2="&#xe6;" k="47" />
<hkern u1="X" u2="&#xe5;" k="47" />
<hkern u1="X" u2="&#xe4;" k="47" />
<hkern u1="X" u2="&#xe3;" k="47" />
<hkern u1="X" u2="&#xe2;" k="47" />
<hkern u1="X" u2="&#xe1;" k="47" />
<hkern u1="X" u2="&#xe0;" k="47" />
<hkern u1="X" u2="&#xd8;" k="70" />
<hkern u1="X" u2="&#xd6;" k="70" />
<hkern u1="X" u2="&#xd5;" k="70" />
<hkern u1="X" u2="&#xd4;" k="70" />
<hkern u1="X" u2="&#xd3;" k="70" />
<hkern u1="X" u2="&#xd2;" k="70" />
<hkern u1="X" u2="&#xc7;" k="70" />
<hkern u1="X" u2="&#xbb;" k="-31" />
<hkern u1="X" u2="&#xad;" k="88" />
<hkern u1="X" u2="&#xab;" k="61" />
<hkern u1="X" u2="y" k="129" />
<hkern u1="X" u2="w" k="123" />
<hkern u1="X" u2="v" k="129" />
<hkern u1="X" u2="q" k="47" />
<hkern u1="X" u2="o" k="47" />
<hkern u1="X" u2="g" k="47" />
<hkern u1="X" u2="e" k="47" />
<hkern u1="X" u2="d" k="47" />
<hkern u1="X" u2="c" k="47" />
<hkern u1="X" u2="a" k="47" />
<hkern u1="X" u2="Q" k="70" />
<hkern u1="X" u2="O" k="70" />
<hkern u1="X" u2="G" k="70" />
<hkern u1="X" u2="C" k="70" />
<hkern u1="X" u2="&#x2d;" k="88" />
<hkern u1="Y" u2="&#xff;" k="68" />
<hkern u1="Y" u2="&#xf6;" k="129" />
<hkern u1="Y" u2="&#xf5;" k="190" />
<hkern u1="Y" u2="&#xf4;" k="195" />
<hkern u1="Y" u2="&#xee;" k="-31" />
<hkern u1="Y" u2="&#xec;" k="-25" />
<hkern u1="Y" u2="&#xeb;" k="63" />
<hkern u1="Y" u2="&#xea;" k="174" />
<hkern u1="Y" u2="&#xe8;" k="158" />
<hkern u1="Y" u2="&#xe4;" k="129" />
<hkern u1="Y" u2="&#xe3;" k="168" />
<hkern u1="Y" u2="&#xe2;" k="225" />
<hkern u1="Y" u2="&#xe0;" k="209" />
<hkern u1="Y" u2="x" k="102" />
<hkern u1="Y" u2="_" k="190" />
<hkern u1="Y" u2="V" k="-41" />
<hkern u1="Y" u2="&#x34;" k="74" />
<hkern u1="Y" u2="&#x2f;" k="236" />
<hkern u1="Z" u2="x" k="-25" />
<hkern u1="\" u2="&#x178;" k="236" />
<hkern u1="\" u2="&#xdd;" k="236" />
<hkern u1="\" u2="Y" k="236" />
<hkern u1="\" u2="W" k="137" />
<hkern u1="\" u2="V" k="174" />
<hkern u1="\" u2="T" k="186" />
<hkern u1="\" u2="&#x37;" k="16" />
<hkern u1="_" u2="&#x178;" k="190" />
<hkern u1="_" u2="&#x153;" k="147" />
<hkern u1="_" u2="&#x152;" k="121" />
<hkern u1="_" u2="&#xff;" k="94" />
<hkern u1="_" u2="&#xfd;" k="94" />
<hkern u1="_" u2="&#xf8;" k="147" />
<hkern u1="_" u2="&#xf6;" k="147" />
<hkern u1="_" u2="&#xf5;" k="147" />
<hkern u1="_" u2="&#xf4;" k="147" />
<hkern u1="_" u2="&#xf3;" k="147" />
<hkern u1="_" u2="&#xf2;" k="147" />
<hkern u1="_" u2="&#xf0;" k="147" />
<hkern u1="_" u2="&#xeb;" k="147" />
<hkern u1="_" u2="&#xea;" k="147" />
<hkern u1="_" u2="&#xe9;" k="147" />
<hkern u1="_" u2="&#xe8;" k="147" />
<hkern u1="_" u2="&#xe7;" k="147" />
<hkern u1="_" u2="&#xe6;" k="147" />
<hkern u1="_" u2="&#xe5;" k="147" />
<hkern u1="_" u2="&#xe4;" k="147" />
<hkern u1="_" u2="&#xe3;" k="147" />
<hkern u1="_" u2="&#xe2;" k="147" />
<hkern u1="_" u2="&#xe1;" k="147" />
<hkern u1="_" u2="&#xe0;" k="147" />
<hkern u1="_" u2="&#xdd;" k="190" />
<hkern u1="_" u2="&#xd8;" k="121" />
<hkern u1="_" u2="&#xd6;" k="121" />
<hkern u1="_" u2="&#xd5;" k="121" />
<hkern u1="_" u2="&#xd4;" k="121" />
<hkern u1="_" u2="&#xd3;" k="121" />
<hkern u1="_" u2="&#xd2;" k="121" />
<hkern u1="_" u2="&#xc7;" k="121" />
<hkern u1="_" u2="y" k="94" />
<hkern u1="_" u2="w" k="109" />
<hkern u1="_" u2="v" k="94" />
<hkern u1="_" u2="q" k="147" />
<hkern u1="_" u2="o" k="147" />
<hkern u1="_" u2="g" k="147" />
<hkern u1="_" u2="e" k="147" />
<hkern u1="_" u2="d" k="147" />
<hkern u1="_" u2="c" k="147" />
<hkern u1="_" u2="a" k="147" />
<hkern u1="_" u2="Y" k="190" />
<hkern u1="_" u2="W" k="92" />
<hkern u1="_" u2="V" k="170" />
<hkern u1="_" u2="T" k="139" />
<hkern u1="_" u2="Q" k="121" />
<hkern u1="_" u2="O" k="121" />
<hkern u1="_" u2="G" k="121" />
<hkern u1="_" u2="C" k="121" />
<hkern u1="_" u2="&#x30;" k="86" />
<hkern u1="b" u2="x" k="39" />
<hkern u1="b" u2="_" k="66" />
<hkern u1="b" u2="X" k="47" />
<hkern u1="b" u2="V" k="152" />
<hkern u1="c" u2="V" k="68" />
<hkern u1="e" u2="x" k="10" />
<hkern u1="e" u2="V" k="121" />
<hkern u1="f" u2="_" k="35" />
<hkern u1="f" u2="&#x3f;" k="-6" />
<hkern u1="f" u2="&#x2a;" k="-51" />
<hkern u1="f" u2="&#x21;" k="-31" />
<hkern u1="g" u2="&#x201c;" k="29" />
<hkern u1="g" u2="&#x2018;" k="29" />
<hkern u1="g" u2="j" k="-14" />
<hkern u1="h" u2="V" k="72" />
<hkern u1="j" u2="j" k="-29" />
<hkern u1="k" u2="&#xf8;" k="57" />
<hkern u1="m" u2="V" k="72" />
<hkern u1="n" u2="&#x201c;" k="29" />
<hkern u1="n" u2="&#x2018;" k="29" />
<hkern u1="n" u2="V" k="72" />
<hkern u1="o" u2="x" k="39" />
<hkern u1="o" u2="_" k="66" />
<hkern u1="o" u2="X" k="47" />
<hkern u1="o" u2="V" k="152" />
<hkern u1="p" u2="x" k="39" />
<hkern u1="p" u2="_" k="66" />
<hkern u1="p" u2="X" k="47" />
<hkern u1="p" u2="V" k="152" />
<hkern u1="r" u2="x" k="25" />
<hkern u1="s" u2="x" k="20" />
<hkern u1="s" u2="V" k="96" />
<hkern u1="u" u2="V" k="78" />
<hkern u1="v" u2="x" k="-6" />
<hkern u1="v" u2="_" k="129" />
<hkern u1="v" u2="X" k="113" />
<hkern u1="v" u2="V" k="41" />
<hkern u1="w" u2="x" k="-10" />
<hkern u1="w" u2="_" k="86" />
<hkern u1="w" u2="X" k="109" />
<hkern u1="w" u2="V" k="61" />
<hkern u1="x" u2="&#x203a;" k="4" />
<hkern u1="x" u2="&#x2039;" k="78" />
<hkern u1="x" u2="&#x201d;" k="-12" />
<hkern u1="x" u2="&#x201c;" k="-43" />
<hkern u1="x" u2="&#x2019;" k="-12" />
<hkern u1="x" u2="&#x2018;" k="-43" />
<hkern u1="x" u2="&#x2014;" k="104" />
<hkern u1="x" u2="&#x2013;" k="104" />
<hkern u1="x" u2="&#x153;" k="39" />
<hkern u1="x" u2="&#xff;" k="-6" />
<hkern u1="x" u2="&#xfd;" k="-6" />
<hkern u1="x" u2="&#xf8;" k="39" />
<hkern u1="x" u2="&#xf6;" k="39" />
<hkern u1="x" u2="&#xf5;" k="39" />
<hkern u1="x" u2="&#xf4;" k="39" />
<hkern u1="x" u2="&#xf3;" k="39" />
<hkern u1="x" u2="&#xf2;" k="39" />
<hkern u1="x" u2="&#xf0;" k="39" />
<hkern u1="x" u2="&#xeb;" k="39" />
<hkern u1="x" u2="&#xea;" k="39" />
<hkern u1="x" u2="&#xe9;" k="39" />
<hkern u1="x" u2="&#xe8;" k="39" />
<hkern u1="x" u2="&#xe7;" k="39" />
<hkern u1="x" u2="&#xe6;" k="39" />
<hkern u1="x" u2="&#xe5;" k="39" />
<hkern u1="x" u2="&#xe4;" k="39" />
<hkern u1="x" u2="&#xe3;" k="39" />
<hkern u1="x" u2="&#xe2;" k="39" />
<hkern u1="x" u2="&#xe1;" k="39" />
<hkern u1="x" u2="&#xe0;" k="39" />
<hkern u1="x" u2="&#xbb;" k="4" />
<hkern u1="x" u2="&#xad;" k="104" />
<hkern u1="x" u2="&#xab;" k="78" />
<hkern u1="x" u2="y" k="-6" />
<hkern u1="x" u2="w" k="-10" />
<hkern u1="x" u2="v" k="-6" />
<hkern u1="x" u2="q" k="39" />
<hkern u1="x" u2="o" k="39" />
<hkern u1="x" u2="g" k="39" />
<hkern u1="x" u2="e" k="39" />
<hkern u1="x" u2="d" k="39" />
<hkern u1="x" u2="c" k="39" />
<hkern u1="x" u2="a" k="39" />
<hkern u1="x" u2="W" k="78" />
<hkern u1="x" u2="V" k="66" />
<hkern u1="x" u2="T" k="102" />
<hkern u1="x" u2="&#x32;" k="-25" />
<hkern u1="x" u2="&#x2d;" k="104" />
<hkern u1="y" u2="x" k="-6" />
<hkern u1="y" u2="_" k="129" />
<hkern u1="y" u2="X" k="113" />
<hkern u1="y" u2="V" k="41" />
<hkern u1="&#xab;" u2="x" k="4" />
<hkern u1="&#xab;" u2="V" k="63" />
<hkern u1="&#xad;" u2="x" k="104" />
<hkern u1="&#xad;" u2="X" k="66" />
<hkern u1="&#xad;" u2="V" k="117" />
<hkern u1="&#xad;" u2="&#x32;" k="70" />
<hkern u1="&#xad;" u2="&#x31;" k="123" />
<hkern u1="&#xbb;" u2="x" k="78" />
<hkern u1="&#xbb;" u2="X" k="92" />
<hkern u1="&#xbb;" u2="V" k="125" />
<hkern u1="&#xbb;" u2="J" k="18" />
<hkern u1="&#xc0;" u2="x" k="-51" />
<hkern u1="&#xc0;" u2="\" k="172" />
<hkern u1="&#xc0;" u2="V" k="143" />
<hkern u1="&#xc1;" u2="x" k="-51" />
<hkern u1="&#xc1;" u2="\" k="172" />
<hkern u1="&#xc1;" u2="V" k="143" />
<hkern u1="&#xc2;" u2="x" k="-51" />
<hkern u1="&#xc2;" u2="\" k="172" />
<hkern u1="&#xc2;" u2="V" k="143" />
<hkern u1="&#xc3;" u2="x" k="-51" />
<hkern u1="&#xc3;" u2="\" k="172" />
<hkern u1="&#xc3;" u2="V" k="143" />
<hkern u1="&#xc4;" u2="x" k="-51" />
<hkern u1="&#xc4;" u2="\" k="172" />
<hkern u1="&#xc4;" u2="V" k="143" />
<hkern u1="&#xc5;" u2="x" k="-51" />
<hkern u1="&#xc5;" u2="\" k="172" />
<hkern u1="&#xc5;" u2="V" k="143" />
<hkern u1="&#xc6;" u2="x" k="-14" />
<hkern u1="&#xc8;" u2="x" k="-14" />
<hkern u1="&#xc9;" u2="x" k="-14" />
<hkern u1="&#xca;" u2="x" k="-14" />
<hkern u1="&#xcb;" u2="x" k="-14" />
<hkern u1="&#xd2;" u2="x" k="20" />
<hkern u1="&#xd2;" u2="_" k="121" />
<hkern u1="&#xd2;" u2="X" k="59" />
<hkern u1="&#xd2;" u2="V" k="94" />
<hkern u1="&#xd3;" u2="x" k="20" />
<hkern u1="&#xd3;" u2="_" k="121" />
<hkern u1="&#xd3;" u2="X" k="59" />
<hkern u1="&#xd3;" u2="V" k="94" />
<hkern u1="&#xd4;" u2="x" k="20" />
<hkern u1="&#xd4;" u2="_" k="121" />
<hkern u1="&#xd4;" u2="X" k="59" />
<hkern u1="&#xd4;" u2="V" k="94" />
<hkern u1="&#xd5;" u2="x" k="20" />
<hkern u1="&#xd5;" u2="_" k="121" />
<hkern u1="&#xd5;" u2="X" k="59" />
<hkern u1="&#xd5;" u2="V" k="94" />
<hkern u1="&#xd6;" u2="x" k="20" />
<hkern u1="&#xd6;" u2="_" k="121" />
<hkern u1="&#xd6;" u2="X" k="59" />
<hkern u1="&#xd6;" u2="V" k="94" />
<hkern u1="&#xd8;" u2="x" k="20" />
<hkern u1="&#xd8;" u2="_" k="121" />
<hkern u1="&#xd8;" u2="X" k="59" />
<hkern u1="&#xd8;" u2="V" k="94" />
<hkern u1="&#xd9;" u2="x" k="27" />
<hkern u1="&#xda;" u2="x" k="27" />
<hkern u1="&#xdb;" u2="x" k="27" />
<hkern u1="&#xdc;" u2="x" k="27" />
<hkern u1="&#xdd;" u2="&#xff;" k="68" />
<hkern u1="&#xdd;" u2="&#xf6;" k="129" />
<hkern u1="&#xdd;" u2="&#xf5;" k="190" />
<hkern u1="&#xdd;" u2="&#xf4;" k="195" />
<hkern u1="&#xdd;" u2="&#xee;" k="-31" />
<hkern u1="&#xdd;" u2="&#xec;" k="-25" />
<hkern u1="&#xdd;" u2="&#xeb;" k="63" />
<hkern u1="&#xdd;" u2="&#xea;" k="174" />
<hkern u1="&#xdd;" u2="&#xe8;" k="158" />
<hkern u1="&#xdd;" u2="&#xe4;" k="129" />
<hkern u1="&#xdd;" u2="&#xe3;" k="168" />
<hkern u1="&#xdd;" u2="&#xe2;" k="225" />
<hkern u1="&#xdd;" u2="&#xe0;" k="209" />
<hkern u1="&#xdd;" u2="x" k="102" />
<hkern u1="&#xdd;" u2="_" k="190" />
<hkern u1="&#xdd;" u2="V" k="-41" />
<hkern u1="&#xdd;" u2="&#x34;" k="74" />
<hkern u1="&#xdd;" u2="&#x2f;" k="236" />
<hkern u1="&#xde;" u2="&#x178;" k="115" />
<hkern u1="&#xde;" u2="&#xdd;" k="115" />
<hkern u1="&#xde;" u2="&#xc5;" k="47" />
<hkern u1="&#xde;" u2="&#xc4;" k="47" />
<hkern u1="&#xde;" u2="&#xc3;" k="47" />
<hkern u1="&#xde;" u2="&#xc2;" k="47" />
<hkern u1="&#xde;" u2="&#xc1;" k="47" />
<hkern u1="&#xde;" u2="&#xc0;" k="47" />
<hkern u1="&#xde;" u2="_" k="59" />
<hkern u1="&#xde;" u2="Y" k="115" />
<hkern u1="&#xde;" u2="W" k="31" />
<hkern u1="&#xde;" u2="V" k="25" />
<hkern u1="&#xde;" u2="T" k="51" />
<hkern u1="&#xde;" u2="A" k="47" />
<hkern u1="&#xdf;" u2="&#xff;" k="84" />
<hkern u1="&#xdf;" u2="&#xfd;" k="84" />
<hkern u1="&#xdf;" u2="y" k="84" />
<hkern u1="&#xdf;" u2="w" k="70" />
<hkern u1="&#xdf;" u2="v" k="84" />
<hkern u1="&#xe6;" u2="x" k="10" />
<hkern u1="&#xe6;" u2="V" k="121" />
<hkern u1="&#xe7;" u2="V" k="68" />
<hkern u1="&#xe8;" u2="x" k="10" />
<hkern u1="&#xe8;" u2="V" k="121" />
<hkern u1="&#xe9;" u2="x" k="10" />
<hkern u1="&#xe9;" u2="V" k="121" />
<hkern u1="&#xea;" u2="x" k="10" />
<hkern u1="&#xea;" u2="V" k="121" />
<hkern u1="&#xeb;" u2="x" k="10" />
<hkern u1="&#xeb;" u2="V" k="121" />
<hkern u1="&#xed;" u2="T" k="-39" />
<hkern u1="&#xef;" u2="V" k="-90" />
<hkern u1="&#xf2;" u2="x" k="39" />
<hkern u1="&#xf2;" u2="_" k="66" />
<hkern u1="&#xf2;" u2="X" k="47" />
<hkern u1="&#xf2;" u2="V" k="152" />
<hkern u1="&#xf3;" u2="x" k="39" />
<hkern u1="&#xf3;" u2="_" k="66" />
<hkern u1="&#xf3;" u2="X" k="47" />
<hkern u1="&#xf3;" u2="V" k="152" />
<hkern u1="&#xf4;" u2="x" k="39" />
<hkern u1="&#xf4;" u2="_" k="66" />
<hkern u1="&#xf4;" u2="X" k="47" />
<hkern u1="&#xf4;" u2="V" k="152" />
<hkern u1="&#xf5;" u2="x" k="39" />
<hkern u1="&#xf5;" u2="_" k="66" />
<hkern u1="&#xf5;" u2="X" k="47" />
<hkern u1="&#xf5;" u2="V" k="152" />
<hkern u1="&#xf6;" u2="x" k="39" />
<hkern u1="&#xf6;" u2="_" k="66" />
<hkern u1="&#xf6;" u2="X" k="47" />
<hkern u1="&#xf6;" u2="V" k="152" />
<hkern u1="&#xf8;" u2="x" k="39" />
<hkern u1="&#xf8;" u2="_" k="66" />
<hkern u1="&#xf8;" u2="X" k="47" />
<hkern u1="&#xf8;" u2="V" k="152" />
<hkern u1="&#xf9;" u2="V" k="78" />
<hkern u1="&#xfa;" u2="V" k="78" />
<hkern u1="&#xfb;" u2="V" k="78" />
<hkern u1="&#xfc;" u2="V" k="78" />
<hkern u1="&#xfd;" u2="x" k="-6" />
<hkern u1="&#xfd;" u2="_" k="129" />
<hkern u1="&#xfd;" u2="X" k="113" />
<hkern u1="&#xfd;" u2="V" k="41" />
<hkern u1="&#xfe;" u2="x" k="39" />
<hkern u1="&#xfe;" u2="_" k="66" />
<hkern u1="&#xfe;" u2="X" k="47" />
<hkern u1="&#xfe;" u2="V" k="152" />
<hkern u1="&#xff;" u2="x" k="-6" />
<hkern u1="&#xff;" u2="_" k="129" />
<hkern u1="&#xff;" u2="X" k="113" />
<hkern u1="&#xff;" u2="V" k="41" />
<hkern u1="&#x152;" u2="x" k="-14" />
<hkern u1="&#x153;" u2="x" k="10" />
<hkern u1="&#x153;" u2="V" k="121" />
<hkern u1="&#x178;" u2="&#xff;" k="68" />
<hkern u1="&#x178;" u2="&#xf6;" k="129" />
<hkern u1="&#x178;" u2="&#xf5;" k="190" />
<hkern u1="&#x178;" u2="&#xf4;" k="195" />
<hkern u1="&#x178;" u2="&#xee;" k="-31" />
<hkern u1="&#x178;" u2="&#xec;" k="-25" />
<hkern u1="&#x178;" u2="&#xeb;" k="63" />
<hkern u1="&#x178;" u2="&#xea;" k="174" />
<hkern u1="&#x178;" u2="&#xe8;" k="158" />
<hkern u1="&#x178;" u2="&#xe4;" k="129" />
<hkern u1="&#x178;" u2="&#xe3;" k="168" />
<hkern u1="&#x178;" u2="&#xe2;" k="225" />
<hkern u1="&#x178;" u2="&#xe0;" k="209" />
<hkern u1="&#x178;" u2="x" k="102" />
<hkern u1="&#x178;" u2="_" k="190" />
<hkern u1="&#x178;" u2="V" k="-41" />
<hkern u1="&#x178;" u2="&#x34;" k="74" />
<hkern u1="&#x178;" u2="&#x2f;" k="236" />
<hkern u1="&#x2013;" u2="x" k="104" />
<hkern u1="&#x2013;" u2="X" k="66" />
<hkern u1="&#x2013;" u2="V" k="117" />
<hkern u1="&#x2013;" u2="&#x32;" k="70" />
<hkern u1="&#x2013;" u2="&#x31;" k="84" />
<hkern u1="&#x2014;" u2="x" k="104" />
<hkern u1="&#x2014;" u2="X" k="66" />
<hkern u1="&#x2014;" u2="V" k="117" />
<hkern u1="&#x2014;" u2="&#x32;" k="70" />
<hkern u1="&#x2014;" u2="&#x31;" k="123" />
<hkern u1="&#x2018;" u2="x" k="25" />
<hkern u1="&#x2018;" u2="j" k="23" />
<hkern u1="&#x2018;" u2="V" k="-16" />
<hkern u1="&#x2019;" u2="x" k="72" />
<hkern u1="&#x2019;" u2="j" k="23" />
<hkern u1="&#x2019;" u2="a" k="59" />
<hkern u1="&#x2019;" u2="V" k="-29" />
<hkern u1="&#x201a;" u2="V" k="92" />
<hkern u1="&#x201c;" u2="x" k="25" />
<hkern u1="&#x201c;" u2="j" k="23" />
<hkern u1="&#x201c;" u2="V" k="-16" />
<hkern u1="&#x201d;" u2="x" k="72" />
<hkern u1="&#x201d;" u2="j" k="23" />
<hkern u1="&#x201d;" u2="a" k="59" />
<hkern u1="&#x201d;" u2="V" k="-29" />
<hkern u1="&#x201e;" u2="V" k="92" />
<hkern u1="&#x2026;" u2="V" k="193" />
<hkern u1="&#x2026;" u2="&#x39;" k="84" />
<hkern u1="&#x2026;" u2="&#x38;" k="72" />
<hkern u1="&#x2026;" u2="&#x37;" k="18" />
<hkern u1="&#x2026;" u2="&#x2a;" k="246" />
<hkern u1="&#x2039;" u2="x" k="4" />
<hkern u1="&#x2039;" u2="V" k="63" />
<hkern u1="&#x203a;" u2="x" k="78" />
<hkern u1="&#x203a;" u2="X" k="92" />
<hkern u1="&#x203a;" u2="V" k="125" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="43" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="39" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,y,yacute,ydieresis" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="41" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="G" 	g2="T" 	k="45" />
<hkern g1="G" 	g2="w" 	k="20" />
<hkern g1="G" 	g2="v,y,yacute,ydieresis" 	k="25" />
<hkern g1="G" 	g2="z" 	k="16" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="4" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="100" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="K" 	g2="w" 	k="113" />
<hkern g1="K" 	g2="v,y,yacute,ydieresis" 	k="115" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="86" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="86" />
<hkern g1="K" 	g2="t" 	k="43" />
<hkern g1="K" 	g2="s" 	k="25" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="K" 	g2="z" 	k="-25" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-18" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="291" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="289" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="170" />
<hkern g1="L" 	g2="T" 	k="199" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="8" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="154" />
<hkern g1="L" 	g2="w" 	k="129" />
<hkern g1="L" 	g2="v,y,yacute,ydieresis" 	k="174" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="L" 	g2="t" 	k="57" />
<hkern g1="L" 	g2="W" 	k="135" />
<hkern g1="L" 	g2="z" 	k="-14" />
<hkern g1="L" 	g2="J" 	k="-27" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="154" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="66" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="8" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="72" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="111" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="43" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="R" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="R" 	g2="W" 	k="51" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-45" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="S" 	g2="T" 	k="25" />
<hkern g1="S" 	g2="w" 	k="25" />
<hkern g1="S" 	g2="v,y,yacute,ydieresis" 	k="39" />
<hkern g1="S" 	g2="t" 	k="10" />
<hkern g1="S" 	g2="W" 	k="39" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="2" />
<hkern g1="S" 	g2="z" 	k="20" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="254" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="188" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-37" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="186" />
<hkern g1="T" 	g2="w" 	k="217" />
<hkern g1="T" 	g2="v,y,yacute,ydieresis" 	k="219" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="195" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="199" />
<hkern g1="T" 	g2="s" 	k="174" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="188" />
<hkern g1="T" 	g2="z" 	k="168" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="180" />
<hkern g1="T" 	g2="J" 	k="150" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="205" />
<hkern g1="T" 	g2="AE" 	k="195" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="217" />
<hkern g1="T" 	g2="idieresis" 	k="-113" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="31" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="113" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="66" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="57" />
<hkern g1="W" 	g2="w" 	k="51" />
<hkern g1="W" 	g2="v,y,yacute,ydieresis" 	k="51" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="51" />
<hkern g1="W" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="92" />
<hkern g1="W" 	g2="t" 	k="47" />
<hkern g1="W" 	g2="s" 	k="96" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="123" />
<hkern g1="W" 	g2="J" 	k="121" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="119" />
<hkern g1="W" 	g2="AE" 	k="137" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="199" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="176" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="190" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="127" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute,ydieresis" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="190" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="231" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="188" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="145" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="162" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="147" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="213" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="162" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="idieresis" 	k="-158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="4" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="4" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="Z" 	g2="w" 	k="86" />
<hkern g1="Z" 	g2="v,y,yacute,ydieresis" 	k="102" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="59" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="Z" 	g2="idieresis" 	k="-119" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="43" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="10" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v,y,yacute,ydieresis" 	k="16" />
<hkern g1="comma,period,ellipsis" 	g2="quoteleft,quotedblleft" 	k="195" />
<hkern g1="comma,period,ellipsis" 	g2="quoteright,quotedblright" 	k="205" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="236" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="205" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="quotedbl,quotesingle" 	k="106" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="123" />
<hkern g1="comma,period,ellipsis" 	g2="v,y,yacute,ydieresis" 	k="150" />
<hkern g1="comma,period,ellipsis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="154" />
<hkern g1="comma,period,ellipsis" 	g2="comma,period,ellipsis" 	k="-14" />
<hkern g1="comma,period,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="8" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="45" />
<hkern g1="comma,period,ellipsis" 	g2="parenright,bracketright,braceright" 	k="57" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="104" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="88" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="219" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="195" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="45" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v,y,yacute,ydieresis" 	k="53" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="82" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotesinglbase,quotedblbase" 	k="4" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-14" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-23" />
<hkern g1="f" 	g2="w" 	k="-29" />
<hkern g1="f" 	g2="v,y,yacute,ydieresis" 	k="-20" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="53" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="51" />
<hkern g1="f" 	g2="parenright,bracketright,braceright" 	k="-84" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="176" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="188" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="w" 	k="10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="39" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="s" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="z" 	k="14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Z" 	k="10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="S" 	k="-4" />
<hkern g1="guillemotright,guilsinglright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="199" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="254" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="29" />
<hkern g1="guillemotright,guilsinglright" 	g2="v,y,yacute,ydieresis" 	k="43" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="92" />
<hkern g1="guillemotright,guilsinglright" 	g2="s" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="27" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="190" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="195" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="w" 	k="23" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="v,y,yacute,ydieresis" 	k="29" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="66" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="29" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="82" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="18" />
<hkern g1="idieresis" 	g2="Y,Yacute,Ydieresis" 	k="-98" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-35" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="4" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="k" 	g2="w" 	k="-20" />
<hkern g1="k" 	g2="v,y,yacute,ydieresis" 	k="-10" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="109" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="76" />
<hkern g1="k" 	g2="s" 	k="41" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="10" />
<hkern g1="h,m,n" 	g2="quoteleft,quotedblleft" 	k="78" />
<hkern g1="h,m,n" 	g2="quoteright,quotedblright" 	k="129" />
<hkern g1="h,m,n" 	g2="Y,Yacute,Ydieresis" 	k="168" />
<hkern g1="h,m,n" 	g2="T" 	k="106" />
<hkern g1="h,m,n" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="h,m,n" 	g2="w" 	k="59" />
<hkern g1="h,m,n" 	g2="v,y,yacute,ydieresis" 	k="70" />
<hkern g1="h,m,n" 	g2="W" 	k="72" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="78" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="115" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="231" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="199" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="119" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="53" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v,y,yacute,ydieresis" 	k="70" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="W" 	k="117" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="27" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotesinglbase,quotedblbase" 	k="37" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="parenright,bracketright,braceright" 	k="25" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-35" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="59" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="v,y,yacute,ydieresis" 	k="18" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="184" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="135" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="215" />
<hkern g1="quoteleft,quotedblleft" 	g2="z" 	k="45" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,ellipsis" 	k="279" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde" 	k="94" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="4" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="76" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-25" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="74" />
<hkern g1="quoteright,quotedblright" 	g2="w" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="v,y,yacute,ydieresis" 	k="94" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="240" />
<hkern g1="quoteright,quotedblright" 	g2="t" 	k="59" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="231" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="201" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,ellipsis" 	k="266" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="123" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="16" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="119" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="127" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="39" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v,y,yacute,ydieresis" 	k="78" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="12" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="47" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="quotedbl,quotesingle" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="119" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="162" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,ellipsis" 	k="106" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="8" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="33" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="162" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="180" />
<hkern g1="s" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="s" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="88" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="172" />
<hkern g1="s" 	g2="T" 	k="203" />
<hkern g1="s" 	g2="w" 	k="39" />
<hkern g1="s" 	g2="v,y,yacute,ydieresis" 	k="59" />
<hkern g1="s" 	g2="hyphen,uni00AD,endash,emdash" 	k="8" />
<hkern g1="s" 	g2="t" 	k="31" />
<hkern g1="s" 	g2="W" 	k="102" />
<hkern g1="s" 	g2="Z" 	k="12" />
<hkern g1="s" 	g2="quotesinglbase,quotedblbase" 	k="10" />
<hkern g1="s" 	g2="f,uniFB01,uniFB02" 	k="31" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="t" 	g2="quotedbl,quotesingle" 	k="4" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="53" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="w" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="w" 	g2="T" 	k="188" />
<hkern g1="w" 	g2="w" 	k="-20" />
<hkern g1="w" 	g2="v,y,yacute,ydieresis" 	k="-12" />
<hkern g1="w" 	g2="hyphen,uni00AD,endash,emdash" 	k="47" />
<hkern g1="w" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="w" 	g2="t" 	k="-6" />
<hkern g1="w" 	g2="W" 	k="47" />
<hkern g1="w" 	g2="s" 	k="27" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="113" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="86" />
<hkern g1="w" 	g2="f,uniFB01,uniFB02" 	k="-4" />
<hkern g1="zero" 	g2="comma,period,ellipsis" 	k="45" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="v,y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="154" />
<hkern g1="v,y,yacute,ydieresis" 	g2="T" 	k="238" />
<hkern g1="v,y,yacute,ydieresis" 	g2="w" 	k="-12" />
<hkern g1="v,y,yacute,ydieresis" 	g2="v,y,yacute,ydieresis" 	k="-16" />
<hkern g1="v,y,yacute,ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="v,y,yacute,ydieresis" 	g2="t" 	k="-8" />
<hkern g1="v,y,yacute,ydieresis" 	g2="W" 	k="68" />
<hkern g1="v,y,yacute,ydieresis" 	g2="s" 	k="27" />
<hkern g1="v,y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="v,y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="106" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="143" />
<hkern g1="v,y,yacute,ydieresis" 	g2="f,uniFB01,uniFB02" 	k="-4" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="z" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="z" 	g2="z" 	k="10" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="c,ccedilla" 	g2="T" 	k="147" />
<hkern g1="c,ccedilla" 	g2="w" 	k="10" />
<hkern g1="c,ccedilla" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="c,ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="2" />
<hkern g1="c,ccedilla" 	g2="W" 	k="39" />
</font>
</defs></svg> 