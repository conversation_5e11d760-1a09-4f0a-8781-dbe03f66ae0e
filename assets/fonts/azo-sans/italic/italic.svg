<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="azo_sansitalic" horiz-adv-x="1191" >
<font-face units-per-em="2048" ascent="1581" descent="-467" />
<missing-glyph horiz-adv-x="485" />
<glyph unicode="&#xfb01;" horiz-adv-x="1196" d="M113 842l28 166h158l43 245q32 178 127 256q94 78 246 78q77 0 157 -26l-28 -168q-70 26 -133 26q-76 0 -121 -39q-49 -42 -68 -155l-39 -217h252l-28 -166h-252l-148 -842h-184l147 842h-157zM760 0l178 1008h184l-178 -1008h-184zM961 1348q0 57 40 96t97 39 q54 0 89.5 -35.5t35.5 -87.5q0 -57 -41.5 -96t-98.5 -39q-53 0 -87.5 35.5t-34.5 87.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1196" d="M113 842l28 166h158l43 245q32 178 127 256q94 78 246 78q77 0 157 -26l-28 -168q-70 26 -133 26q-76 0 -121 -39q-49 -42 -68 -155l-39 -217h252l-28 -166h-252l-148 -842h-184l147 842h-157zM760 0l276 1565h185l-277 -1565h-184z" />
<glyph horiz-adv-x="2" />
<glyph horiz-adv-x="2" />
<glyph horiz-adv-x="682" />
<glyph unicode="&#xd;" horiz-adv-x="700" />
<glyph unicode=" "  horiz-adv-x="485" />
<glyph unicode="&#x09;" horiz-adv-x="485" />
<glyph unicode="&#xa0;" horiz-adv-x="485" />
<glyph unicode="!" horiz-adv-x="622" d="M113 100q0 57 40 96.5t97 39.5q54 0 89.5 -36t35.5 -87q0 -57 -41 -96.5t-98 -39.5q-53 0 -88 35.5t-35 87.5zM246 455l133 1028h213l-232 -1028h-114z" />
<glyph unicode="&#x22;" horiz-adv-x="714" d="M217 969l68 551h172l-127 -551h-113zM547 969l67 551h172l-127 -551h-112z" />
<glyph unicode="#" horiz-adv-x="1249" d="M20 457l58 125h250l147 319h-250l58 125h249l211 457h134l-211 -457h264l211 457h133l-211 -457h250l-58 -125h-249l-148 -319h250l-57 -125h-250l-211 -457h-133l211 457h-265l-210 -457h-134l211 457h-250zM461 582h264l147 319h-264z" />
<glyph unicode="$" horiz-adv-x="1148" d="M109 221l34 195q98 -67 208.5 -107.5t209.5 -40.5q51 0 96 10.5t82.5 31.5t59.5 58.5t22 86.5q0 33 -12.5 60t-30 46t-54.5 40t-65.5 34t-84.5 37q-13 5 -19 8q-65 27 -103.5 45t-86.5 49t-74 63t-44 79t-18 104q0 162 118 261t304 105l45 252h152l-47 -264 q141 -24 272 -94l-33 -188q-87 56 -188.5 88t-194.5 32q-103 0 -170 -43.5t-67 -130.5q0 -17 3.5 -32.5t9 -28t16.5 -25t20 -21.5t27 -20t30 -17.5t36 -17t38.5 -16.5t44 -18t45.5 -19q63 -27 105 -48t87 -52.5t70.5 -65.5t42.5 -82t17 -106q0 -170 -119.5 -267.5 t-317.5 -105.5l-45 -252h-151l45 262q-174 23 -315 115z" />
<glyph unicode="%" horiz-adv-x="1886" d="M205 1079q0 104 45 200t134.5 161t203.5 65q145 0 236 -96t91 -244q0 -104 -45 -200t-134.5 -161t-203.5 -65q-145 0 -236 96t-91 244zM270 0l1282 1483h154l-1282 -1483h-154zM338 1087q0 -89 53.5 -155t145.5 -66q105 0 175 89.5t70 201.5q0 89 -53 155t-145 66 q-105 0 -175.5 -89.5t-70.5 -201.5zM1061 317q0 104 45 200t134.5 161t203.5 65q145 0 236.5 -96t91.5 -244q0 -104 -45 -200t-134.5 -161t-203.5 -65q-145 0 -236.5 96t-91.5 244zM1194 326q0 -89 53.5 -155.5t145.5 -66.5q105 0 175 89.5t70 201.5q0 89 -53 155t-145 66 q-105 0 -175.5 -89t-70.5 -201z" />
<glyph unicode="&#x26;" horiz-adv-x="1431" d="M119 356q0 141 88 250.5t235 181.5l56 27l-37 49q-103 136 -103 260q0 104 57 192t152.5 138.5t206.5 50.5q143 0 240.5 -83.5t97.5 -221.5q0 -118 -72 -214.5t-190 -160.5l-94 -51l250 -330l219 445h186l-297 -588l225 -297l-2 -4h-219l-119 154q-159 -177 -421 -177 q-204 0 -331.5 105t-127.5 274zM303 377q0 -103 78.5 -167.5t208.5 -64.5q199 0 305 148l-301 395l-84 -41q-89 -45 -148 -112.5t-59 -157.5zM543 1128q0 -75 67 -163l54 -70l96 57q168 100 168 226q0 69 -47.5 113t-122.5 44q-89 0 -152 -59.5t-63 -147.5z" />
<glyph unicode="'" horiz-adv-x="385" d="M217 969l68 551h172l-127 -551h-113z" />
<glyph unicode="(" horiz-adv-x="686" d="M150 238q0 385 143 747q125 314 338 537h196q-235 -229 -374 -578q-134 -338 -134 -696q0 -387 152 -684h-168q-153 279 -153 674z" />
<glyph unicode=")" horiz-adv-x="686" d="M-127 -436q237 231 375 577q135 341 135 701q0 385 -154 680h170q154 -281 154 -670q0 -390 -143 -752q-125 -313 -338 -536h-199z" />
<glyph unicode="*" horiz-adv-x="925" d="M176 1149l66 131l268 -115l-41 -82zM219 811l254 238l59 -56l-219 -270zM541 1180l28 311h138l-84 -311h-82zM565 993l78 49l160 -241l-125 -78zM647 1157l303 123l19 -135l-310 -72z" />
<glyph unicode="+" d="M139 668l27 147h409l76 430h152l-76 -430h410l-27 -147h-410l-75 -430h-152l76 430h-410z" />
<glyph unicode="," horiz-adv-x="598" d="M-25 -301l215 580h193l-272 -580h-136z" />
<glyph unicode="-" horiz-adv-x="708" d="M113 485l28 164h477l-28 -164h-477z" />
<glyph unicode="." horiz-adv-x="598" d="M92 106q0 58 42.5 100t101.5 42q56 0 92.5 -37.5t36.5 -91.5q0 -58 -42.5 -100t-101.5 -42q-56 0 -92.5 37.5t-36.5 91.5z" />
<glyph unicode="/" horiz-adv-x="909" d="M-82 -78l997 1639h160l-997 -1639h-160z" />
<glyph unicode="0" horiz-adv-x="1165" d="M115 485q0 147 38 336.5t107 339.5q158 342 461 342q196 0 304 -133.5t108 -376.5q0 -145 -38 -333t-108 -341q-158 -342 -463 -342q-195 0 -302 133t-107 375zM309 508q0 -354 228 -354q98 0 165.5 63t116.5 174q54 122 86.5 282.5t32.5 299.5q0 352 -227 352 q-180 0 -283 -235q-52 -118 -85.5 -279t-33.5 -303z" />
<glyph unicode="1" horiz-adv-x="1021" d="M250 1122l33 191l571 192l-266 -1505h-195l219 1245z" />
<glyph unicode="2" horiz-adv-x="1140" d="M-29 4l568 569q87 87 142 148t104 129t71.5 129.5t22.5 122.5q0 107 -69 165t-185 58q-162 0 -340 -113l37 209q147 84 317 84q88 0 165 -24t137.5 -70.5t96 -123.5t35.5 -175q0 -152 -88.5 -293.5t-282.5 -335.5l-303 -303l2 -4h574l-31 -176h-971z" />
<glyph unicode="3" horiz-adv-x="1101" d="M27 121l36 209q94 -79 200.5 -125.5t209.5 -46.5q141 0 229 73q95 79 95 197q0 107 -89 168t-223 61h-141l31 172h147q86 0 156 27.5t113 73.5q67 70 67 172q0 104 -75 163.5t-189 59.5q-75 0 -158 -26t-149 -66l35 197q139 71 294 71q66 0 128 -14t118.5 -44t98.5 -73 t67 -106t25 -138q0 -145 -84 -249q-64 -77 -195 -129v-5q99 -39 158 -118t59 -197q0 -102 -44 -187t-120 -145q-150 -119 -368 -119q-121 0 -234 40t-198 104z" />
<glyph unicode="4" horiz-adv-x="1292" d="M12 362l949 1121h190l-168 -953h266l-30 -172h-267l-63 -358h-195l64 358h-744zM369 535l2 -5h417l58 326q18 103 59 313l-4 2q-123 -155 -215 -262z" />
<glyph unicode="5" horiz-adv-x="1124" d="M43 96l45 197q202 -135 393 -135q154 0 244 78q86 77 86 192q0 174 -223 254q-147 51 -381 55l184 746h696l-43 -176h-503l-103 -420q212 -18 353 -96q215 -117 215 -355q0 -100 -40 -188t-120 -152q-143 -119 -373 -119q-236 0 -430 119z" />
<glyph unicode="6" horiz-adv-x="1204" d="M94 434q0 76 20 148.5t61.5 144t81.5 126.5t103 130l416 500h238l-467 -555q-10 -13 -32.5 -39t-29.5 -35l4 -4q73 45 173 45q187 0 313.5 -115t126.5 -301q0 -105 -42.5 -199t-117.5 -163q-73 -67 -170 -103.5t-205 -36.5q-94 0 -178 28t-150.5 82.5t-105.5 144 t-39 202.5zM291 422q0 -117 77 -196t207 -79q140 0 240 95q43 41 68.5 100t25.5 123q0 118 -80 192t-208 74q-135 0 -234 -90q-96 -90 -96 -219z" />
<glyph unicode="7" horiz-adv-x="1095" d="M119 0l772 1307h-664l31 176h944l2 -4l-874 -1479h-211z" />
<glyph unicode="8" horiz-adv-x="1257" d="M98 383q0 149 90.5 257.5t243.5 156.5v6q-160 96 -160 282q0 188 162 312q141 108 336 108q118 0 215.5 -41t159 -125.5t61.5 -199.5q0 -123 -70 -220t-202 -143v-4q95 -47 147 -134t52 -194q0 -103 -47 -192.5t-128 -153.5q-152 -121 -372 -121q-136 0 -245.5 46.5 t-176 140t-66.5 219.5zM291 399q0 -113 82.5 -181t216.5 -68q153 0 248 81q100 87 100 211q0 112 -87.5 183t-215.5 71q-77 0 -145.5 -27t-114.5 -73q-84 -84 -84 -197zM459 1077q0 -102 77.5 -164.5t186.5 -62.5q131 0 219 82q80 77 80 178q0 103 -76 163t-186 60 q-121 0 -209 -71q-92 -75 -92 -185z" />
<glyph unicode="9" horiz-adv-x="1181" d="M166 1008q0 103 42 197.5t120 164.5q73 66 167.5 100.5t196.5 34.5q92 0 177 -29t151 -82t105.5 -136t39.5 -183q0 -73 -20 -144.5t-61.5 -142t-82 -126t-102.5 -130.5l-442 -532h-240l496 588q15 18 24 30l-4 5q-76 -35 -153 -35q-105 0 -198.5 49.5t-154.5 147.5 t-61 223zM358 1022q0 -122 80 -195t201 -73q136 0 231 92q99 94 99 219q0 114 -80.5 190t-206.5 76q-133 0 -227 -88q-97 -90 -97 -221z" />
<glyph unicode=":" horiz-adv-x="598" d="M94 104q0 58 41.5 99t100.5 41q55 0 92 -37t37 -90q0 -58 -42.5 -99t-101.5 -41q-55 0 -91 37t-36 90zM233 891q0 58 41.5 98.5t100.5 40.5q55 0 92 -37t37 -90q0 -58 -42.5 -98.5t-101.5 -40.5q-55 0 -91 37t-36 90z" />
<glyph unicode=";" horiz-adv-x="598" d="M-23 -301l216 580h190l-272 -580h-134zM233 891q0 58 41.5 98.5t100.5 40.5q55 0 92 -37t37 -90q0 -58 -42.5 -98.5t-101.5 -40.5q-55 0 -91 37t-36 90z" />
<glyph unicode="&#x3c;" horiz-adv-x="1386" d="M213 688l18 107l1106 442l-28 -160l-842 -336v-4l723 -336l-27 -155z" />
<glyph unicode="=" d="M102 459l27 147h971l-27 -147h-971zM176 877l27 147h971l-27 -147h-971z" />
<glyph unicode="&#x3e;" horiz-adv-x="1386" d="M135 246l29 160l842 335v4l-723 336l26 156l951 -442l-19 -107z" />
<glyph unicode="?" horiz-adv-x="974" d="M197 100q0 57 40 96.5t97 39.5q54 0 89.5 -36t35.5 -87q0 -57 -41.5 -96.5t-98.5 -39.5q-53 0 -87.5 35.5t-34.5 87.5zM272 1264l35 198q100 43 219 43q131 0 240.5 -51.5t176.5 -151t67 -227.5q0 -192 -139 -318.5t-355 -148.5l-55 -217h-170l65 373q92 0 172 17 t145.5 52t103.5 94t38 136q0 120 -86 191t-213 71q-122 0 -244 -61z" />
<glyph unicode="@" horiz-adv-x="1626" d="M72 432q0 162 60.5 313.5t165 267t256.5 185.5t323 70q195 0 354 -87.5t249.5 -243.5t90.5 -349q0 -93 -22 -177.5t-64 -152t-110.5 -107.5t-153.5 -40q-71 0 -128.5 28t-88.5 88h-5q-54 -56 -126 -87t-148 -31q-132 0 -213.5 90t-81.5 233q0 116 53 222t155.5 175.5 t231.5 69.5q142 0 310 -106l-66 -361q-8 -47 -8 -84q0 -135 127 -135q79 0 134 53.5t78 132t23 172.5q0 122 -42.5 231t-118 190.5t-187.5 129t-243 47.5q-194 0 -356.5 -100t-254.5 -267t-92 -362q0 -262 165 -432t429 -170q214 0 389 119l41 -88q-190 -129 -432 -129 q-197 0 -355 86.5t-248.5 245.5t-90.5 360zM565 436q0 -94 50 -151.5t139 -57.5q61 0 120.5 29.5t102.5 83.5q0 51 10 109l51 278q-77 53 -178 53q-65 0 -121.5 -31t-94 -80.5t-58.5 -110.5t-21 -122z" />
<glyph unicode="A" horiz-adv-x="1402" d="M-80 0l858 1483h191l336 -1483h-201l-90 414h-652l-235 -414h-207zM459 584h518l-59 268q-42 193 -82 399h-5q-106 -195 -208 -377z" />
<glyph unicode="B" horiz-adv-x="1282" d="M76 0l262 1483h399q243 0 365 -84q65 -45 101 -114.5t36 -154.5q0 -124 -75 -222.5t-212 -143.5v-4q103 -35 165.5 -115t62.5 -192q0 -94 -44 -182.5t-122 -149.5q-156 -121 -445 -121h-493zM301 176h305q204 0 297 84q82 76 82 191q0 110 -84 172.5t-231 62.5h-279z M418 836h313q59 0 112.5 14t99.5 42.5t73.5 78t27.5 114.5q0 109 -81 166q-77 56 -246 56h-215z" />
<glyph unicode="C" horiz-adv-x="1361" d="M127 627q0 173 66 343t194 298q117 117 268.5 177t319.5 60q238 0 418 -104l-35 -195q-180 119 -391 119q-131 0 -245 -46t-198 -130q-96 -95 -149 -230.5t-53 -273.5q0 -227 141.5 -357t388.5 -130q204 0 373 88l-37 -211q-163 -58 -350 -58q-209 0 -368.5 75 t-251 223.5t-91.5 351.5z" />
<glyph unicode="D" horiz-adv-x="1488" d="M76 0l262 1483h362q346 0 545 -180q100 -90 153.5 -213.5t53.5 -270.5q0 -155 -59 -307.5t-176 -269.5q-245 -242 -662 -242h-479zM301 176h268q339 0 521 197q80 86 123.5 201t43.5 231q0 105 -37 199t-110 161q-155 142 -434 142h-174z" />
<glyph unicode="E" horiz-adv-x="1124" d="M76 0l262 1483h846l-31 -176h-651l-80 -451h565l-31 -170h-565l-90 -510h670l-31 -176h-864z" />
<glyph unicode="F" horiz-adv-x="1130" d="M76 0l262 1483h858l-31 -176h-663l-84 -480h586l-31 -170h-586l-117 -657h-194z" />
<glyph unicode="G" horiz-adv-x="1476" d="M125 629q0 180 71 354t205 301q115 108 261 164.5t307 56.5q131 0 253.5 -35t213.5 -94l-35 -194q-201 143 -438 143q-127 0 -236.5 -44.5t-191.5 -121.5q-103 -97 -159.5 -232t-56.5 -278q0 -224 134 -359.5t372 -135.5q143 0 256 45l66 370h-326l31 170h520l-119 -669 q-92 -42 -208 -67.5t-232 -25.5q-150 0 -276.5 44.5t-218 126.5t-142.5 205.5t-51 275.5z" />
<glyph unicode="H" horiz-adv-x="1505" d="M76 0l262 1483h194l-110 -625h786l111 625h194l-262 -1483h-194l121 682h-787l-121 -682h-194z" />
<glyph unicode="I" horiz-adv-x="524" d="M76 0l262 1483h194l-262 -1483h-194z" />
<glyph unicode="J" horiz-adv-x="792" d="M-35 29l33 188q113 -59 205 -59q55 0 93 17.5t61.5 54.5t36.5 78t24 104l188 1071h195l-193 -1096q-33 -188 -112 -283q-107 -127 -295 -127q-118 0 -236 52z" />
<glyph unicode="K" horiz-adv-x="1282" d="M76 0l262 1483h194l-116 -656l4 -2l745 658h248l-782 -688l553 -795h-234l-538 780h-4l-138 -780h-194z" />
<glyph unicode="L" horiz-adv-x="1107" d="M76 0l262 1483h194l-229 -1307h698l-30 -176h-895z" />
<glyph unicode="M" horiz-adv-x="1728" d="M74 0l262 1483h174l385 -756l649 756h197l-262 -1483h-195l135 764q50 275 78 412l-4 2q-163 -196 -268 -320l-377 -438l-236 463q-69 136 -143 293l-4 -2q-23 -152 -57 -351l-146 -823h-188z" />
<glyph unicode="N" horiz-adv-x="1560" d="M76 0l262 1483h178l430 -776q94 -167 225 -416l5 2q46 292 75 457l129 733h189l-262 -1483h-179l-430 776q-94 167 -225 416l-4 -2q-38 -245 -76 -457l-129 -733h-188z" />
<glyph unicode="O" horiz-adv-x="1634" d="M127 664q0 154 52 302t149 265q106 129 255.5 201.5t327.5 72.5q190 0 343.5 -83t244 -240.5t90.5 -362.5q0 -154 -51.5 -302t-148.5 -265q-106 -129 -256 -202t-328 -73q-142 0 -266 49t-215.5 136.5t-144 217.5t-52.5 284zM322 678q0 -223 135 -371.5t356 -148.5 q128 0 237 52.5t189 151.5q73 90 114.5 206.5t41.5 236.5q0 223 -135.5 371.5t-356.5 148.5q-128 0 -237 -53t-189 -152q-73 -90 -114 -206t-41 -236z" />
<glyph unicode="P" horiz-adv-x="1257" d="M76 0l262 1483h393q285 0 420 -121q129 -113 129 -289q0 -93 -39.5 -184.5t-116.5 -161.5q-156 -141 -460 -141h-291l-103 -586h-194zM403 756h285q214 0 309 92q88 85 88 209q0 109 -75 172q-90 78 -283 78h-225z" />
<glyph unicode="Q" horiz-adv-x="1634" d="M127 664q0 154 52 302t149 265q106 129 255.5 201.5t327.5 72.5q190 0 343.5 -83t244 -240.5t90.5 -362.5q0 -154 -51.5 -302t-148.5 -265q-158 -194 -396 -250v-4q213 -141 463 -231l-190 -111q-288 117 -533 326q-264 24 -435 209t-171 473zM322 678q0 -223 135 -371.5 t356 -148.5q128 0 237 52.5t189 151.5q73 90 114.5 206.5t41.5 236.5q0 223 -135.5 371.5t-356.5 148.5q-128 0 -237 -53t-189 -152q-73 -90 -114 -206t-41 -236z" />
<glyph unicode="R" horiz-adv-x="1280" d="M76 0l262 1483h403q269 0 398 -115q121 -108 121 -276q0 -86 -37 -174.5t-107 -151.5q-104 -94 -268 -119l309 -647h-215l-293 635h-266l-113 -635h-194zM412 795h297q181 0 276 88q80 73 80 186q0 111 -80 170q-83 68 -264 68h-219z" />
<glyph unicode="S" horiz-adv-x="1132" d="M68 141l36 213q93 -89 214 -143.5t235 -54.5q133 0 211 63q86 70 86 187q0 30 -8 56.5t-19.5 46.5t-35 41t-42.5 34.5t-56 32.5t-60.5 30t-69.5 31q-56 25 -92.5 43t-81.5 45t-72.5 54.5t-53 63.5t-37 81t-11.5 98q0 194 145 321q140 121 346 121q111 0 223.5 -35.5 t207.5 -101.5l-37 -207q-91 75 -199 119.5t-211 44.5q-124 0 -199 -61q-81 -66 -81 -177q0 -47 17 -82.5t54.5 -63.5t78 -49.5t106.5 -51.5q22 -10 34 -15q70 -32 114 -55t93.5 -58.5t76.5 -72t45.5 -90t18.5 -117.5q0 -107 -45 -199.5t-127 -154.5q-134 -101 -331 -101 q-128 0 -252.5 43.5t-220.5 120.5z" />
<glyph unicode="T" horiz-adv-x="1318" d="M217 1307l31 176h1169l-31 -176h-487l-229 -1307h-195l230 1307h-488z" />
<glyph unicode="U" horiz-adv-x="1470" d="M158 465q0 86 26 246l135 772h195l-135 -772q-23 -129 -23 -217q0 -161 83.5 -248.5t256.5 -87.5q224 0 340 151q87 114 133 379l140 795h188l-141 -807q-32 -180 -73.5 -290.5t-104.5 -188.5q-178 -220 -496 -220q-246 0 -385 130.5t-139 357.5z" />
<glyph unicode="V" horiz-adv-x="1343" d="M190 1483h203l152 -764q39 -192 86 -455h4q115 222 239 449l420 770h205l-819 -1483h-188z" />
<glyph unicode="W" horiz-adv-x="2066" d="M195 1483h200l107 -803q32 -247 47 -391h4q18 40 188 393l394 801h143l111 -803q31 -227 49 -391h4q52 114 186 393l389 801h201l-723 -1483h-205l-102 745q-34 246 -45 353h-4q-28 -62 -170 -357l-365 -741h-207z" />
<glyph unicode="X" horiz-adv-x="1329" d="M-76 0l668 786l-344 697h215l121 -250q93 -193 143 -307h4q97 121 248 301l215 256h233l-591 -697l395 -786h-219l-156 315q-116 238 -158 330h-4q-130 -162 -258 -313l-278 -332h-234z" />
<glyph unicode="Y" horiz-adv-x="1325" d="M180 1483h217l166 -330q82 -160 156 -315h4q143 177 262 315l281 330h225l-698 -811l-119 -672h-195l119 670z" />
<glyph unicode="Z" horiz-adv-x="1208" d="M-57 4l1022 1299l-2 4h-742l31 176h1085l2 -4l-1022 -1299l2 -4h779l-31 -176h-1122z" />
<glyph unicode="[" horiz-adv-x="671" d="M0 -436l346 1958h440l-26 -148h-271l-292 -1663h270l-27 -147h-440z" />
<glyph unicode="\" horiz-adv-x="909" d="M236 1561h145l377 -1639h-146z" />
<glyph unicode="]" horiz-adv-x="671" d="M-100 -436l26 147h271l292 1663h-270l27 148h440l-346 -1958h-440z" />
<glyph unicode="^" horiz-adv-x="1161" d="M184 741l506 764h133l236 -764h-152l-180 564h-4l-383 -564h-156z" />
<glyph unicode="_" horiz-adv-x="950" d="M-139 -291l18 107h950l-18 -107h-950z" />
<glyph unicode="`" horiz-adv-x="1015" d="M455 1489h204l99 -336h-137z" />
<glyph unicode="a" d="M90 410q0 119 45.5 238.5t132.5 209.5q85 88 195 130t227 42q175 0 412 -110l-162 -920h-184l16 88l-4 2q-138 -113 -287 -113q-167 0 -279 120t-112 313zM274 424q0 -132 69.5 -208.5t174.5 -76.5q149 0 289 146l92 528q-108 53 -227 53q-173 0 -283 -127 q-57 -65 -86 -148.5t-29 -166.5z" />
<glyph unicode="b" horiz-adv-x="1183" d="M76 29l272 1536h184l-110 -617l4 -2q118 84 264 84q173 0 288.5 -121t115.5 -315q0 -116 -44 -233t-132 -205q-179 -179 -484 -179q-173 0 -358 52zM283 162q97 -23 176 -23q213 0 334 131q52 57 84 140t32 170q0 131 -71.5 207.5t-188.5 76.5q-133 0 -258 -92z" />
<glyph unicode="c" horiz-adv-x="1021" d="M90 424q0 123 50.5 247t152.5 214q167 145 395 145q153 0 289 -63l-31 -176q-142 71 -264 71q-157 0 -268 -100q-64 -59 -102 -146t-38 -178q0 -133 83 -213t231 -80q142 0 278 68l-32 -190q-123 -46 -265 -46q-97 0 -182.5 28.5t-152 83t-105.5 141t-39 194.5z" />
<glyph unicode="d" d="M90 412q0 119 43.5 236t126.5 206q165 176 418 176q134 0 246 -53l4 2l104 586h185l-277 -1565h-184l16 88l-4 2q-133 -113 -285 -113q-171 0 -282 120.5t-111 314.5zM274 428q0 -132 68.5 -210.5t177.5 -78.5q147 0 285 142l92 526q-109 61 -231 61q-172 0 -283 -133 q-52 -62 -80.5 -143t-28.5 -164z" />
<glyph unicode="e" horiz-adv-x="1126" d="M90 438q0 111 37.5 218t105.5 188q80 95 187 140.5t217 45.5q182 0 297 -111t115 -294q0 -90 -23 -174h-754q0 -147 90.5 -227.5t235.5 -80.5q164 0 320 86l-35 -192q-126 -60 -297 -60q-103 0 -192.5 29t-157.5 85t-107 145t-39 202zM295 596h582v20q0 111 -64.5 179.5 t-183.5 68.5t-207.5 -70.5t-126.5 -197.5z" />
<glyph unicode="f" horiz-adv-x="686" d="M113 842l28 166h158l43 245q32 178 127 256q94 78 246 78q77 0 157 -26l-28 -168q-70 26 -133 26q-76 0 -121 -39q-49 -42 -68 -155l-39 -217h252l-28 -166h-252l-148 -842h-184l147 842h-157z" />
<glyph unicode="g" horiz-adv-x="1175" d="M23 -385l34 192q70 -50 158.5 -81t175.5 -31q130 0 224 72.5t122 220.5l17 96l-2 2q-120 -82 -250 -82q-181 0 -296.5 119.5t-115.5 310.5q0 116 45 231.5t127 198.5q78 80 187 123t233 43q197 0 405 -100l-163 -932q-84 -471 -543 -471q-195 0 -358 88zM274 449 q0 -129 73 -207t192 -78q123 0 245 90l101 569q-103 43 -223 43q-169 0 -275 -118q-53 -59 -83 -139t-30 -160z" />
<glyph unicode="h" horiz-adv-x="1212" d="M74 0l276 1565h185l-121 -680l4 -2q178 147 358 147q140 0 222.5 -82t82.5 -227q0 -53 -16 -141l-102 -580h-185l101 567q14 77 14 121q0 170 -170 170q-84 0 -165 -40.5t-177 -117.5l-123 -700h-184z" />
<glyph unicode="i" horiz-adv-x="509" d="M74 0l178 1008h184l-178 -1008h-184zM274 1348q0 57 40.5 96t97.5 39q54 0 89.5 -35.5t35.5 -87.5q0 -57 -41.5 -96t-98.5 -39q-53 0 -88 35.5t-35 87.5z" />
<glyph unicode="j" horiz-adv-x="516" d="M-252 -451l29 168q81 -22 121 -22q76 0 110 45t51 139l199 1129h184l-202 -1151q-25 -150 -97 -232q-90 -98 -245 -98q-79 0 -150 22zM281 1348q0 57 40 96t97 39q54 0 89.5 -35.5t35.5 -87.5q0 -57 -41.5 -96t-98.5 -39q-53 0 -87.5 35.5t-34.5 87.5z" />
<glyph unicode="k" horiz-adv-x="1097" d="M74 0l276 1565h185l-173 -973h5l542 416h246l-590 -451l406 -557h-219l-394 545h-4l-96 -545h-184z" />
<glyph unicode="l" horiz-adv-x="509" d="M74 0l276 1565h185l-277 -1565h-184z" />
<glyph unicode="m" horiz-adv-x="1880" d="M74 0l178 1008h184l-22 -125l4 -2q171 149 352 149q100 0 174 -48t103 -134q209 182 405 182q135 0 217 -80t82 -215q0 -54 -16 -145l-105 -590h-184l102 578q15 83 15 118q0 76 -41 119t-123 43q-154 0 -336 -164q-2 -36 -14 -104l-105 -590h-184l102 578q15 83 15 118 q0 76 -41 119t-123 43q-148 0 -332 -162l-123 -696h-184z" />
<glyph unicode="n" horiz-adv-x="1212" d="M74 0l178 1008h184l-22 -123l4 -2q183 147 362 147q136 0 218.5 -82.5t82.5 -226.5q0 -53 -16 -141l-102 -580h-185l101 567q14 77 14 121q0 170 -162 170q-150 0 -350 -158l-123 -700h-184z" />
<glyph unicode="o" horiz-adv-x="1212" d="M90 442q0 115 43 227t123 197q158 164 391 164q208 0 341.5 -128t133.5 -337q0 -115 -43 -227t-123 -197q-158 -164 -391 -164q-208 0 -341.5 128t-133.5 337zM274 455q0 -135 81 -222.5t223 -87.5q148 0 245 105q54 57 84.5 138t30.5 165q0 135 -80.5 222t-222.5 87 q-150 0 -246 -104q-54 -57 -84.5 -138t-30.5 -165z" />
<glyph unicode="p" horiz-adv-x="1183" d="M-6 -451l258 1459h184l-10 -60l4 -2q119 84 254 84q179 0 294.5 -126.5t115.5 -328.5q0 -120 -43 -234.5t-123 -197.5q-160 -166 -416 -166q-137 0 -246 48l-4 -2l-84 -474h-184zM293 193q110 -54 231 -54q169 0 275 119q54 60 82 140.5t28 164.5q0 139 -72 220t-190 81 q-66 0 -132.5 -24.5t-119.5 -65.5z" />
<glyph unicode="q" horiz-adv-x="1196" d="M90 416q0 120 46.5 239.5t133.5 204.5q173 170 455 170q177 0 377 -76l-248 -1405h-184l90 512l-4 2q-115 -86 -258 -86q-175 0 -291.5 122t-116.5 317zM274 428q0 -131 72.5 -209t185.5 -78q136 0 261 103l104 590q-100 34 -197 34q-192 0 -307 -125q-55 -59 -87 -143.5 t-32 -171.5z" />
<glyph unicode="r" horiz-adv-x="819" d="M74 0l178 1008h184l-30 -172l4 -2q64 81 168 131.5t202 50.5q34 0 64 -6l-31 -179q-35 5 -61 5q-106 0 -205.5 -55.5t-177.5 -153.5l-111 -627h-184z" />
<glyph unicode="s" horiz-adv-x="1011" d="M51 92l33 191q78 -65 185.5 -103.5t211.5 -38.5q106 0 160 45q49 39 49 105q0 18 -5.5 32.5t-13 25.5t-23.5 21t-29 16t-39 14t-43 12.5l-51 13.5t-54 14q-53 15 -92 29.5t-77.5 36.5t-63 48t-39.5 62.5t-15 81.5q0 63 27 126.5t76 107.5q107 98 301 98q207 0 379 -94 l-33 -186q-74 53 -172.5 83.5t-192.5 30.5q-88 0 -137 -30q-61 -40 -61 -109q0 -24 10.5 -42.5t24.5 -30t43.5 -22.5t50.5 -16t64 -15.5t67 -16.5q64 -17 111 -37t89 -50.5t63.5 -75t21.5 -102.5q0 -63 -26 -127.5t-81 -113.5q-111 -99 -307 -99q-237 0 -412 115z" />
<glyph unicode="t" horiz-adv-x="778" d="M113 842l28 166h185l49 284l186 11l-51 -295h297l-29 -166h-299l-76 -430q-14 -86 -14 -123q0 -68 38 -106t114 -38q49 0 116 19l-30 -172q-62 -15 -117 -15q-134 0 -220.5 72t-86.5 213q0 64 14 144l78 436h-182z" />
<glyph unicode="u" horiz-adv-x="1200" d="M121 279q0 61 16 159l101 570h184l-98 -557q-15 -92 -15 -140q0 -77 40.5 -120.5t125.5 -43.5q159 0 344 164l123 697h184l-178 -1008h-184l20 117l-4 2q-189 -142 -348 -142q-134 0 -222.5 81.5t-88.5 220.5z" />
<glyph unicode="v" horiz-adv-x="1110" d="M131 1008h195l96 -414q38 -164 80 -367h4q110 197 211 369l241 412h199l-600 -1008h-182z" />
<glyph unicode="w" horiz-adv-x="1660" d="M129 1008h197l69 -449q33 -212 45 -311h4q56 108 166 311l246 449h129l86 -449q37 -195 55 -311h4q57 119 154 309l229 451h197l-528 -1008h-181l-83 428q-36 195 -48 279h-4q-69 -137 -145 -277l-238 -430h-182z" />
<glyph unicode="x" horiz-adv-x="1013" d="M-104 0l501 537l-272 471h207l78 -138q67 -118 120 -219h5q76 90 196 223l121 134h215l-436 -469l311 -539h-211l-100 178q-73 129 -131 242h-4q-51 -58 -211 -232l-174 -188h-215z" />
<glyph unicode="y" horiz-adv-x="1110" d="M18 -451l375 566l-264 893h199l102 -355q46 -158 92 -338h4q140 229 213 342l224 351h200l-938 -1459h-207z" />
<glyph unicode="z" horiz-adv-x="989" d="M-39 4l703 832l-2 4h-498l29 168h835l2 -4l-704 -832l2 -4h540l-28 -168h-877z" />
<glyph unicode="{" horiz-adv-x="731" d="M57 469l27 147h70q88 0 122 37t50 129l80 451q27 154 105.5 232.5t219.5 78.5q71 0 143 -20l-24 -142q-54 15 -107 15q-75 0 -114.5 -42t-57.5 -149l-73 -420q-19 -104 -55.5 -162.5t-112.5 -78.5v-4q90 -37 90 -152q0 -26 -12 -108l-68 -387q-10 -55 -10 -89 q0 -64 34 -90t101 -26q47 0 88 6l-27 -148q-34 -6 -86 -6q-136 0 -209 59t-73 181q0 42 10 104l72 408q8 41 8 76q0 53 -29 76.5t-104 23.5h-58z" />
<glyph unicode="|" horiz-adv-x="649" d="M80 -451l369 2089h151l-369 -2089h-151z" />
<glyph unicode="}" horiz-adv-x="731" d="M-129 -438l25 141q51 -14 106 -14q75 0 114.5 41.5t57.5 148.5l74 420q19 104 55.5 163t112.5 79v4q-90 37 -90 151q0 27 12 109l68 387q10 55 10 88q0 64 -34 90.5t-101 26.5q-48 0 -88 -6l26 147q34 6 86 6q136 0 209.5 -59t73.5 -180q0 -43 -10 -105l-72 -407 q-8 -41 -8 -76q0 -53 29 -77t104 -24h57l-26 -147h-70q-88 0 -122 -37t-50 -129l-80 -450q-27 -154 -106 -233t-220 -79q-68 0 -143 21z" />
<glyph unicode="~" horiz-adv-x="1105" d="M158 604l37 207q89 80 206 80q97 0 228 -74q86 -48 121 -58q31 -9 63 -9q115 0 213 131l4 -2l-37 -207q-89 -80 -207 -80q-96 0 -227 74q-86 48 -121 58q-31 9 -63 9q-115 0 -213 -131z" />
<glyph unicode="&#xa1;" horiz-adv-x="622" d="M35 -475l231 1028h115l-133 -1028h-213zM252 895q0 57 41 96t98 39q53 0 88 -35.5t35 -87.5q0 -57 -40 -96t-97 -39q-53 0 -89 36t-36 87z" />
<glyph unicode="&#xa2;" horiz-adv-x="1050" d="M121 662q0 123 50.5 246.5t152.5 213.5q139 120 311 140l45 258h151l-45 -256q116 -11 222 -60l-31 -176q-144 72 -264 72q-156 0 -269 -101q-64 -59 -101.5 -145.5t-37.5 -177.5q0 -133 82.5 -213t230.5 -80q143 0 279 68l-33 -191q-120 -45 -262 -45l-45 -252h-151 l47 270q-150 37 -241 147t-91 282z" />
<glyph unicode="&#xa3;" horiz-adv-x="1155" d="M59 0l27 156q246 158 246 477h-160l27 155h120q-18 167 -18 218q0 231 142 365t376 134q186 0 330 -94l-35 -192q-142 104 -309 104q-148 0 -228.5 -84t-80.5 -229q0 -62 20 -222h365l-27 -155h-328q0 -144 -50.5 -259.5t-143.5 -193.5v-4h676l-31 -176h-918z" />
<glyph unicode="&#xa4;" horiz-adv-x="1185" d="M63 324l193 163q-41 80 -41 185q0 165 104 301l-112 141l137 121l113 -143q103 61 223 61q151 0 258 -84l195 166l96 -125l-193 -164q41 -80 41 -184q0 -165 -104 -301l112 -142l-137 -120l-112 143q-103 -61 -224 -61q-151 0 -258 84l-194 -166zM362 682 q0 -110 73.5 -183t189.5 -73q127 0 216 96t89 230q0 110 -73 183t-189 73q-127 0 -216.5 -96t-89.5 -230z" />
<glyph unicode="&#xa5;" horiz-adv-x="1355" d="M180 449l27 149h393l25 139l-47 92h-330l26 150h226l-258 504h217l131 -262q56 -109 155 -316h5q156 193 262 316l223 262h225l-434 -504h229l-26 -150h-332l-78 -90l-24 -141h393l-27 -149h-393l-78 -449h-194l77 449h-393z" />
<glyph unicode="&#xa6;" horiz-adv-x="649" d="M80 -451l141 803h152l-142 -803h-151zM307 836l142 802h151l-141 -802h-152z" />
<glyph unicode="&#xa7;" horiz-adv-x="1142" d="M104 -225l33 186q67 -53 149.5 -84t162.5 -31q101 0 168 48t67 135q0 32 -11.5 59.5t-27.5 47t-51 43.5t-63 40t-83 45q-21 11 -32 17q-58 31 -92 51t-75.5 52t-64 63t-38 75t-15.5 96q0 117 68.5 200t189.5 124v4q-82 85 -82 201q0 152 117 251t293 99q158 0 311 -80 l-31 -178q-142 92 -280 92q-95 0 -160 -45.5t-65 -126.5q0 -22 6 -42t13.5 -35t27 -33t32 -28.5t44.5 -29t48.5 -27.5t58.5 -31q22 -12 34 -18q48 -26 78.5 -43.5t68.5 -43.5t61 -51t44.5 -57t31 -70t9.5 -83q0 -120 -68 -207.5t-182 -121.5v-5q69 -81 69 -190 q0 -163 -118.5 -262.5t-300.5 -99.5q-183 0 -345 94zM309 637q0 -65 45 -110.5t154 -102.5q95 -49 149 -80q90 15 150.5 67.5t60.5 135.5q0 67 -41.5 112.5t-130.5 94.5q-38 21 -80 42t-67 35q-63 -7 -113.5 -25.5t-88.5 -62t-38 -106.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="1015" d="M340 1315q0 51 36 85t87 34q45 0 76.5 -31t31.5 -76q0 -51 -35.5 -85t-86.5 -34q-46 0 -77.5 31.5t-31.5 75.5zM737 1315q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1662" d="M117 741q0 158 57.5 299.5t157.5 243.5t241 161.5t301 59.5t301 -59.5t241 -161.5t158 -243.5t58 -299.5t-58 -299.5t-158 -243.5t-241 -161.5t-301 -59.5t-301 59.5t-241 161.5t-157.5 243.5t-57.5 299.5zM213 741q0 -186 83 -338.5t235 -241.5t343 -89t343 89 t235.5 241.5t83.5 338.5t-83.5 339t-235.5 242t-343 89q-143 0 -267 -52t-210 -142t-135 -213.5t-49 -262.5zM481 748q0 181 123 301t311 120q124 0 226 -55v-135q-104 74 -228 74q-133 0 -218 -87t-85 -216q0 -130 86.5 -214.5t223.5 -84.5q133 0 233 84v-140 q-98 -61 -240 -61q-188 0 -310 117.5t-122 296.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="786" d="M180 1110q0 159 106.5 277t274.5 118q143 0 291 -98l-100 -571h-138l9 45l-5 2q-78 -62 -172 -62q-116 0 -191 79.5t-75 209.5zM319 1122q0 -80 45.5 -128t122.5 -48q88 0 158 64l57 325q-70 45 -153 45q-100 0 -165 -78t-65 -180z" />
<glyph unicode="&#xab;" horiz-adv-x="1112" d="M92 530l404 422h170l-416 -436l258 -461h-152zM506 530l403 422h170l-415 -436l258 -461h-152z" />
<glyph unicode="&#xac;" horiz-adv-x="1189" d="M139 668l27 147h964l-102 -577h-151l75 430h-813z" />
<glyph unicode="&#xad;" horiz-adv-x="708" d="M113 485l28 164h477l-28 -164h-477z" />
<glyph unicode="&#xae;" horiz-adv-x="946" d="M201 1124q0 164 108.5 272.5t274.5 108.5t274.5 -108.5t108.5 -272.5t-108.5 -272.5t-274.5 -108.5t-274.5 108.5t-108.5 272.5zM262 1124q0 -139 91 -230t231 -91t230.5 91t90.5 230t-90.5 230.5t-230.5 91.5t-231 -91.5t-91 -230.5zM444 918v413h148q66 0 111 -35.5 t45 -97.5q0 -44 -25.5 -75.5t-65.5 -45.5l101 -159h-84l-88 149h-68v-149h-74zM518 1130h68q39 0 62.5 17.5t23.5 50.5q0 32 -23.5 49t-62.5 17h-68v-134z" />
<glyph unicode="&#xaf;" horiz-adv-x="1015" d="M360 1251l25 140h559l-24 -140h-560z" />
<glyph unicode="&#xb0;" horiz-adv-x="704" d="M168 1204q0 127 87.5 214t219.5 87t219.5 -87t87.5 -214t-87.5 -214t-219.5 -87t-219.5 87t-87.5 214zM266 1204q0 -85 60 -145t149 -60t149 60t60 145t-60 145t-149 60t-149 -60t-60 -145z" />
<glyph unicode="&#xb1;" d="M76 160l26 147h918l-27 -147h-917zM199 856l26 148h383l66 368h151l-65 -368h383l-27 -148h-383l-65 -369h-152l66 369h-383z" />
<glyph unicode="&#xb2;" horiz-adv-x="737" d="M102 868l373 369q100 99 140 155t40 115t-40 89t-113 30q-50 0 -108 -21.5t-105 -56.5l30 166q86 53 205 53q123 0 205 -64.5t82 -178.5q0 -88 -49.5 -168t-151.5 -182l-168 -166l2 -4h312l-25 -140h-627z" />
<glyph unicode="&#xb3;" horiz-adv-x="708" d="M145 942l29 166q51 -55 119 -87t133 -32q80 0 131 39t51 98q0 57 -50 89t-128 32h-88l23 123h92q100 0 153 53q37 37 37 86q0 58 -43.5 88.5t-111.5 30.5q-97 0 -187 -51l27 149q80 39 180 39q52 0 102 -13t93 -39t69.5 -71t26.5 -102q0 -87 -53 -147q-48 -55 -129 -76 v-4q143 -55 143 -191q0 -122 -109 -200q-98 -72 -235 -72q-77 0 -150.5 24.5t-124.5 67.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="1015" d="M489 1153l218 336h208l-284 -336h-142z" />
<glyph unicode="&#xb5;" horiz-adv-x="1247" d="M207 -451v1459h184v-582q0 -83 11.5 -130.5t37.5 -78.5q56 -70 160 -70q88 0 172 49t143 123v689h185v-1008h-185v127l-4 2q-152 -152 -342 -152q-108 0 -184 48l-4 -2l18 -474h-192z" />
<glyph unicode="&#xb6;" horiz-adv-x="1155" d="M215 1065q0 103 56.5 200t153.5 157.5t206 60.5h487l-340 -1934h-151l315 1794h-164l-315 -1794h-152l209 1192q-134 7 -219.5 99t-85.5 225z" />
<glyph unicode="&#xb7;" horiz-adv-x="598" d="M182 612q0 58 42.5 100t101.5 42q56 0 92.5 -37.5t36.5 -91.5q0 -58 -42.5 -100t-101.5 -42q-56 0 -92.5 37.5t-36.5 91.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1017" d="M176 -395l49 98q71 -51 135 -51q39 0 67 19t28 55q0 25 -16.5 45t-43.5 32t-52.5 19.5t-53.5 11.5l117 234l86 -31l-64 -129q147 -47 147 -170q0 -83 -60.5 -137t-154.5 -54q-99 0 -184 58z" />
<glyph unicode="&#xb9;" horiz-adv-x="649" d="M270 1507l25 144l381 116l-166 -903h-152l131 711z" />
<glyph unicode="&#xba;" horiz-adv-x="829" d="M188 1124q0 159 102 270t261 111q138 0 229.5 -82.5t91.5 -220.5q0 -159 -101.5 -270t-260.5 -111q-139 0 -230.5 82.5t-91.5 220.5zM328 1135q0 -81 49 -135t137 -54q94 0 156.5 71t62.5 175q0 81 -49 134.5t-137 53.5q-94 0 -156.5 -70.5t-62.5 -174.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1112" d="M33 55l416 437l-259 460h152l264 -475l-403 -422h-170zM446 55l416 437l-258 460h152l264 -475l-404 -422h-170z" />
<glyph unicode="&#xbc;" horiz-adv-x="1875" d="M242 1245l24 144l381 116l-166 -903h-151l131 711zM250 -27l1225 1510h176l-1225 -1510h-176zM965 209l589 680h148l-98 -561h149l-20 -123h-152l-37 -205h-151l37 205h-463zM1221 332l2 -4h229l27 151q10 61 39 197l-5 2q-47 -60 -133 -162z" />
<glyph unicode="&#xbd;" horiz-adv-x="1976" d="M242 1245l24 144l381 116l-166 -903h-151l131 711zM250 -27l1225 1510h176l-1225 -1510h-176zM1145 4l373 369q100 99 140 155t40 115t-40.5 89t-113.5 30q-50 0 -108 -21.5t-105 -56.5l31 166q86 53 205 53q122 0 204 -65t82 -179q0 -88 -49 -168t-151 -182l-168 -166 l2 -4h311l-24 -139h-627z" />
<glyph unicode="&#xbe;" horiz-adv-x="2021" d="M143 680l29 166q51 -55 119 -87t133 -32q80 0 131 39t51 98q0 57 -50 89t-128 32h-88l22 123h93q100 0 153 53q37 37 37 86q0 58 -44 88.5t-112 30.5q-96 0 -186 -51l27 149q80 39 180 39q52 0 102 -13t93 -39t69.5 -71t26.5 -102q0 -88 -53 -148q-47 -53 -130 -75v-4 q144 -56 144 -191q0 -123 -109 -201q-96 -71 -235 -71q-77 0 -150.5 24.5t-124.5 67.5zM395 -27l1225 1510h176l-1225 -1510h-176zM1110 209l590 680h147l-98 -561h149l-20 -123h-152l-36 -205h-152l37 205h-463zM1366 332l2 -4h229l27 151q10 61 39 197l-4 2 q-47 -60 -133 -162z" />
<glyph unicode="&#xbf;" horiz-adv-x="974" d="M-25 -68q0 192 139 318.5t355 148.5l55 217h170l-65 -372q-92 0 -172 -17t-145.5 -52t-103.5 -94t-38 -136q0 -120 86 -191t213 -71q122 0 244 61l-35 -199q-100 -43 -219 -43q-131 0 -240.5 51.5t-176.5 151t-67 227.5zM526 895q0 57 41.5 96t98.5 39q53 0 87.5 -35.5 t34.5 -87.5q0 -57 -40 -96t-97 -39q-53 0 -89 36t-36 87z" />
<glyph unicode="&#xc0;" horiz-adv-x="1402" d="M-80 0l858 1483h191l336 -1483h-201l-90 414h-652l-235 -414h-207zM459 584h518l-59 268q-42 193 -82 399h-5q-106 -195 -208 -377zM731 1964h205l98 -336h-137z" />
<glyph unicode="&#xc1;" horiz-adv-x="1402" d="M-80 0l858 1483h191l336 -1483h-201l-90 414h-652l-235 -414h-207zM459 584h518l-59 268q-42 193 -82 399h-5q-106 -195 -208 -377zM766 1628l217 336h209l-285 -336h-141z" />
<glyph unicode="&#xc2;" horiz-adv-x="1402" d="M-80 0l858 1483h191l336 -1483h-201l-90 414h-652l-235 -414h-207zM459 584h518l-59 268q-42 193 -82 399h-5q-106 -195 -208 -377zM569 1628l291 336h197l172 -336h-146l-143 215l-209 -215h-162z" />
<glyph unicode="&#xc3;" horiz-adv-x="1402" d="M-80 0l858 1483h191l336 -1483h-201l-90 414h-652l-235 -414h-207zM459 584h518l-59 268q-42 193 -82 399h-5q-106 -195 -208 -377zM594 1673l31 174q69 76 170 76q59 0 163 -53q84 -43 140 -43q88 0 164 100l4 -2l-31 -174q-69 -76 -170 -76q-58 0 -164 54 q-84 43 -139 43q-88 0 -164 -101z" />
<glyph unicode="&#xc4;" horiz-adv-x="1402" d="M-80 0l858 1483h191l336 -1483h-201l-90 414h-652l-235 -414h-207zM459 584h518l-59 268q-42 193 -82 399h-5q-106 -195 -208 -377zM616 1790q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5zM1014 1790q0 51 36 85 t87 34q45 0 76.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77 31.5t-31 75.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1402" d="M-80 0l858 1483h191l336 -1483h-201l-90 414h-652l-235 -414h-207zM459 584h518l-59 268q-42 193 -82 399h-5q-106 -195 -208 -377zM700 1817q0 101 72.5 177.5t177.5 76.5q95 0 159.5 -60t64.5 -156q0 -100 -72.5 -175.5t-177.5 -75.5q-95 0 -159.5 59t-64.5 154z M809 1823q0 -47 30.5 -82t88.5 -35q56 0 96.5 41t40.5 102q0 49 -30.5 84t-88.5 35q-55 0 -96 -41.5t-41 -103.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1859" d="M-49 0l1038 1483h930l-31 -176h-610l-80 -451h524l-30 -170h-525l-90 -510h629l-31 -176h-823l74 414h-477l-291 -414h-207zM569 584h385l131 733h-4q-129 -190 -241 -350z" />
<glyph unicode="&#xc7;" horiz-adv-x="1361" d="M127 627q0 173 66 343t194 298q117 117 268.5 177t319.5 60q238 0 418 -104l-35 -195q-180 119 -391 119q-131 0 -245 -46t-198 -130q-96 -95 -149 -230.5t-53 -273.5q0 -227 141.5 -357t388.5 -130q204 0 373 88l-37 -211q-163 -58 -350 -58q-34 0 -68 3l-35 -72 q148 -48 148 -170q0 -83 -60.5 -137t-154.5 -54q-100 0 -185 58l49 98q71 -51 136 -51q39 0 66.5 19t27.5 55q0 25 -16.5 45t-43.5 32t-52.5 19.5t-53.5 11.5l78 158q-251 41 -399 207t-148 428z" />
<glyph unicode="&#xc8;" horiz-adv-x="1124" d="M76 0l262 1483h846l-31 -176h-651l-80 -451h565l-31 -170h-565l-90 -510h670l-31 -176h-864zM621 1964h204l99 -336h-138z" />
<glyph unicode="&#xc9;" horiz-adv-x="1124" d="M76 0l262 1483h846l-31 -176h-651l-80 -451h565l-31 -170h-565l-90 -510h670l-31 -176h-864zM655 1628l217 336h209l-284 -336h-142z" />
<glyph unicode="&#xca;" horiz-adv-x="1124" d="M76 0l262 1483h846l-31 -176h-651l-80 -451h565l-31 -170h-565l-90 -510h670l-31 -176h-864zM459 1628l291 336h196l172 -336h-145l-144 215l-208 -215h-162z" />
<glyph unicode="&#xcb;" horiz-adv-x="1124" d="M76 0l262 1483h846l-31 -176h-651l-80 -451h565l-31 -170h-565l-90 -510h670l-31 -176h-864zM506 1790q0 51 36 85t87 34q45 0 76.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77 31.5t-31 75.5zM903 1790q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5 q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="524" d="M76 0l262 1483h194l-262 -1483h-194zM293 1964h205l98 -336h-137z" />
<glyph unicode="&#xcd;" horiz-adv-x="524" d="M76 0l262 1483h194l-262 -1483h-194zM328 1628l217 336h209l-285 -336h-141z" />
<glyph unicode="&#xce;" horiz-adv-x="524" d="M76 0l262 1483h194l-262 -1483h-194zM131 1628l291 336h196l173 -336h-146l-143 215l-209 -215h-162z" />
<glyph unicode="&#xcf;" horiz-adv-x="524" d="M76 0l262 1483h194l-262 -1483h-194zM178 1790q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5zM575 1790q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1507" d="M47 676l29 160h166l114 647h363q346 0 545 -180q100 -90 153 -213.5t53 -270.5q0 -155 -59 -307.5t-176 -269.5q-245 -242 -662 -242h-479l119 676h-166zM319 176h269q338 0 520 197q80 86 124 201t44 231q0 104 -37.5 198.5t-110.5 161.5q-155 142 -434 142h-174 l-84 -471h328l-29 -160h-327z" />
<glyph unicode="&#xd1;" horiz-adv-x="1560" d="M76 0l262 1483h178l430 -776q94 -167 225 -416l5 2q46 292 75 457l129 733h189l-262 -1483h-179l-430 776q-94 167 -225 416l-4 -2q-38 -245 -76 -457l-129 -733h-188zM674 1673l31 174q69 76 169 76q60 0 164 -53q84 -43 140 -43q87 0 163 100l5 -2l-31 -174 q-69 -76 -170 -76q-58 0 -164 54q-84 43 -139 43q-88 0 -164 -101z" />
<glyph unicode="&#xd2;" horiz-adv-x="1634" d="M127 664q0 154 52 302t149 265q106 129 255.5 201.5t327.5 72.5q190 0 343.5 -83t244 -240.5t90.5 -362.5q0 -154 -51.5 -302t-148.5 -265q-106 -129 -256 -202t-328 -73q-142 0 -266 49t-215.5 136.5t-144 217.5t-52.5 284zM322 678q0 -223 135 -371.5t356 -148.5 q128 0 237 52.5t189 151.5q73 90 114.5 206.5t41.5 236.5q0 223 -135.5 371.5t-356.5 148.5q-128 0 -237 -53t-189 -152q-73 -90 -114 -206t-41 -236zM848 1964h205l98 -336h-137z" />
<glyph unicode="&#xd3;" horiz-adv-x="1634" d="M127 664q0 154 52 302t149 265q106 129 255.5 201.5t327.5 72.5q190 0 343.5 -83t244 -240.5t90.5 -362.5q0 -154 -51.5 -302t-148.5 -265q-106 -129 -256 -202t-328 -73q-142 0 -266 49t-215.5 136.5t-144 217.5t-52.5 284zM322 678q0 -223 135 -371.5t356 -148.5 q128 0 237 52.5t189 151.5q73 90 114.5 206.5t41.5 236.5q0 223 -135.5 371.5t-356.5 148.5q-128 0 -237 -53t-189 -152q-73 -90 -114 -206t-41 -236zM883 1628l217 336h209l-285 -336h-141z" />
<glyph unicode="&#xd4;" horiz-adv-x="1634" d="M127 664q0 154 52 302t149 265q106 129 255.5 201.5t327.5 72.5q190 0 343.5 -83t244 -240.5t90.5 -362.5q0 -154 -51.5 -302t-148.5 -265q-106 -129 -256 -202t-328 -73q-142 0 -266 49t-215.5 136.5t-144 217.5t-52.5 284zM322 678q0 -223 135 -371.5t356 -148.5 q128 0 237 52.5t189 151.5q73 90 114.5 206.5t41.5 236.5q0 223 -135.5 371.5t-356.5 148.5q-128 0 -237 -53t-189 -152q-73 -90 -114 -206t-41 -236zM686 1628l291 336h197l172 -336h-146l-143 215l-209 -215h-162z" />
<glyph unicode="&#xd5;" horiz-adv-x="1634" d="M127 664q0 154 52 302t149 265q106 129 255.5 201.5t327.5 72.5q190 0 343.5 -83t244 -240.5t90.5 -362.5q0 -154 -51.5 -302t-148.5 -265q-106 -129 -256 -202t-328 -73q-142 0 -266 49t-215.5 136.5t-144 217.5t-52.5 284zM322 678q0 -223 135 -371.5t356 -148.5 q128 0 237 52.5t189 151.5q73 90 114.5 206.5t41.5 236.5q0 223 -135.5 371.5t-356.5 148.5q-128 0 -237 -53t-189 -152q-73 -90 -114 -206t-41 -236zM711 1673l30 174q69 76 170 76q60 0 164 -53q84 -43 139 -43q88 0 164 100l4 -2l-30 -174q-69 -76 -170 -76 q-58 0 -164 54q-84 43 -139 43q-88 0 -164 -101z" />
<glyph unicode="&#xd6;" horiz-adv-x="1634" d="M127 664q0 154 52 302t149 265q106 129 255.5 201.5t327.5 72.5q190 0 343.5 -83t244 -240.5t90.5 -362.5q0 -154 -51.5 -302t-148.5 -265q-106 -129 -256 -202t-328 -73q-142 0 -266 49t-215.5 136.5t-144 217.5t-52.5 284zM322 678q0 -223 135 -371.5t356 -148.5 q128 0 237 52.5t189 151.5q73 90 114.5 206.5t41.5 236.5q0 223 -135.5 371.5t-356.5 148.5q-128 0 -237 -53t-189 -152q-73 -90 -114 -206t-41 -236zM733 1790q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5zM1130 1790 q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5z" />
<glyph unicode="&#xd7;" d="M160 422l372 321l-258 322l119 106l260 -323l373 323l88 -110l-373 -322l258 -321l-118 -107l-260 324l-373 -324z" />
<glyph unicode="&#xd8;" horiz-adv-x="1634" d="M70 61l186 183q-129 175 -129 420q0 154 52 302t149 265q106 129 255.5 201.5t327.5 72.5q275 0 461 -168l180 176l95 -92l-187 -182q129 -175 129 -420q0 -154 -51.5 -302t-148.5 -265q-106 -129 -256 -202t-328 -73q-275 0 -461 168l-180 -176zM322 678q0 -169 75 -295 l838 819q-133 123 -332 123q-128 0 -237 -53t-189 -152q-73 -90 -114 -206t-41 -236zM481 281q133 -123 332 -123q128 0 237 52.5t189 151.5q73 90 114.5 206.5t41.5 236.5q0 168 -76 295z" />
<glyph unicode="&#xd9;" horiz-adv-x="1470" d="M158 465q0 86 26 246l135 772h195l-135 -772q-23 -129 -23 -217q0 -161 83.5 -248.5t256.5 -87.5q224 0 340 151q87 114 133 379l140 795h188l-141 -807q-32 -180 -73.5 -290.5t-104.5 -188.5q-178 -220 -496 -220q-246 0 -385 130.5t-139 357.5zM770 1964h205l98 -336 h-137z" />
<glyph unicode="&#xda;" horiz-adv-x="1470" d="M158 465q0 86 26 246l135 772h195l-135 -772q-23 -129 -23 -217q0 -161 83.5 -248.5t256.5 -87.5q224 0 340 151q87 114 133 379l140 795h188l-141 -807q-32 -180 -73.5 -290.5t-104.5 -188.5q-178 -220 -496 -220q-246 0 -385 130.5t-139 357.5zM805 1628l217 336h209 l-285 -336h-141z" />
<glyph unicode="&#xdb;" horiz-adv-x="1470" d="M158 465q0 86 26 246l135 772h195l-135 -772q-23 -129 -23 -217q0 -161 83.5 -248.5t256.5 -87.5q224 0 340 151q87 114 133 379l140 795h188l-141 -807q-32 -180 -73.5 -290.5t-104.5 -188.5q-178 -220 -496 -220q-246 0 -385 130.5t-139 357.5zM608 1628l291 336h197 l172 -336h-146l-143 215l-209 -215h-162z" />
<glyph unicode="&#xdc;" horiz-adv-x="1470" d="M158 465q0 86 26 246l135 772h195l-135 -772q-23 -129 -23 -217q0 -161 83.5 -248.5t256.5 -87.5q224 0 340 151q87 114 133 379l140 795h188l-141 -807q-32 -180 -73.5 -290.5t-104.5 -188.5q-178 -220 -496 -220q-246 0 -385 130.5t-139 357.5zM655 1790q0 51 36 85 t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5zM1053 1790q0 51 36 85t87 34q46 0 77 -31.5t31 -75.5q0 -51 -36 -85t-87 -34q-45 0 -76.5 31.5t-31.5 75.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1325" d="M180 1483h217l166 -330q82 -160 156 -315h4q143 177 262 315l281 330h225l-698 -811l-119 -672h-195l119 670zM733 1628l217 336h209l-285 -336h-141z" />
<glyph unicode="&#xde;" horiz-adv-x="1257" d="M76 0l262 1483h194l-51 -289h199q285 0 420 -121q129 -113 129 -289q0 -93 -39.5 -184.5t-116.5 -161.5q-156 -141 -461 -141h-288l-54 -297h-194zM354 467h283q214 0 309 92q88 85 88 209q0 108 -76 172q-90 78 -282 78h-225z" />
<glyph unicode="&#xdf;" horiz-adv-x="1284" d="M74 0l209 1182q40 222 168 317q120 88 329 88q214 0 314 -90q88 -78 88 -221q0 -161 -115 -336q-50 4 -92 4q-41 0 -79 -5.5t-80 -20t-67.5 -46t-25.5 -77.5q0 -25 9.5 -45.5t21 -35t42 -33.5t51 -30t69.5 -35l12 -6q52 -26 82.5 -43.5t67.5 -45.5t56.5 -55.5t33.5 -67 t14 -87.5q0 -58 -27 -122.5t-80 -112.5q-111 -99 -307 -99q-185 0 -352 95l33 182q168 -113 337 -113q106 0 160 45q22 17 35.5 45.5t13.5 57.5q0 16 -2.5 29.5t-10 26t-13.5 21.5t-20.5 20t-23.5 18t-30.5 18t-33.5 17l-39 19.5t-42 20.5q-132 66 -191 129.5t-59 165.5 q0 81 36 145t97 103.5t137 60t161 21.5q34 81 34 149q0 77 -47 119q-53 47 -180 47q-137 0 -207 -55q-72 -57 -96 -193l-207 -1171h-184z" />
<glyph unicode="&#xe0;" d="M90 410q0 119 45.5 238.5t132.5 209.5q85 88 195 130t227 42q175 0 412 -110l-162 -920h-184l16 88l-4 2q-138 -113 -287 -113q-167 0 -279 120t-112 313zM274 424q0 -132 69.5 -208.5t174.5 -76.5q149 0 289 146l92 528q-108 53 -227 53q-173 0 -283 -127 q-57 -65 -86 -148.5t-29 -166.5zM549 1489h205l98 -336h-137z" />
<glyph unicode="&#xe1;" d="M90 410q0 119 45.5 238.5t132.5 209.5q85 88 195 130t227 42q175 0 412 -110l-162 -920h-184l16 88l-4 2q-138 -113 -287 -113q-167 0 -279 120t-112 313zM274 424q0 -132 69.5 -208.5t174.5 -76.5q149 0 289 146l92 528q-108 53 -227 53q-173 0 -283 -127 q-57 -65 -86 -148.5t-29 -166.5zM584 1153l217 336h209l-285 -336h-141z" />
<glyph unicode="&#xe2;" d="M90 410q0 119 45.5 238.5t132.5 209.5q85 88 195 130t227 42q175 0 412 -110l-162 -920h-184l16 88l-4 2q-138 -113 -287 -113q-167 0 -279 120t-112 313zM274 424q0 -132 69.5 -208.5t174.5 -76.5q149 0 289 146l92 528q-108 53 -227 53q-173 0 -283 -127 q-57 -65 -86 -148.5t-29 -166.5zM387 1153l291 336h196l173 -336h-146l-143 215l-209 -215h-162z" />
<glyph unicode="&#xe3;" d="M90 410q0 119 45.5 238.5t132.5 209.5q85 88 195 130t227 42q175 0 412 -110l-162 -920h-184l16 88l-4 2q-138 -113 -287 -113q-167 0 -279 120t-112 313zM274 424q0 -132 69.5 -208.5t174.5 -76.5q149 0 289 146l92 528q-108 53 -227 53q-173 0 -283 -127 q-57 -65 -86 -148.5t-29 -166.5zM412 1198l30 174q69 76 170 76q60 0 164 -53q84 -43 139 -43q88 0 164 100l4 -2l-30 -174q-69 -76 -170 -76q-60 0 -164 53q-84 43 -139 43q-88 0 -164 -100z" />
<glyph unicode="&#xe4;" d="M90 410q0 119 45.5 238.5t132.5 209.5q85 88 195 130t227 42q175 0 412 -110l-162 -920h-184l16 88l-4 2q-138 -113 -287 -113q-167 0 -279 120t-112 313zM274 424q0 -132 69.5 -208.5t174.5 -76.5q149 0 289 146l92 528q-108 53 -227 53q-173 0 -283 -127 q-57 -65 -86 -148.5t-29 -166.5zM434 1315q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5zM831 1315q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5z" />
<glyph unicode="&#xe5;" d="M90 410q0 119 45.5 238.5t132.5 209.5q85 88 195 130t227 42q175 0 412 -110l-162 -920h-184l16 88l-4 2q-138 -113 -287 -113q-167 0 -279 120t-112 313zM274 424q0 -132 69.5 -208.5t174.5 -76.5q149 0 289 146l92 528q-108 53 -227 53q-173 0 -283 -127 q-57 -65 -86 -148.5t-29 -166.5zM518 1341q0 101 72.5 177.5t177.5 76.5q95 0 159 -59.5t64 -155.5q0 -101 -72.5 -176.5t-177.5 -75.5q-94 0 -158.5 59t-64.5 154zM627 1348q0 -47 30.5 -82t87.5 -35q56 0 97 41t41 102q0 49 -30.5 84t-88.5 35q-55 0 -96 -41.5t-41 -103.5 z" />
<glyph unicode="&#xe6;" horiz-adv-x="1804" d="M90 416q0 116 44 233.5t132 206.5q171 174 465 174q145 0 309 -41l-16 -86l2 -2q123 129 305 129q170 0 282.5 -112.5t112.5 -292.5q0 -81 -22 -174h-739q0 -147 89.5 -227.5t231.5 -80.5q157 0 309 86l-34 -192q-123 -60 -287 -60q-125 0 -231.5 46t-172.5 133 q-144 -179 -364 -179q-183 0 -299.5 122t-116.5 317zM274 432q0 -131 72.5 -211t190.5 -80q159 0 264 154q-17 70 -17 135q0 49 11 113l55 311q-72 14 -141 14q-205 0 -320 -127q-53 -59 -84 -141.5t-31 -167.5zM987 596h567v18q0 109 -61 179.5t-180 70.5q-118 0 -203 -73 t-123 -195z" />
<glyph unicode="&#xe7;" horiz-adv-x="1021" d="M90 424q0 123 50.5 247t152.5 214q167 145 395 145q153 0 289 -63l-31 -176q-142 71 -264 71q-157 0 -268 -100q-64 -59 -102 -146t-38 -178q0 -133 83 -213t231 -80q142 0 278 68l-32 -190q-123 -46 -265 -46h-6l-35 -71q148 -48 148 -170q0 -83 -60.5 -137t-154.5 -54 q-100 0 -185 58l50 98q71 -51 135 -51q39 0 66.5 19t27.5 55q0 25 -16.5 45t-43.5 32t-52.5 19.5t-53.5 11.5l78 154q-166 28 -271.5 141t-105.5 297z" />
<glyph unicode="&#xe8;" horiz-adv-x="1126" d="M90 438q0 111 37.5 218t105.5 188q80 95 187 140.5t217 45.5q182 0 297 -111t115 -294q0 -90 -23 -174h-754q0 -147 90.5 -227.5t235.5 -80.5q164 0 320 86l-35 -192q-126 -60 -297 -60q-103 0 -192.5 29t-157.5 85t-107 145t-39 202zM295 596h582v20q0 111 -64.5 179.5 t-183.5 68.5t-207.5 -70.5t-126.5 -197.5zM518 1489h205l98 -336h-137z" />
<glyph unicode="&#xe9;" horiz-adv-x="1126" d="M90 438q0 111 37.5 218t105.5 188q80 95 187 140.5t217 45.5q182 0 297 -111t115 -294q0 -90 -23 -174h-754q0 -147 90.5 -227.5t235.5 -80.5q164 0 320 86l-35 -192q-126 -60 -297 -60q-103 0 -192.5 29t-157.5 85t-107 145t-39 202zM295 596h582v20q0 111 -64.5 179.5 t-183.5 68.5t-207.5 -70.5t-126.5 -197.5zM553 1153l217 336h209l-285 -336h-141z" />
<glyph unicode="&#xea;" horiz-adv-x="1126" d="M90 438q0 111 37.5 218t105.5 188q80 95 187 140.5t217 45.5q182 0 297 -111t115 -294q0 -90 -23 -174h-754q0 -147 90.5 -227.5t235.5 -80.5q164 0 320 86l-35 -192q-126 -60 -297 -60q-103 0 -192.5 29t-157.5 85t-107 145t-39 202zM295 596h582v20q0 111 -64.5 179.5 t-183.5 68.5t-207.5 -70.5t-126.5 -197.5zM356 1153l291 336h197l172 -336h-146l-143 215l-209 -215h-162z" />
<glyph unicode="&#xeb;" horiz-adv-x="1126" d="M90 438q0 111 37.5 218t105.5 188q80 95 187 140.5t217 45.5q182 0 297 -111t115 -294q0 -90 -23 -174h-754q0 -147 90.5 -227.5t235.5 -80.5q164 0 320 86l-35 -192q-126 -60 -297 -60q-103 0 -192.5 29t-157.5 85t-107 145t-39 202zM295 596h582v20q0 111 -64.5 179.5 t-183.5 68.5t-207.5 -70.5t-126.5 -197.5zM403 1315q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5zM801 1315q0 51 36 85t87 34q46 0 77 -31t31 -76q0 -51 -36 -85t-87 -34q-45 0 -76.5 31t-31.5 76z" />
<glyph unicode="&#xec;" horiz-adv-x="509" d="M74 0l178 1008h184l-178 -1008h-184zM201 1489h205l98 -336h-137z" />
<glyph unicode="&#xed;" horiz-adv-x="509" d="M74 0l178 1008h184l-178 -1008h-184zM236 1153l217 336h209l-285 -336h-141z" />
<glyph unicode="&#xee;" horiz-adv-x="509" d="M39 1153l291 336h196l172 -336h-145l-143 215l-209 -215h-162zM74 0l178 1008h184l-178 -1008h-184z" />
<glyph unicode="&#xef;" horiz-adv-x="509" d="M74 0l178 1008h184l-178 -1008h-184zM86 1315q0 51 36 85t87 34q45 0 76.5 -31t31.5 -76q0 -51 -35.5 -85t-86.5 -34q-46 0 -77.5 31.5t-31.5 75.5zM483 1315q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1245" d="M104 432q0 116 47.5 228.5t135.5 193.5q69 63 158.5 99t183.5 36q86 0 165 -31t124 -90l4 2q-45 174 -160 322l-332 -88v131l238 63q-80 77 -183 142l131 110q134 -92 234 -204l221 59v-131l-133 -37q197 -279 197 -612q0 -307 -162 -480q-73 -78 -175 -123t-220 -45 q-203 0 -338.5 127.5t-135.5 327.5zM289 453q0 -133 79.5 -220.5t219.5 -87.5q133 0 231 91q58 55 90.5 130.5t32.5 159.5q-2 136 -83 219.5t-220 83.5q-134 0 -227 -86q-60 -56 -91.5 -132t-31.5 -158z" />
<glyph unicode="&#xf1;" horiz-adv-x="1212" d="M74 0l178 1008h184l-22 -123l4 -2q183 147 362 147q136 0 218.5 -82.5t82.5 -226.5q0 -53 -16 -141l-102 -580h-185l101 567q14 77 14 121q0 170 -162 170q-150 0 -350 -158l-123 -700h-184zM416 1198l30 174q69 76 170 76q60 0 164 -53q84 -43 140 -43q87 0 163 100 l4 -2l-30 -174q-69 -76 -170 -76q-60 0 -164 53q-84 43 -139 43q-88 0 -164 -100z" />
<glyph unicode="&#xf2;" horiz-adv-x="1212" d="M90 442q0 115 43 227t123 197q158 164 391 164q208 0 341.5 -128t133.5 -337q0 -115 -43 -227t-123 -197q-158 -164 -391 -164q-208 0 -341.5 128t-133.5 337zM274 455q0 -135 81 -222.5t223 -87.5q148 0 245 105q54 57 84.5 138t30.5 165q0 135 -80.5 222t-222.5 87 q-150 0 -246 -104q-54 -57 -84.5 -138t-30.5 -165zM553 1489h205l98 -336h-137z" />
<glyph unicode="&#xf3;" horiz-adv-x="1212" d="M90 442q0 115 43 227t123 197q158 164 391 164q208 0 341.5 -128t133.5 -337q0 -115 -43 -227t-123 -197q-158 -164 -391 -164q-208 0 -341.5 128t-133.5 337zM274 455q0 -135 81 -222.5t223 -87.5q148 0 245 105q54 57 84.5 138t30.5 165q0 135 -80.5 222t-222.5 87 q-150 0 -246 -104q-54 -57 -84.5 -138t-30.5 -165zM588 1153l217 336h209l-285 -336h-141z" />
<glyph unicode="&#xf4;" horiz-adv-x="1212" d="M90 442q0 115 43 227t123 197q158 164 391 164q208 0 341.5 -128t133.5 -337q0 -115 -43 -227t-123 -197q-158 -164 -391 -164q-208 0 -341.5 128t-133.5 337zM274 455q0 -135 81 -222.5t223 -87.5q148 0 245 105q54 57 84.5 138t30.5 165q0 135 -80.5 222t-222.5 87 q-150 0 -246 -104q-54 -57 -84.5 -138t-30.5 -165zM391 1153l291 336h197l172 -336h-146l-143 215l-209 -215h-162z" />
<glyph unicode="&#xf5;" horiz-adv-x="1212" d="M90 442q0 115 43 227t123 197q158 164 391 164q208 0 341.5 -128t133.5 -337q0 -115 -43 -227t-123 -197q-158 -164 -391 -164q-208 0 -341.5 128t-133.5 337zM274 455q0 -135 81 -222.5t223 -87.5q148 0 245 105q54 57 84.5 138t30.5 165q0 135 -80.5 222t-222.5 87 q-150 0 -246 -104q-54 -57 -84.5 -138t-30.5 -165zM416 1198l30 174q69 76 170 76q60 0 164 -53q84 -43 140 -43q87 0 163 100l4 -2l-30 -174q-69 -76 -170 -76q-60 0 -164 53q-84 43 -139 43q-88 0 -164 -100z" />
<glyph unicode="&#xf6;" horiz-adv-x="1212" d="M90 442q0 115 43 227t123 197q158 164 391 164q208 0 341.5 -128t133.5 -337q0 -115 -43 -227t-123 -197q-158 -164 -391 -164q-208 0 -341.5 128t-133.5 337zM274 455q0 -135 81 -222.5t223 -87.5q148 0 245 105q54 57 84.5 138t30.5 165q0 135 -80.5 222t-222.5 87 q-150 0 -246 -104q-54 -57 -84.5 -138t-30.5 -165zM438 1315q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5zM836 1315q0 51 35.5 85t86.5 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77 31t-31 76z " />
<glyph unicode="&#xf7;" d="M139 668l27 147h971l-27 -147h-971zM444 297q0 51 36 86t87 35q47 0 79 -32t32 -77q0 -51 -37 -86t-88 -35q-47 0 -78 32t-31 77zM598 1174q0 51 36 85.5t87 34.5q47 0 78.5 -31.5t31.5 -76.5q0 -51 -36.5 -86t-87.5 -35q-47 0 -78 32t-31 77z" />
<glyph unicode="&#xf8;" horiz-adv-x="1212" d="M33 66l129 118q-72 115 -72 258q0 115 43 227t123 197q158 164 391 164q190 0 320 -108l123 112l90 -92l-129 -119q71 -114 71 -258q0 -115 -43 -227t-123 -197q-158 -164 -391 -164q-188 0 -319 109l-123 -113zM274 455q0 -76 29 -142l528 486q-79 63 -196 63 q-150 0 -246 -104q-54 -57 -84.5 -138t-30.5 -165zM381 209q80 -64 197 -64q148 0 245 105q54 57 84.5 138t30.5 165q0 75 -29 141z" />
<glyph unicode="&#xf9;" horiz-adv-x="1200" d="M121 279q0 61 16 159l101 570h184l-98 -557q-15 -92 -15 -140q0 -77 40.5 -120.5t125.5 -43.5q159 0 344 164l123 697h184l-178 -1008h-184l20 117l-4 2q-189 -142 -348 -142q-134 0 -222.5 81.5t-88.5 220.5zM547 1489h205l98 -336h-137z" />
<glyph unicode="&#xfa;" horiz-adv-x="1200" d="M121 279q0 61 16 159l101 570h184l-98 -557q-15 -92 -15 -140q0 -77 40.5 -120.5t125.5 -43.5q159 0 344 164l123 697h184l-178 -1008h-184l20 117l-4 2q-189 -142 -348 -142q-134 0 -222.5 81.5t-88.5 220.5zM582 1153l217 336h209l-285 -336h-141z" />
<glyph unicode="&#xfb;" horiz-adv-x="1200" d="M121 279q0 61 16 159l101 570h184l-98 -557q-15 -92 -15 -140q0 -77 40.5 -120.5t125.5 -43.5q159 0 344 164l123 697h184l-178 -1008h-184l20 117l-4 2q-189 -142 -348 -142q-134 0 -222.5 81.5t-88.5 220.5zM385 1153l291 336h196l172 -336h-145l-143 215l-209 -215 h-162z" />
<glyph unicode="&#xfc;" horiz-adv-x="1200" d="M121 279q0 61 16 159l101 570h184l-98 -557q-15 -92 -15 -140q0 -77 40.5 -120.5t125.5 -43.5q159 0 344 164l123 697h184l-178 -1008h-184l20 117l-4 2q-189 -142 -348 -142q-134 0 -222.5 81.5t-88.5 220.5zM432 1315q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5 q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5zM829 1315q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1110" d="M18 -451l375 566l-264 893h199l102 -355q46 -158 92 -338h4q140 229 213 342l224 351h200l-938 -1459h-207zM539 1153l217 336h209l-285 -336h-141z" />
<glyph unicode="&#xfe;" horiz-adv-x="1183" d="M-6 -451l356 2016h185l-111 -617l4 -2q120 84 258 84q177 0 292.5 -123.5t115.5 -322.5q0 -119 -43.5 -235.5t-126.5 -201.5q-82 -84 -196.5 -127t-246.5 -43q-106 0 -217 35l-4 -2l-82 -461h-184zM289 180q105 -41 209 -41q186 0 299 123q54 59 83 140t29 167 q0 136 -71.5 215.5t-188.5 79.5q-135 0 -256 -90z" />
<glyph unicode="&#xff;" horiz-adv-x="1110" d="M18 -451l375 566l-264 893h199l102 -355q46 -158 92 -338h4q140 229 213 342l224 351h200l-938 -1459h-207zM389 1315q0 51 36 85t87 34q46 0 77.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5zM786 1315q0 51 36 85t87 34q46 0 77.5 -31.5 t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77.5 31.5t-31.5 75.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1843" d="M127 664q0 157 59.5 308.5t178.5 268.5q116 116 275 179t347 63h916l-31 -176h-610l-80 -451h524l-31 -170h-524l-90 -510h629l-31 -176h-791q-336 0 -538.5 182t-202.5 482zM322 676q0 -217 143.5 -357t400.5 -143l201 1131h-94q-303 0 -484 -197q-80 -88 -123.5 -201 t-43.5 -233z" />
<glyph unicode="&#x153;" horiz-adv-x="1921" d="M90 440q0 105 35.5 212t103.5 190q72 88 174 138t226 50q142 0 246.5 -64t146.5 -163h4q66 105 173.5 166t238.5 61q180 0 292.5 -111.5t112.5 -295.5q0 -92 -22 -172h-740q0 -148 89.5 -228t232.5 -80q157 0 309 86l-35 -192q-123 -60 -284 -60q-140 0 -254 56.5 t-174 163.5h-4q-73 -110 -181 -165t-233 -55q-200 0 -328.5 127t-128.5 336zM274 457q0 -135 77 -223.5t210 -88.5q164 0 260 131q86 119 86 275q0 132 -74 221.5t-210 89.5q-165 0 -263 -131q-86 -119 -86 -274zM1104 596h567v18q0 109 -61 179.5t-180 70.5 q-118 0 -203 -73t-123 -195z" />
<glyph unicode="&#x178;" horiz-adv-x="1325" d="M180 1483h217l166 -330q82 -160 156 -315h4q143 177 262 315l281 330h225l-698 -811l-119 -672h-195l119 670zM584 1790q0 51 36 85t87 34q45 0 76.5 -31.5t31.5 -75.5q0 -51 -36 -85t-87 -34q-46 0 -77 31.5t-31 75.5zM981 1790q0 51 36 85t87 34q46 0 77 -31.5 t31 -75.5q0 -51 -35.5 -85t-86.5 -34q-46 0 -77.5 31.5t-31.5 75.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1015" d="M293 1153l291 336h196l172 -336h-145l-143 215l-209 -215h-162z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1015" d="M317 1198l31 174q69 76 170 76q60 0 164 -53q84 -43 139 -43q88 0 164 100l4 -2l-31 -174q-69 -76 -170 -76q-59 0 -163 53q-84 43 -140 43q-87 0 -163 -100z" />
<glyph unicode="&#x2000;" horiz-adv-x="1035" />
<glyph unicode="&#x2001;" horiz-adv-x="2071" />
<glyph unicode="&#x2002;" horiz-adv-x="1035" />
<glyph unicode="&#x2003;" horiz-adv-x="2071" />
<glyph unicode="&#x2004;" horiz-adv-x="690" />
<glyph unicode="&#x2005;" horiz-adv-x="517" />
<glyph unicode="&#x2006;" horiz-adv-x="345" />
<glyph unicode="&#x2007;" horiz-adv-x="345" />
<glyph unicode="&#x2008;" horiz-adv-x="258" />
<glyph unicode="&#x2009;" horiz-adv-x="414" />
<glyph unicode="&#x200a;" horiz-adv-x="115" />
<glyph unicode="&#x2010;" horiz-adv-x="708" d="M113 485l28 164h477l-28 -164h-477z" />
<glyph unicode="&#x2011;" horiz-adv-x="708" d="M113 485l28 164h477l-28 -164h-477z" />
<glyph unicode="&#x2012;" horiz-adv-x="708" d="M113 485l28 164h477l-28 -164h-477z" />
<glyph unicode="&#x2013;" horiz-adv-x="1001" d="M104 494l27 147h787l-27 -147h-787z" />
<glyph unicode="&#x2014;" horiz-adv-x="1587" d="M76 504l22 127h1434l-23 -127h-1433z" />
<glyph unicode="&#x2018;" horiz-adv-x="546" d="M193 969l266 551h125l-209 -551h-182z" />
<glyph unicode="&#x2019;" horiz-adv-x="546" d="M221 969l209 551h182l-266 -551h-125z" />
<glyph unicode="&#x201a;" horiz-adv-x="546" d="M0 -215l209 551h182l-266 -551h-125z" />
<glyph unicode="&#x201c;" horiz-adv-x="876" d="M193 969l266 551h125l-209 -551h-182zM522 969l266 551h125l-208 -551h-183z" />
<glyph unicode="&#x201d;" horiz-adv-x="876" d="M221 969l209 551h182l-266 -551h-125zM551 969l209 551h182l-266 -551h-125z" />
<glyph unicode="&#x201e;" horiz-adv-x="876" d="M0 -215l209 551h182l-266 -551h-125zM330 -215l209 551h182l-266 -551h-125z" />
<glyph unicode="&#x2022;" horiz-adv-x="778" d="M184 608q0 92 66 157.5t158 65.5t157.5 -65.5t65.5 -157.5t-65.5 -157.5t-157.5 -65.5t-158 65.5t-66 157.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1658" d="M92 106q0 58 42.5 100t101.5 42q56 0 92.5 -37.5t36.5 -91.5q0 -58 -42.5 -100t-101.5 -42q-56 0 -92.5 37.5t-36.5 91.5zM623 106q0 58 42 100t101 42q56 0 92.5 -37.5t36.5 -91.5q0 -58 -42 -100t-101 -42q-56 0 -92.5 37.5t-36.5 91.5zM1153 106q0 58 42 100t101 42 q56 0 92.5 -37.5t36.5 -91.5q0 -58 -42 -100t-101 -42q-56 0 -92.5 37.5t-36.5 91.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="414" />
<glyph unicode="&#x2039;" horiz-adv-x="698" d="M92 530l404 422h170l-416 -436l258 -461h-152z" />
<glyph unicode="&#x203a;" horiz-adv-x="698" d="M33 55l416 437l-259 460h152l264 -475l-403 -422h-170z" />
<glyph unicode="&#x205f;" horiz-adv-x="517" />
<glyph unicode="&#x20ac;" horiz-adv-x="1210" d="M23 504l26 149h160q3 95 27 189h-154l27 149h174q97 232 289 373t442 141q146 0 276 -47l-35 -194q-125 61 -256 61q-160 0 -294 -88t-216 -246h408l-27 -149h-438q-29 -114 -29 -189h435l-27 -149h-395q35 -164 152.5 -255t295.5 -91q129 0 230 37l-35 -195 q-94 -23 -209 -23q-262 0 -431.5 142t-203.5 385h-192z" />
<glyph unicode="&#x2122;" horiz-adv-x="1298" d="M156 1384v99h467v-99h-181v-495h-106v495h-180zM715 889v594h100l193 -266l192 266h103v-594h-107v266q0 66 4 152l-4 2q-70 -104 -94 -138l-94 -131l-95 131q-24 34 -94 138l-4 -2q4 -86 4 -152v-266h-104z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x2a;" u2="&#x2026;" k="244" />
<hkern u1="&#x2a;" u2="&#x2e;" k="244" />
<hkern u1="&#x2a;" u2="&#x2c;" k="244" />
<hkern u1="&#x2c;" u2="V" k="281" />
<hkern u1="&#x2c;" u2="&#x39;" k="82" />
<hkern u1="&#x2c;" u2="&#x38;" k="66" />
<hkern u1="&#x2c;" u2="&#x37;" k="45" />
<hkern u1="&#x2c;" u2="&#x2a;" k="244" />
<hkern u1="&#x2d;" u2="x" k="129" />
<hkern u1="&#x2d;" u2="X" k="55" />
<hkern u1="&#x2d;" u2="V" k="111" />
<hkern u1="&#x2d;" u2="&#x32;" k="113" />
<hkern u1="&#x2d;" u2="&#x31;" k="82" />
<hkern u1="&#x2e;" u2="V" k="281" />
<hkern u1="&#x2e;" u2="&#x39;" k="82" />
<hkern u1="&#x2e;" u2="&#x38;" k="66" />
<hkern u1="&#x2e;" u2="&#x37;" k="45" />
<hkern u1="&#x2e;" u2="&#x2a;" k="244" />
<hkern u1="&#x2f;" u2="&#x153;" k="121" />
<hkern u1="&#x2f;" u2="&#xf8;" k="121" />
<hkern u1="&#x2f;" u2="&#xf6;" k="121" />
<hkern u1="&#x2f;" u2="&#xf5;" k="121" />
<hkern u1="&#x2f;" u2="&#xf4;" k="121" />
<hkern u1="&#x2f;" u2="&#xf3;" k="121" />
<hkern u1="&#x2f;" u2="&#xf2;" k="121" />
<hkern u1="&#x2f;" u2="&#xf0;" k="121" />
<hkern u1="&#x2f;" u2="&#xeb;" k="121" />
<hkern u1="&#x2f;" u2="&#xea;" k="121" />
<hkern u1="&#x2f;" u2="&#xe9;" k="121" />
<hkern u1="&#x2f;" u2="&#xe8;" k="121" />
<hkern u1="&#x2f;" u2="&#xe7;" k="121" />
<hkern u1="&#x2f;" u2="&#xe6;" k="121" />
<hkern u1="&#x2f;" u2="&#xe5;" k="121" />
<hkern u1="&#x2f;" u2="&#xe4;" k="121" />
<hkern u1="&#x2f;" u2="&#xe3;" k="121" />
<hkern u1="&#x2f;" u2="&#xe2;" k="121" />
<hkern u1="&#x2f;" u2="&#xe1;" k="121" />
<hkern u1="&#x2f;" u2="&#xe0;" k="121" />
<hkern u1="&#x2f;" u2="&#xc5;" k="215" />
<hkern u1="&#x2f;" u2="&#xc4;" k="215" />
<hkern u1="&#x2f;" u2="&#xc3;" k="215" />
<hkern u1="&#x2f;" u2="&#xc2;" k="215" />
<hkern u1="&#x2f;" u2="&#xc1;" k="215" />
<hkern u1="&#x2f;" u2="&#xc0;" k="215" />
<hkern u1="&#x2f;" u2="q" k="121" />
<hkern u1="&#x2f;" u2="o" k="121" />
<hkern u1="&#x2f;" u2="g" k="121" />
<hkern u1="&#x2f;" u2="e" k="121" />
<hkern u1="&#x2f;" u2="d" k="121" />
<hkern u1="&#x2f;" u2="c" k="121" />
<hkern u1="&#x2f;" u2="a" k="121" />
<hkern u1="&#x2f;" u2="A" k="215" />
<hkern u1="&#x2f;" u2="&#x34;" k="178" />
<hkern u1="&#x30;" u2="_" k="92" />
<hkern u1="&#x36;" u2="&#x37;" k="18" />
<hkern u1="&#x36;" u2="&#x31;" k="66" />
<hkern u1="&#x37;" u2="&#x2026;" k="225" />
<hkern u1="&#x37;" u2="&#x34;" k="150" />
<hkern u1="&#x37;" u2="&#x2f;" k="225" />
<hkern u1="&#x37;" u2="&#x2e;" k="225" />
<hkern u1="&#x37;" u2="&#x2c;" k="225" />
<hkern u1="&#x38;" u2="&#x2026;" k="20" />
<hkern u1="&#x38;" u2="&#x2e;" k="20" />
<hkern u1="&#x38;" u2="&#x2c;" k="20" />
<hkern u1="&#x39;" u2="&#x2026;" k="205" />
<hkern u1="&#x39;" u2="&#x2e;" k="205" />
<hkern u1="&#x39;" u2="&#x2c;" k="205" />
<hkern u1="A" u2="x" k="-43" />
<hkern u1="A" u2="\" k="215" />
<hkern u1="A" u2="V" k="205" />
<hkern u1="B" u2="&#x203a;" k="25" />
<hkern u1="B" u2="&#x201c;" k="51" />
<hkern u1="B" u2="&#x2018;" k="51" />
<hkern u1="B" u2="&#x178;" k="123" />
<hkern u1="B" u2="&#xff;" k="45" />
<hkern u1="B" u2="&#xfd;" k="45" />
<hkern u1="B" u2="&#xdd;" k="123" />
<hkern u1="B" u2="&#xc6;" k="20" />
<hkern u1="B" u2="&#xc5;" k="8" />
<hkern u1="B" u2="&#xc4;" k="8" />
<hkern u1="B" u2="&#xc3;" k="8" />
<hkern u1="B" u2="&#xc2;" k="8" />
<hkern u1="B" u2="&#xc1;" k="8" />
<hkern u1="B" u2="&#xc0;" k="8" />
<hkern u1="B" u2="&#xbb;" k="25" />
<hkern u1="B" u2="z" k="31" />
<hkern u1="B" u2="y" k="45" />
<hkern u1="B" u2="w" k="41" />
<hkern u1="B" u2="v" k="45" />
<hkern u1="B" u2="Y" k="123" />
<hkern u1="B" u2="W" k="27" />
<hkern u1="B" u2="V" k="45" />
<hkern u1="B" u2="A" k="8" />
<hkern u1="D" u2="x" k="14" />
<hkern u1="D" u2="_" k="150" />
<hkern u1="D" u2="X" k="37" />
<hkern u1="D" u2="V" k="90" />
<hkern u1="F" u2="&#x203a;" k="66" />
<hkern u1="F" u2="&#x2039;" k="57" />
<hkern u1="F" u2="&#x2026;" k="225" />
<hkern u1="F" u2="&#x201e;" k="137" />
<hkern u1="F" u2="&#x201a;" k="137" />
<hkern u1="F" u2="&#x153;" k="41" />
<hkern u1="F" u2="&#xff;" k="51" />
<hkern u1="F" u2="&#xfd;" k="51" />
<hkern u1="F" u2="&#xfc;" k="31" />
<hkern u1="F" u2="&#xfb;" k="31" />
<hkern u1="F" u2="&#xfa;" k="31" />
<hkern u1="F" u2="&#xf9;" k="31" />
<hkern u1="F" u2="&#xf8;" k="41" />
<hkern u1="F" u2="&#xf6;" k="41" />
<hkern u1="F" u2="&#xf5;" k="41" />
<hkern u1="F" u2="&#xf4;" k="41" />
<hkern u1="F" u2="&#xf3;" k="41" />
<hkern u1="F" u2="&#xf2;" k="41" />
<hkern u1="F" u2="&#xf1;" k="66" />
<hkern u1="F" u2="&#xf0;" k="41" />
<hkern u1="F" u2="&#xef;" k="-37" />
<hkern u1="F" u2="&#xee;" k="-33" />
<hkern u1="F" u2="&#xeb;" k="41" />
<hkern u1="F" u2="&#xea;" k="41" />
<hkern u1="F" u2="&#xe9;" k="41" />
<hkern u1="F" u2="&#xe8;" k="41" />
<hkern u1="F" u2="&#xe7;" k="41" />
<hkern u1="F" u2="&#xe6;" k="41" />
<hkern u1="F" u2="&#xe5;" k="41" />
<hkern u1="F" u2="&#xe4;" k="41" />
<hkern u1="F" u2="&#xe3;" k="41" />
<hkern u1="F" u2="&#xe2;" k="41" />
<hkern u1="F" u2="&#xe1;" k="41" />
<hkern u1="F" u2="&#xe0;" k="41" />
<hkern u1="F" u2="&#xc5;" k="47" />
<hkern u1="F" u2="&#xc4;" k="47" />
<hkern u1="F" u2="&#xc3;" k="47" />
<hkern u1="F" u2="&#xc2;" k="47" />
<hkern u1="F" u2="&#xc1;" k="47" />
<hkern u1="F" u2="&#xc0;" k="47" />
<hkern u1="F" u2="&#xbb;" k="66" />
<hkern u1="F" u2="&#xab;" k="57" />
<hkern u1="F" u2="z" k="10" />
<hkern u1="F" u2="y" k="51" />
<hkern u1="F" u2="x" k="51" />
<hkern u1="F" u2="w" k="31" />
<hkern u1="F" u2="v" k="51" />
<hkern u1="F" u2="u" k="31" />
<hkern u1="F" u2="t" k="29" />
<hkern u1="F" u2="s" k="41" />
<hkern u1="F" u2="r" k="66" />
<hkern u1="F" u2="q" k="41" />
<hkern u1="F" u2="p" k="66" />
<hkern u1="F" u2="o" k="41" />
<hkern u1="F" u2="n" k="66" />
<hkern u1="F" u2="m" k="66" />
<hkern u1="F" u2="g" k="41" />
<hkern u1="F" u2="e" k="41" />
<hkern u1="F" u2="d" k="41" />
<hkern u1="F" u2="c" k="41" />
<hkern u1="F" u2="a" k="41" />
<hkern u1="F" u2="A" k="47" />
<hkern u1="F" u2="&#x2e;" k="225" />
<hkern u1="F" u2="&#x2c;" k="225" />
<hkern u1="K" u2="&#xf8;" k="102" />
<hkern u1="K" u2="x" k="-37" />
<hkern u1="L" u2="&#x2019;" k="221" />
<hkern u1="L" u2="x" k="-10" />
<hkern u1="L" u2="V" k="215" />
<hkern u1="O" u2="x" k="14" />
<hkern u1="O" u2="_" k="150" />
<hkern u1="O" u2="X" k="37" />
<hkern u1="O" u2="V" k="90" />
<hkern u1="P" u2="&#x203a;" k="10" />
<hkern u1="P" u2="&#x2039;" k="51" />
<hkern u1="P" u2="&#x2026;" k="281" />
<hkern u1="P" u2="&#x201e;" k="139" />
<hkern u1="P" u2="&#x201a;" k="139" />
<hkern u1="P" u2="&#x153;" k="23" />
<hkern u1="P" u2="&#xff;" k="10" />
<hkern u1="P" u2="&#xfd;" k="10" />
<hkern u1="P" u2="&#xfc;" k="10" />
<hkern u1="P" u2="&#xfb;" k="10" />
<hkern u1="P" u2="&#xfa;" k="10" />
<hkern u1="P" u2="&#xf9;" k="10" />
<hkern u1="P" u2="&#xf8;" k="23" />
<hkern u1="P" u2="&#xf6;" k="23" />
<hkern u1="P" u2="&#xf5;" k="23" />
<hkern u1="P" u2="&#xf4;" k="23" />
<hkern u1="P" u2="&#xf3;" k="23" />
<hkern u1="P" u2="&#xf2;" k="23" />
<hkern u1="P" u2="&#xf1;" k="18" />
<hkern u1="P" u2="&#xf0;" k="23" />
<hkern u1="P" u2="&#xef;" k="-37" />
<hkern u1="P" u2="&#xee;" k="-74" />
<hkern u1="P" u2="&#xeb;" k="23" />
<hkern u1="P" u2="&#xea;" k="23" />
<hkern u1="P" u2="&#xe9;" k="23" />
<hkern u1="P" u2="&#xe8;" k="23" />
<hkern u1="P" u2="&#xe7;" k="23" />
<hkern u1="P" u2="&#xe6;" k="23" />
<hkern u1="P" u2="&#xe5;" k="23" />
<hkern u1="P" u2="&#xe4;" k="23" />
<hkern u1="P" u2="&#xe3;" k="23" />
<hkern u1="P" u2="&#xe2;" k="23" />
<hkern u1="P" u2="&#xe1;" k="23" />
<hkern u1="P" u2="&#xe0;" k="23" />
<hkern u1="P" u2="&#xc5;" k="47" />
<hkern u1="P" u2="&#xc4;" k="47" />
<hkern u1="P" u2="&#xc3;" k="47" />
<hkern u1="P" u2="&#xc2;" k="47" />
<hkern u1="P" u2="&#xc1;" k="47" />
<hkern u1="P" u2="&#xc0;" k="47" />
<hkern u1="P" u2="&#xbb;" k="10" />
<hkern u1="P" u2="&#xab;" k="51" />
<hkern u1="P" u2="z" k="10" />
<hkern u1="P" u2="y" k="10" />
<hkern u1="P" u2="x" k="20" />
<hkern u1="P" u2="v" k="10" />
<hkern u1="P" u2="u" k="10" />
<hkern u1="P" u2="s" k="10" />
<hkern u1="P" u2="r" k="18" />
<hkern u1="P" u2="q" k="23" />
<hkern u1="P" u2="p" k="18" />
<hkern u1="P" u2="o" k="23" />
<hkern u1="P" u2="n" k="18" />
<hkern u1="P" u2="m" k="18" />
<hkern u1="P" u2="g" k="23" />
<hkern u1="P" u2="e" k="23" />
<hkern u1="P" u2="d" k="23" />
<hkern u1="P" u2="c" k="23" />
<hkern u1="P" u2="a" k="37" />
<hkern u1="P" u2="W" k="16" />
<hkern u1="P" u2="V" k="27" />
<hkern u1="P" u2="J" k="158" />
<hkern u1="P" u2="A" k="47" />
<hkern u1="P" u2="&#x2e;" k="281" />
<hkern u1="P" u2="&#x2c;" k="281" />
<hkern u1="Q" u2="x" k="14" />
<hkern u1="Q" u2="_" k="150" />
<hkern u1="Q" u2="X" k="37" />
<hkern u1="Q" u2="V" k="90" />
<hkern u1="R" u2="&#xf8;" k="49" />
<hkern u1="R" u2="V" k="33" />
<hkern u1="S" u2="_" k="49" />
<hkern u1="S" u2="V" k="51" />
<hkern u1="T" u2="&#xff;" k="82" />
<hkern u1="T" u2="&#xfc;" k="170" />
<hkern u1="T" u2="&#xf6;" k="143" />
<hkern u1="T" u2="&#xf1;" k="215" />
<hkern u1="T" u2="&#xee;" k="-10" />
<hkern u1="T" u2="&#xec;" k="-18" />
<hkern u1="T" u2="&#xeb;" k="143" />
<hkern u1="T" u2="&#xea;" k="219" />
<hkern u1="T" u2="&#xe4;" k="184" />
<hkern u1="T" u2="&#xe3;" k="172" />
<hkern u1="T" u2="&#xe2;" k="217" />
<hkern u1="T" u2="x" k="172" />
<hkern u1="T" u2="_" k="139" />
<hkern u1="T" u2="&#x2f;" k="174" />
<hkern u1="U" u2="x" k="14" />
<hkern u1="U" u2="a" k="18" />
<hkern u1="V" u2="&#x203a;" k="61" />
<hkern u1="V" u2="&#x2039;" k="119" />
<hkern u1="V" u2="&#x2026;" k="211" />
<hkern u1="V" u2="&#x2014;" k="117" />
<hkern u1="V" u2="&#x2013;" k="117" />
<hkern u1="V" u2="&#x153;" k="139" />
<hkern u1="V" u2="&#x152;" k="25" />
<hkern u1="V" u2="&#xff;" k="68" />
<hkern u1="V" u2="&#xfd;" k="68" />
<hkern u1="V" u2="&#xfc;" k="111" />
<hkern u1="V" u2="&#xfb;" k="111" />
<hkern u1="V" u2="&#xfa;" k="111" />
<hkern u1="V" u2="&#xf9;" k="111" />
<hkern u1="V" u2="&#xf8;" k="139" />
<hkern u1="V" u2="&#xf6;" k="139" />
<hkern u1="V" u2="&#xf5;" k="139" />
<hkern u1="V" u2="&#xf4;" k="139" />
<hkern u1="V" u2="&#xf3;" k="139" />
<hkern u1="V" u2="&#xf2;" k="139" />
<hkern u1="V" u2="&#xf1;" k="96" />
<hkern u1="V" u2="&#xf0;" k="139" />
<hkern u1="V" u2="&#xeb;" k="139" />
<hkern u1="V" u2="&#xea;" k="139" />
<hkern u1="V" u2="&#xe9;" k="139" />
<hkern u1="V" u2="&#xe8;" k="139" />
<hkern u1="V" u2="&#xe7;" k="139" />
<hkern u1="V" u2="&#xe6;" k="139" />
<hkern u1="V" u2="&#xe5;" k="139" />
<hkern u1="V" u2="&#xe4;" k="139" />
<hkern u1="V" u2="&#xe3;" k="139" />
<hkern u1="V" u2="&#xe2;" k="139" />
<hkern u1="V" u2="&#xe1;" k="139" />
<hkern u1="V" u2="&#xe0;" k="139" />
<hkern u1="V" u2="&#xd8;" k="25" />
<hkern u1="V" u2="&#xd6;" k="25" />
<hkern u1="V" u2="&#xd5;" k="25" />
<hkern u1="V" u2="&#xd4;" k="25" />
<hkern u1="V" u2="&#xd3;" k="25" />
<hkern u1="V" u2="&#xd2;" k="25" />
<hkern u1="V" u2="&#xc7;" k="25" />
<hkern u1="V" u2="&#xc6;" k="250" />
<hkern u1="V" u2="&#xc5;" k="164" />
<hkern u1="V" u2="&#xc4;" k="164" />
<hkern u1="V" u2="&#xc3;" k="164" />
<hkern u1="V" u2="&#xc2;" k="164" />
<hkern u1="V" u2="&#xc1;" k="164" />
<hkern u1="V" u2="&#xc0;" k="164" />
<hkern u1="V" u2="&#xbb;" k="61" />
<hkern u1="V" u2="&#xad;" k="117" />
<hkern u1="V" u2="&#xab;" k="119" />
<hkern u1="V" u2="y" k="68" />
<hkern u1="V" u2="x" k="57" />
<hkern u1="V" u2="w" k="61" />
<hkern u1="V" u2="v" k="68" />
<hkern u1="V" u2="u" k="111" />
<hkern u1="V" u2="t" k="27" />
<hkern u1="V" u2="s" k="113" />
<hkern u1="V" u2="r" k="96" />
<hkern u1="V" u2="q" k="139" />
<hkern u1="V" u2="p" k="96" />
<hkern u1="V" u2="o" k="139" />
<hkern u1="V" u2="n" k="96" />
<hkern u1="V" u2="m" k="96" />
<hkern u1="V" u2="g" k="139" />
<hkern u1="V" u2="e" k="139" />
<hkern u1="V" u2="d" k="139" />
<hkern u1="V" u2="c" k="139" />
<hkern u1="V" u2="a" k="139" />
<hkern u1="V" u2="_" k="150" />
<hkern u1="V" u2="S" k="41" />
<hkern u1="V" u2="Q" k="25" />
<hkern u1="V" u2="O" k="25" />
<hkern u1="V" u2="J" k="111" />
<hkern u1="V" u2="G" k="25" />
<hkern u1="V" u2="C" k="25" />
<hkern u1="V" u2="A" k="164" />
<hkern u1="V" u2="&#x2f;" k="170" />
<hkern u1="V" u2="&#x2e;" k="211" />
<hkern u1="V" u2="&#x2d;" k="117" />
<hkern u1="V" u2="&#x2c;" k="211" />
<hkern u1="W" u2="&#xe4;" k="88" />
<hkern u1="W" u2="x" k="35" />
<hkern u1="W" u2="_" k="88" />
<hkern u1="W" u2="&#x2f;" k="133" />
<hkern u1="X" u2="&#x203a;" k="25" />
<hkern u1="X" u2="&#x2039;" k="94" />
<hkern u1="X" u2="&#x201d;" k="8" />
<hkern u1="X" u2="&#x201c;" k="66" />
<hkern u1="X" u2="&#x2019;" k="8" />
<hkern u1="X" u2="&#x2018;" k="66" />
<hkern u1="X" u2="&#x2014;" k="57" />
<hkern u1="X" u2="&#x2013;" k="57" />
<hkern u1="X" u2="&#x153;" k="72" />
<hkern u1="X" u2="&#x152;" k="37" />
<hkern u1="X" u2="&#xff;" k="145" />
<hkern u1="X" u2="&#xfd;" k="145" />
<hkern u1="X" u2="&#xf8;" k="72" />
<hkern u1="X" u2="&#xf6;" k="72" />
<hkern u1="X" u2="&#xf5;" k="72" />
<hkern u1="X" u2="&#xf4;" k="72" />
<hkern u1="X" u2="&#xf3;" k="72" />
<hkern u1="X" u2="&#xf2;" k="72" />
<hkern u1="X" u2="&#xf0;" k="72" />
<hkern u1="X" u2="&#xeb;" k="72" />
<hkern u1="X" u2="&#xea;" k="72" />
<hkern u1="X" u2="&#xe9;" k="72" />
<hkern u1="X" u2="&#xe8;" k="72" />
<hkern u1="X" u2="&#xe7;" k="72" />
<hkern u1="X" u2="&#xe6;" k="72" />
<hkern u1="X" u2="&#xe5;" k="72" />
<hkern u1="X" u2="&#xe4;" k="72" />
<hkern u1="X" u2="&#xe3;" k="72" />
<hkern u1="X" u2="&#xe2;" k="72" />
<hkern u1="X" u2="&#xe1;" k="72" />
<hkern u1="X" u2="&#xe0;" k="72" />
<hkern u1="X" u2="&#xd8;" k="37" />
<hkern u1="X" u2="&#xd6;" k="37" />
<hkern u1="X" u2="&#xd5;" k="37" />
<hkern u1="X" u2="&#xd4;" k="37" />
<hkern u1="X" u2="&#xd3;" k="37" />
<hkern u1="X" u2="&#xd2;" k="37" />
<hkern u1="X" u2="&#xc7;" k="37" />
<hkern u1="X" u2="&#xbb;" k="25" />
<hkern u1="X" u2="&#xad;" k="57" />
<hkern u1="X" u2="&#xab;" k="94" />
<hkern u1="X" u2="y" k="145" />
<hkern u1="X" u2="w" k="76" />
<hkern u1="X" u2="v" k="145" />
<hkern u1="X" u2="q" k="72" />
<hkern u1="X" u2="o" k="72" />
<hkern u1="X" u2="g" k="72" />
<hkern u1="X" u2="e" k="72" />
<hkern u1="X" u2="d" k="72" />
<hkern u1="X" u2="c" k="72" />
<hkern u1="X" u2="a" k="72" />
<hkern u1="X" u2="Q" k="37" />
<hkern u1="X" u2="O" k="37" />
<hkern u1="X" u2="G" k="37" />
<hkern u1="X" u2="C" k="37" />
<hkern u1="X" u2="&#x2d;" k="57" />
<hkern u1="Y" u2="&#xff;" k="113" />
<hkern u1="Y" u2="&#xf6;" k="143" />
<hkern u1="Y" u2="&#xf5;" k="164" />
<hkern u1="Y" u2="&#xf4;" k="211" />
<hkern u1="Y" u2="&#xee;" k="-41" />
<hkern u1="Y" u2="&#xec;" k="-51" />
<hkern u1="Y" u2="&#xeb;" k="174" />
<hkern u1="Y" u2="&#xea;" k="174" />
<hkern u1="Y" u2="&#xe8;" k="195" />
<hkern u1="Y" u2="&#xe4;" k="180" />
<hkern u1="Y" u2="&#xe3;" k="195" />
<hkern u1="Y" u2="&#xe2;" k="231" />
<hkern u1="Y" u2="&#xe0;" k="195" />
<hkern u1="Y" u2="x" k="115" />
<hkern u1="Y" u2="_" k="168" />
<hkern u1="Y" u2="&#x34;" k="186" />
<hkern u1="Y" u2="&#x2f;" k="252" />
<hkern u1="Z" u2="x" k="-20" />
<hkern u1="\" u2="&#x178;" k="252" />
<hkern u1="\" u2="&#xdd;" k="252" />
<hkern u1="\" u2="Y" k="252" />
<hkern u1="\" u2="W" k="160" />
<hkern u1="\" u2="V" k="207" />
<hkern u1="\" u2="T" k="174" />
<hkern u1="\" u2="&#x37;" k="39" />
<hkern u1="_" u2="&#x178;" k="168" />
<hkern u1="_" u2="&#x153;" k="82" />
<hkern u1="_" u2="&#x152;" k="150" />
<hkern u1="_" u2="&#xff;" k="111" />
<hkern u1="_" u2="&#xfd;" k="111" />
<hkern u1="_" u2="&#xf8;" k="82" />
<hkern u1="_" u2="&#xf6;" k="82" />
<hkern u1="_" u2="&#xf5;" k="82" />
<hkern u1="_" u2="&#xf4;" k="82" />
<hkern u1="_" u2="&#xf3;" k="82" />
<hkern u1="_" u2="&#xf2;" k="82" />
<hkern u1="_" u2="&#xf0;" k="82" />
<hkern u1="_" u2="&#xeb;" k="82" />
<hkern u1="_" u2="&#xea;" k="82" />
<hkern u1="_" u2="&#xe9;" k="82" />
<hkern u1="_" u2="&#xe8;" k="82" />
<hkern u1="_" u2="&#xe7;" k="82" />
<hkern u1="_" u2="&#xe6;" k="82" />
<hkern u1="_" u2="&#xe5;" k="82" />
<hkern u1="_" u2="&#xe4;" k="82" />
<hkern u1="_" u2="&#xe3;" k="82" />
<hkern u1="_" u2="&#xe2;" k="82" />
<hkern u1="_" u2="&#xe1;" k="82" />
<hkern u1="_" u2="&#xe0;" k="82" />
<hkern u1="_" u2="&#xdd;" k="168" />
<hkern u1="_" u2="&#xd8;" k="150" />
<hkern u1="_" u2="&#xd6;" k="150" />
<hkern u1="_" u2="&#xd5;" k="150" />
<hkern u1="_" u2="&#xd4;" k="150" />
<hkern u1="_" u2="&#xd3;" k="150" />
<hkern u1="_" u2="&#xd2;" k="150" />
<hkern u1="_" u2="&#xc7;" k="150" />
<hkern u1="_" u2="y" k="111" />
<hkern u1="_" u2="w" k="102" />
<hkern u1="_" u2="v" k="111" />
<hkern u1="_" u2="q" k="82" />
<hkern u1="_" u2="o" k="82" />
<hkern u1="_" u2="g" k="82" />
<hkern u1="_" u2="e" k="82" />
<hkern u1="_" u2="d" k="82" />
<hkern u1="_" u2="c" k="82" />
<hkern u1="_" u2="a" k="82" />
<hkern u1="_" u2="Y" k="168" />
<hkern u1="_" u2="W" k="139" />
<hkern u1="_" u2="V" k="150" />
<hkern u1="_" u2="T" k="139" />
<hkern u1="_" u2="Q" k="150" />
<hkern u1="_" u2="O" k="150" />
<hkern u1="_" u2="G" k="150" />
<hkern u1="_" u2="C" k="150" />
<hkern u1="_" u2="&#x30;" k="92" />
<hkern u1="b" u2="x" k="47" />
<hkern u1="b" u2="_" k="74" />
<hkern u1="b" u2="X" k="72" />
<hkern u1="b" u2="V" k="188" />
<hkern u1="c" u2="V" k="55" />
<hkern u1="e" u2="x" k="27" />
<hkern u1="e" u2="V" k="121" />
<hkern u1="f" u2="_" k="16" />
<hkern u1="f" u2="&#x3f;" k="-29" />
<hkern u1="f" u2="&#x2a;" k="-66" />
<hkern u1="f" u2="&#x21;" k="-10" />
<hkern u1="g" u2="&#x201c;" k="72" />
<hkern u1="g" u2="&#x2018;" k="72" />
<hkern u1="g" u2="j" k="-10" />
<hkern u1="h" u2="V" k="139" />
<hkern u1="j" u2="j" k="-27" />
<hkern u1="k" u2="&#xf8;" k="35" />
<hkern u1="m" u2="V" k="139" />
<hkern u1="n" u2="&#x201c;" k="92" />
<hkern u1="n" u2="&#x2018;" k="92" />
<hkern u1="n" u2="V" k="139" />
<hkern u1="o" u2="x" k="47" />
<hkern u1="o" u2="_" k="74" />
<hkern u1="o" u2="X" k="72" />
<hkern u1="o" u2="V" k="188" />
<hkern u1="p" u2="x" k="47" />
<hkern u1="p" u2="_" k="74" />
<hkern u1="p" u2="X" k="72" />
<hkern u1="p" u2="V" k="188" />
<hkern u1="s" u2="x" k="25" />
<hkern u1="s" u2="V" k="55" />
<hkern u1="u" u2="V" k="137" />
<hkern u1="v" u2="_" k="102" />
<hkern u1="v" u2="X" k="98" />
<hkern u1="v" u2="V" k="47" />
<hkern u1="x" u2="&#x2039;" k="111" />
<hkern u1="x" u2="&#x2014;" k="129" />
<hkern u1="x" u2="&#x2013;" k="129" />
<hkern u1="x" u2="&#x153;" k="45" />
<hkern u1="x" u2="&#xf8;" k="45" />
<hkern u1="x" u2="&#xf6;" k="45" />
<hkern u1="x" u2="&#xf5;" k="45" />
<hkern u1="x" u2="&#xf4;" k="45" />
<hkern u1="x" u2="&#xf3;" k="45" />
<hkern u1="x" u2="&#xf2;" k="45" />
<hkern u1="x" u2="&#xf0;" k="45" />
<hkern u1="x" u2="&#xeb;" k="45" />
<hkern u1="x" u2="&#xea;" k="45" />
<hkern u1="x" u2="&#xe9;" k="45" />
<hkern u1="x" u2="&#xe8;" k="45" />
<hkern u1="x" u2="&#xe7;" k="45" />
<hkern u1="x" u2="&#xe6;" k="45" />
<hkern u1="x" u2="&#xe5;" k="45" />
<hkern u1="x" u2="&#xe4;" k="45" />
<hkern u1="x" u2="&#xe3;" k="45" />
<hkern u1="x" u2="&#xe2;" k="45" />
<hkern u1="x" u2="&#xe1;" k="45" />
<hkern u1="x" u2="&#xe0;" k="45" />
<hkern u1="x" u2="&#xad;" k="129" />
<hkern u1="x" u2="&#xab;" k="111" />
<hkern u1="x" u2="q" k="45" />
<hkern u1="x" u2="o" k="45" />
<hkern u1="x" u2="g" k="45" />
<hkern u1="x" u2="e" k="45" />
<hkern u1="x" u2="d" k="45" />
<hkern u1="x" u2="c" k="45" />
<hkern u1="x" u2="a" k="45" />
<hkern u1="x" u2="W" k="10" />
<hkern u1="x" u2="V" k="39" />
<hkern u1="x" u2="T" k="115" />
<hkern u1="x" u2="&#x2d;" k="129" />
<hkern u1="y" u2="&#x153;" k="35" />
<hkern u1="y" u2="&#xf8;" k="35" />
<hkern u1="y" u2="&#xf6;" k="35" />
<hkern u1="y" u2="&#xf5;" k="35" />
<hkern u1="y" u2="&#xf4;" k="35" />
<hkern u1="y" u2="&#xf3;" k="35" />
<hkern u1="y" u2="&#xf2;" k="35" />
<hkern u1="y" u2="&#xf0;" k="35" />
<hkern u1="y" u2="&#xeb;" k="35" />
<hkern u1="y" u2="&#xea;" k="35" />
<hkern u1="y" u2="&#xe9;" k="35" />
<hkern u1="y" u2="&#xe8;" k="35" />
<hkern u1="y" u2="&#xe7;" k="35" />
<hkern u1="y" u2="&#xe6;" k="35" />
<hkern u1="y" u2="&#xe5;" k="35" />
<hkern u1="y" u2="&#xe4;" k="35" />
<hkern u1="y" u2="&#xe3;" k="35" />
<hkern u1="y" u2="&#xe2;" k="35" />
<hkern u1="y" u2="&#xe1;" k="35" />
<hkern u1="y" u2="&#xe0;" k="35" />
<hkern u1="y" u2="q" k="35" />
<hkern u1="y" u2="o" k="35" />
<hkern u1="y" u2="g" k="35" />
<hkern u1="y" u2="e" k="35" />
<hkern u1="y" u2="d" k="35" />
<hkern u1="y" u2="c" k="35" />
<hkern u1="y" u2="a" k="35" />
<hkern u1="y" u2="_" k="102" />
<hkern u1="y" u2="X" k="98" />
<hkern u1="y" u2="V" k="47" />
<hkern u1="&#xab;" u2="x" k="10" />
<hkern u1="&#xab;" u2="V" k="47" />
<hkern u1="&#xad;" u2="x" k="129" />
<hkern u1="&#xad;" u2="X" k="55" />
<hkern u1="&#xad;" u2="V" k="111" />
<hkern u1="&#xad;" u2="&#x32;" k="113" />
<hkern u1="&#xad;" u2="&#x31;" k="82" />
<hkern u1="&#xbb;" u2="x" k="111" />
<hkern u1="&#xbb;" u2="a" k="18" />
<hkern u1="&#xbb;" u2="X" k="88" />
<hkern u1="&#xbb;" u2="V" k="152" />
<hkern u1="&#xbb;" u2="J" k="47" />
<hkern u1="&#xc0;" u2="x" k="-43" />
<hkern u1="&#xc0;" u2="\" k="215" />
<hkern u1="&#xc0;" u2="V" k="205" />
<hkern u1="&#xc1;" u2="x" k="-43" />
<hkern u1="&#xc1;" u2="\" k="215" />
<hkern u1="&#xc1;" u2="V" k="205" />
<hkern u1="&#xc2;" u2="x" k="-43" />
<hkern u1="&#xc2;" u2="\" k="215" />
<hkern u1="&#xc2;" u2="V" k="205" />
<hkern u1="&#xc3;" u2="x" k="-43" />
<hkern u1="&#xc3;" u2="\" k="215" />
<hkern u1="&#xc3;" u2="V" k="205" />
<hkern u1="&#xc4;" u2="x" k="-43" />
<hkern u1="&#xc4;" u2="\" k="215" />
<hkern u1="&#xc4;" u2="V" k="205" />
<hkern u1="&#xc5;" u2="x" k="-43" />
<hkern u1="&#xc5;" u2="\" k="215" />
<hkern u1="&#xc5;" u2="V" k="205" />
<hkern u1="&#xd2;" u2="x" k="14" />
<hkern u1="&#xd2;" u2="_" k="150" />
<hkern u1="&#xd2;" u2="X" k="37" />
<hkern u1="&#xd2;" u2="V" k="90" />
<hkern u1="&#xd3;" u2="x" k="14" />
<hkern u1="&#xd3;" u2="_" k="150" />
<hkern u1="&#xd3;" u2="X" k="37" />
<hkern u1="&#xd3;" u2="V" k="90" />
<hkern u1="&#xd4;" u2="x" k="14" />
<hkern u1="&#xd4;" u2="_" k="150" />
<hkern u1="&#xd4;" u2="X" k="37" />
<hkern u1="&#xd4;" u2="V" k="90" />
<hkern u1="&#xd5;" u2="x" k="14" />
<hkern u1="&#xd5;" u2="_" k="150" />
<hkern u1="&#xd5;" u2="X" k="37" />
<hkern u1="&#xd5;" u2="V" k="90" />
<hkern u1="&#xd6;" u2="x" k="14" />
<hkern u1="&#xd6;" u2="_" k="150" />
<hkern u1="&#xd6;" u2="X" k="37" />
<hkern u1="&#xd6;" u2="V" k="90" />
<hkern u1="&#xd8;" u2="x" k="14" />
<hkern u1="&#xd8;" u2="_" k="150" />
<hkern u1="&#xd8;" u2="X" k="37" />
<hkern u1="&#xd8;" u2="V" k="90" />
<hkern u1="&#xd9;" u2="x" k="14" />
<hkern u1="&#xd9;" u2="a" k="18" />
<hkern u1="&#xda;" u2="x" k="14" />
<hkern u1="&#xda;" u2="a" k="18" />
<hkern u1="&#xdb;" u2="x" k="14" />
<hkern u1="&#xdb;" u2="a" k="18" />
<hkern u1="&#xdc;" u2="x" k="14" />
<hkern u1="&#xdc;" u2="a" k="18" />
<hkern u1="&#xdd;" u2="&#xff;" k="113" />
<hkern u1="&#xdd;" u2="&#xf6;" k="143" />
<hkern u1="&#xdd;" u2="&#xf5;" k="164" />
<hkern u1="&#xdd;" u2="&#xf4;" k="211" />
<hkern u1="&#xdd;" u2="&#xee;" k="-41" />
<hkern u1="&#xdd;" u2="&#xec;" k="-51" />
<hkern u1="&#xdd;" u2="&#xeb;" k="174" />
<hkern u1="&#xdd;" u2="&#xea;" k="174" />
<hkern u1="&#xdd;" u2="&#xe8;" k="195" />
<hkern u1="&#xdd;" u2="&#xe4;" k="180" />
<hkern u1="&#xdd;" u2="&#xe3;" k="195" />
<hkern u1="&#xdd;" u2="&#xe2;" k="231" />
<hkern u1="&#xdd;" u2="&#xe0;" k="195" />
<hkern u1="&#xdd;" u2="x" k="115" />
<hkern u1="&#xdd;" u2="_" k="168" />
<hkern u1="&#xdd;" u2="&#x34;" k="186" />
<hkern u1="&#xdd;" u2="&#x2f;" k="252" />
<hkern u1="&#xde;" u2="&#x178;" k="178" />
<hkern u1="&#xde;" u2="&#xdd;" k="178" />
<hkern u1="&#xde;" u2="&#xc5;" k="55" />
<hkern u1="&#xde;" u2="&#xc4;" k="55" />
<hkern u1="&#xde;" u2="&#xc3;" k="55" />
<hkern u1="&#xde;" u2="&#xc2;" k="55" />
<hkern u1="&#xde;" u2="&#xc1;" k="55" />
<hkern u1="&#xde;" u2="&#xc0;" k="55" />
<hkern u1="&#xde;" u2="_" k="150" />
<hkern u1="&#xde;" u2="Y" k="178" />
<hkern u1="&#xde;" u2="W" k="55" />
<hkern u1="&#xde;" u2="V" k="76" />
<hkern u1="&#xde;" u2="T" k="66" />
<hkern u1="&#xde;" u2="A" k="55" />
<hkern u1="&#xdf;" u2="&#xff;" k="117" />
<hkern u1="&#xdf;" u2="&#xfd;" k="117" />
<hkern u1="&#xdf;" u2="y" k="117" />
<hkern u1="&#xdf;" u2="w" k="106" />
<hkern u1="&#xdf;" u2="v" k="117" />
<hkern u1="&#xe6;" u2="x" k="27" />
<hkern u1="&#xe6;" u2="V" k="121" />
<hkern u1="&#xe7;" u2="V" k="55" />
<hkern u1="&#xe8;" u2="x" k="27" />
<hkern u1="&#xe8;" u2="V" k="121" />
<hkern u1="&#xe9;" u2="x" k="27" />
<hkern u1="&#xe9;" u2="V" k="121" />
<hkern u1="&#xea;" u2="x" k="27" />
<hkern u1="&#xea;" u2="V" k="121" />
<hkern u1="&#xeb;" u2="x" k="27" />
<hkern u1="&#xeb;" u2="V" k="121" />
<hkern u1="&#xf2;" u2="x" k="47" />
<hkern u1="&#xf2;" u2="_" k="74" />
<hkern u1="&#xf2;" u2="X" k="72" />
<hkern u1="&#xf2;" u2="V" k="188" />
<hkern u1="&#xf3;" u2="x" k="47" />
<hkern u1="&#xf3;" u2="_" k="74" />
<hkern u1="&#xf3;" u2="X" k="72" />
<hkern u1="&#xf3;" u2="V" k="188" />
<hkern u1="&#xf4;" u2="x" k="47" />
<hkern u1="&#xf4;" u2="_" k="74" />
<hkern u1="&#xf4;" u2="X" k="72" />
<hkern u1="&#xf4;" u2="V" k="188" />
<hkern u1="&#xf5;" u2="x" k="47" />
<hkern u1="&#xf5;" u2="_" k="74" />
<hkern u1="&#xf5;" u2="X" k="72" />
<hkern u1="&#xf5;" u2="V" k="188" />
<hkern u1="&#xf6;" u2="x" k="47" />
<hkern u1="&#xf6;" u2="_" k="74" />
<hkern u1="&#xf6;" u2="X" k="72" />
<hkern u1="&#xf6;" u2="V" k="188" />
<hkern u1="&#xf8;" u2="x" k="47" />
<hkern u1="&#xf8;" u2="_" k="74" />
<hkern u1="&#xf8;" u2="X" k="72" />
<hkern u1="&#xf8;" u2="V" k="188" />
<hkern u1="&#xf9;" u2="V" k="137" />
<hkern u1="&#xfa;" u2="V" k="137" />
<hkern u1="&#xfb;" u2="V" k="137" />
<hkern u1="&#xfc;" u2="V" k="137" />
<hkern u1="&#xfd;" u2="_" k="102" />
<hkern u1="&#xfd;" u2="X" k="98" />
<hkern u1="&#xfd;" u2="V" k="47" />
<hkern u1="&#xfe;" u2="x" k="47" />
<hkern u1="&#xfe;" u2="_" k="74" />
<hkern u1="&#xfe;" u2="X" k="72" />
<hkern u1="&#xfe;" u2="V" k="188" />
<hkern u1="&#xff;" u2="_" k="102" />
<hkern u1="&#xff;" u2="X" k="98" />
<hkern u1="&#xff;" u2="V" k="47" />
<hkern u1="&#x153;" u2="x" k="27" />
<hkern u1="&#x153;" u2="V" k="121" />
<hkern u1="&#x178;" u2="&#xff;" k="113" />
<hkern u1="&#x178;" u2="&#xf6;" k="143" />
<hkern u1="&#x178;" u2="&#xf5;" k="164" />
<hkern u1="&#x178;" u2="&#xf4;" k="211" />
<hkern u1="&#x178;" u2="&#xee;" k="-41" />
<hkern u1="&#x178;" u2="&#xec;" k="-51" />
<hkern u1="&#x178;" u2="&#xeb;" k="174" />
<hkern u1="&#x178;" u2="&#xea;" k="174" />
<hkern u1="&#x178;" u2="&#xe8;" k="195" />
<hkern u1="&#x178;" u2="&#xe4;" k="180" />
<hkern u1="&#x178;" u2="&#xe3;" k="195" />
<hkern u1="&#x178;" u2="&#xe2;" k="231" />
<hkern u1="&#x178;" u2="&#xe0;" k="195" />
<hkern u1="&#x178;" u2="x" k="115" />
<hkern u1="&#x178;" u2="_" k="168" />
<hkern u1="&#x178;" u2="&#x34;" k="186" />
<hkern u1="&#x178;" u2="&#x2f;" k="252" />
<hkern u1="&#x2013;" u2="x" k="129" />
<hkern u1="&#x2013;" u2="X" k="55" />
<hkern u1="&#x2013;" u2="V" k="111" />
<hkern u1="&#x2013;" u2="&#x32;" k="113" />
<hkern u1="&#x2013;" u2="&#x31;" k="133" />
<hkern u1="&#x2014;" u2="x" k="129" />
<hkern u1="&#x2014;" u2="X" k="55" />
<hkern u1="&#x2014;" u2="V" k="111" />
<hkern u1="&#x2014;" u2="&#x32;" k="113" />
<hkern u1="&#x2014;" u2="&#x31;" k="82" />
<hkern u1="&#x2018;" u2="x" k="47" />
<hkern u1="&#x2018;" u2="j" k="57" />
<hkern u1="&#x2018;" u2="V" k="16" />
<hkern u1="&#x2019;" u2="x" k="27" />
<hkern u1="&#x2019;" u2="j" k="55" />
<hkern u1="&#x2019;" u2="a" k="150" />
<hkern u1="&#x2019;" u2="V" k="20" />
<hkern u1="&#x201a;" u2="V" k="121" />
<hkern u1="&#x201c;" u2="x" k="47" />
<hkern u1="&#x201c;" u2="j" k="57" />
<hkern u1="&#x201c;" u2="V" k="16" />
<hkern u1="&#x201d;" u2="x" k="27" />
<hkern u1="&#x201d;" u2="j" k="55" />
<hkern u1="&#x201d;" u2="a" k="150" />
<hkern u1="&#x201d;" u2="V" k="20" />
<hkern u1="&#x201e;" u2="V" k="121" />
<hkern u1="&#x2026;" u2="V" k="281" />
<hkern u1="&#x2026;" u2="&#x39;" k="82" />
<hkern u1="&#x2026;" u2="&#x38;" k="66" />
<hkern u1="&#x2026;" u2="&#x37;" k="45" />
<hkern u1="&#x2026;" u2="&#x2a;" k="244" />
<hkern u1="&#x2039;" u2="x" k="10" />
<hkern u1="&#x2039;" u2="V" k="47" />
<hkern u1="&#x203a;" u2="x" k="111" />
<hkern u1="&#x203a;" u2="a" k="18" />
<hkern u1="&#x203a;" u2="X" k="88" />
<hkern u1="&#x203a;" u2="V" k="152" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="76" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="104" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,y,yacute,ydieresis" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="51" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="G" 	g2="w" 	k="43" />
<hkern g1="G" 	g2="v,y,yacute,ydieresis" 	k="57" />
<hkern g1="G" 	g2="t" 	k="37" />
<hkern g1="G" 	g2="z" 	k="25" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="8" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="102" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="70" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="27" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="74" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="K" 	g2="w" 	k="143" />
<hkern g1="K" 	g2="v,y,yacute,ydieresis" 	k="164" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="123" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="K" 	g2="t" 	k="98" />
<hkern g1="K" 	g2="s" 	k="31" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-8" />
<hkern g1="K" 	g2="z" 	k="-18" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="76" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="326" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="289" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="193" />
<hkern g1="L" 	g2="T" 	k="215" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="176" />
<hkern g1="L" 	g2="w" 	k="145" />
<hkern g1="L" 	g2="v,y,yacute,ydieresis" 	k="170" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="L" 	g2="t" 	k="98" />
<hkern g1="L" 	g2="W" 	k="145" />
<hkern g1="L" 	g2="z" 	k="27" />
<hkern g1="L" 	g2="J" 	k="-10" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="49" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="37" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="121" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="90" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="55" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="10" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="92" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="39" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="102" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="106" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="R" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="63" />
<hkern g1="R" 	g2="W" 	k="29" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="S" 	g2="w" 	k="41" />
<hkern g1="S" 	g2="v,y,yacute,ydieresis" 	k="59" />
<hkern g1="S" 	g2="t" 	k="18" />
<hkern g1="S" 	g2="W" 	k="41" />
<hkern g1="S" 	g2="z" 	k="29" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="281" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="201" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-29" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="256" />
<hkern g1="T" 	g2="w" 	k="215" />
<hkern g1="T" 	g2="v,y,yacute,ydieresis" 	k="236" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="223" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="223" />
<hkern g1="T" 	g2="s" 	k="195" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="188" />
<hkern g1="T" 	g2="z" 	k="139" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="205" />
<hkern g1="T" 	g2="J" 	k="156" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="203" />
<hkern g1="T" 	g2="AE" 	k="225" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="266" />
<hkern g1="T" 	g2="idieresis" 	k="-84" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="27" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="39" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="133" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="72" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="82" />
<hkern g1="W" 	g2="w" 	k="72" />
<hkern g1="W" 	g2="v,y,yacute,ydieresis" 	k="35" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="68" />
<hkern g1="W" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="78" />
<hkern g1="W" 	g2="t" 	k="20" />
<hkern g1="W" 	g2="s" 	k="92" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="96" />
<hkern g1="W" 	g2="J" 	k="92" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="129" />
<hkern g1="W" 	g2="AE" 	k="242" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="68" />
<hkern g1="W" 	g2="S" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="188" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="168" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-29" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="131" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute,ydieresis" 	k="135" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="160" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="207" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="262" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="176" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="idieresis" 	k="-57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f" 	k="29" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="49" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="8" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="25" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="59" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="Z" 	g2="w" 	k="82" />
<hkern g1="Z" 	g2="v,y,yacute,ydieresis" 	k="106" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="131" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="Z" 	g2="idieresis" 	k="-47" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="88" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="35" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v,y,yacute,ydieresis" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="quoteleft,quotedblleft" 	k="242" />
<hkern g1="comma,period,ellipsis" 	g2="quoteright,quotedblright" 	k="252" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="39" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="236" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="203" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="39" />
<hkern g1="comma,period,ellipsis" 	g2="quotedbl,quotesingle" 	k="84" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="168" />
<hkern g1="comma,period,ellipsis" 	g2="v,y,yacute,ydieresis" 	k="207" />
<hkern g1="comma,period,ellipsis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="147" />
<hkern g1="comma,period,ellipsis" 	g2="comma,period,ellipsis" 	k="-37" />
<hkern g1="comma,period,ellipsis" 	g2="f" 	k="18" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="84" />
<hkern g1="comma,period,ellipsis" 	g2="parenright,bracketright,braceright" 	k="29" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="186" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="147" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="205" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="49" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v,y,yacute,ydieresis" 	k="70" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="63" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotesinglbase,quotedblbase" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f" 	k="10" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-18" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-18" />
<hkern g1="f" 	g2="w" 	k="-10" />
<hkern g1="f" 	g2="v,y,yacute,ydieresis" 	k="-10" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f" 	g2="t" 	k="10" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="57" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="66" />
<hkern g1="f" 	g2="parenright,bracketright,braceright" 	k="-55" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="168" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="201" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="w" 	k="10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v,y,yacute,ydieresis" 	k="27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="47" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="s" 	k="88" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="z" 	k="10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Z" 	k="27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="S" 	k="-10" />
<hkern g1="guillemotright,guilsinglright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="188" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="281" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="49" />
<hkern g1="guillemotright,guilsinglright" 	g2="v,y,yacute,ydieresis" 	k="88" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="106" />
<hkern g1="guillemotright,guilsinglright" 	g2="s" 	k="29" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="49" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="37" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="29" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="66" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="184" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="223" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="w" 	k="39" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="v,y,yacute,ydieresis" 	k="66" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="55" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="74" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="139" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="47" />
<hkern g1="idieresis" 	g2="T" 	k="-84" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="113" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="4" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="k" 	g2="w" 	k="14" />
<hkern g1="k" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="168" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="106" />
<hkern g1="k" 	g2="s" 	k="51" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="47" />
<hkern g1="h,m,n" 	g2="quoteleft,quotedblleft" 	k="131" />
<hkern g1="h,m,n" 	g2="quoteright,quotedblright" 	k="186" />
<hkern g1="h,m,n" 	g2="Y,Yacute,Ydieresis" 	k="176" />
<hkern g1="h,m,n" 	g2="T" 	k="272" />
<hkern g1="h,m,n" 	g2="quotedbl,quotesingle" 	k="84" />
<hkern g1="h,m,n" 	g2="w" 	k="78" />
<hkern g1="h,m,n" 	g2="v,y,yacute,ydieresis" 	k="92" />
<hkern g1="h,m,n" 	g2="t" 	k="39" />
<hkern g1="h,m,n" 	g2="W" 	k="68" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="guillemotright,guilsinglright" 	k="68" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="94" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="162" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="236" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="205" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="111" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="74" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v,y,yacute,ydieresis" 	k="88" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="W" 	k="139" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="31" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotesinglbase,quotedblbase" 	k="18" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,ellipsis" 	k="51" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="parenright,bracketright,braceright" 	k="27" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="102" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="92" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="55" />
<hkern g1="quoteleft,quotedblleft" 	g2="v,y,yacute,ydieresis" 	k="47" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="225" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="29" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="166" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="252" />
<hkern g1="quoteleft,quotedblleft" 	g2="z" 	k="88" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,ellipsis" 	k="297" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde" 	k="111" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="37" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="111" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="92" />
<hkern g1="quoteright,quotedblright" 	g2="w" 	k="47" />
<hkern g1="quoteright,quotedblright" 	g2="v,y,yacute,ydieresis" 	k="102" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="291" />
<hkern g1="quoteright,quotedblright" 	g2="t" 	k="76" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="254" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="233" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="57" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,ellipsis" 	k="326" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="147" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="f" 	k="31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="129" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="150" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v,y,yacute,ydieresis" 	k="117" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="29" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="55" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="quotedbl,quotesingle" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="111" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="176" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,ellipsis" 	k="84" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="84" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="184" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="233" />
<hkern g1="s" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="s" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="121" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="190" />
<hkern g1="s" 	g2="T" 	k="223" />
<hkern g1="s" 	g2="w" 	k="59" />
<hkern g1="s" 	g2="v,y,yacute,ydieresis" 	k="78" />
<hkern g1="s" 	g2="hyphen,uni00AD,endash,emdash" 	k="18" />
<hkern g1="s" 	g2="t" 	k="29" />
<hkern g1="s" 	g2="W" 	k="55" />
<hkern g1="s" 	g2="z" 	k="29" />
<hkern g1="s" 	g2="Z" 	k="27" />
<hkern g1="s" 	g2="quotesinglbase,quotedblbase" 	k="27" />
<hkern g1="s" 	g2="f" 	k="20" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="29" />
<hkern g1="t" 	g2="quotedbl,quotesingle" 	k="8" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="106" />
<hkern g1="w" 	g2="hyphen,uni00AD,endash,emdash" 	k="51" />
<hkern g1="w" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="6" />
<hkern g1="w" 	g2="s" 	k="37" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="109" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="168" />
<hkern g1="zero" 	g2="comma,period,ellipsis" 	k="84" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="76" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="119" />
<hkern g1="v,y,yacute,ydieresis" 	g2="T" 	k="170" />
<hkern g1="v,y,yacute,ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="76" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="v,y,yacute,ydieresis" 	g2="W" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="s" 	k="37" />
<hkern g1="v,y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="v,y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="117" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="231" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="76" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="74" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="c,ccedilla" 	g2="T" 	k="129" />
<hkern g1="c,ccedilla" 	g2="w" 	k="14" />
<hkern g1="c,ccedilla" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="c,ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="c,ccedilla" 	g2="W" 	k="29" />
</font>
</defs></svg> 