<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="azo_sanslight_italic" horiz-adv-x="1642" >
<font-face units-per-em="2048" ascent="1581" descent="-467" />
<missing-glyph horiz-adv-x="491" />
<glyph unicode="&#xfb01;" horiz-adv-x="1140" d="M121 901l18 107h170l49 274q31 165 113 234q79 71 215 71q73 0 156 -22l-19 -113q-77 25 -139 25q-87 0 -135 -45q-52 -46 -74 -170l-45 -254h277l-19 -107h-276l-160 -901h-121l160 901h-170zM752 0l178 1008h121l-179 -1008h-120zM958 1354q0 41 29.5 69.5t69.5 28.5 q38 0 63 -25.5t25 -62.5q0 -41 -29 -69.5t-69 -28.5q-38 0 -63.5 25.5t-25.5 62.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1140" d="M121 901l18 107h170l49 274q31 165 113 234q79 71 215 71q73 0 156 -22l-19 -113q-77 25 -139 25q-87 0 -135 -45q-52 -46 -74 -170l-45 -254h277l-19 -107h-276l-160 -901h-121l160 901h-170zM752 0l276 1565h121l-277 -1565h-120z" />
<glyph horiz-adv-x="2" />
<glyph horiz-adv-x="2" />
<glyph horiz-adv-x="682" />
<glyph unicode="&#xd;" horiz-adv-x="694" />
<glyph unicode=" "  horiz-adv-x="491" />
<glyph unicode="&#x09;" horiz-adv-x="491" />
<glyph unicode="&#xa0;" horiz-adv-x="491" />
<glyph unicode="!" horiz-adv-x="634" d="M141 74q0 42 31 72t74 30q41 0 68.5 -28t27.5 -68q0 -43 -30.5 -73t-73.5 -30q-41 0 -69 28.5t-28 68.5zM266 430l156 1053h137l-215 -1053h-78z" />
<glyph unicode="&#x22;" horiz-adv-x="690" d="M238 985l73 535h117l-115 -535h-75zM545 985l73 535h117l-114 -535h-76z" />
<glyph unicode="#" horiz-adv-x="1228" d="M23 477l40 88h269l164 353h-271l41 88h271l221 477h92l-221 -477h299l221 477h92l-221 -477h268l-41 -88h-268l-164 -353h270l-41 -88h-270l-221 -477h-92l221 477h-299l-221 -477h-92l221 477h-268zM424 565h299l164 353h-299z" />
<glyph unicode="$" horiz-adv-x="1116" d="M111 217l22 129q88 -64 197.5 -100.5t212.5 -36.5q82 0 150 23.5t114.5 79.5t46.5 137q0 39 -13 70t-33.5 54.5t-59 46t-76 39.5t-99.5 41q-64 25 -106.5 44.5t-88 49t-72 61.5t-43.5 77t-17 100q0 159 115.5 254t297.5 100l46 252h100l-45 -258q157 -22 293 -96 l-21 -125q-85 53 -181.5 84t-184.5 31q-124 0 -210.5 -58t-86.5 -169q0 -39 13.5 -70.5t33.5 -54t60 -46t76 -39t99 -40.5q65 -26 107 -45.5t88 -49.5t71.5 -62.5t42.5 -79t17 -103.5q0 -169 -124.5 -265t-320.5 -96l-45 -252h-100l45 258q-175 22 -321 115z" />
<glyph unicode="%" horiz-adv-x="1882" d="M221 1079q0 103 46 200t135 161.5t200 64.5q140 0 230 -99t90 -241q0 -103 -46 -200t-135 -161.5t-200 -64.5q-140 0 -230 99t-90 241zM303 0l1256 1483h106l-1255 -1483h-107zM311 1087q0 -104 64.5 -182t169.5 -78q119 0 202.5 101.5t83.5 228.5q0 104 -64 182t-169 78 q-119 0 -203 -101.5t-84 -228.5zM1047 317q0 77 27.5 152.5t75.5 136.5t121 99t156 38q140 0 230 -99t90 -241q0 -103 -46 -200t-135 -161.5t-200 -64.5q-140 0 -229.5 99t-89.5 241zM1137 326q0 -104 64 -182t169 -78q79 0 146 48t104 123.5t37 157.5q0 104 -64.5 182 t-169.5 78q-119 0 -202.5 -101t-83.5 -228z" />
<glyph unicode="&#x26;" horiz-adv-x="1423" d="M139 338q0 134 83.5 241.5t226.5 184.5l98 53l-49 66q-49 66 -76 126.5t-27 131.5q0 150 112 257t271 107q133 0 222.5 -76.5t89.5 -207.5q0 -115 -71 -207.5t-188 -157.5l-116 -65l305 -392l229 490h125l-278 -588l231 -297l-2 -4h-141l-158 201q-71 -102 -187 -163 t-266 -61q-196 0 -315 99.5t-119 261.5zM260 352q0 -120 86.5 -192t235.5 -72q249 0 372 205l-342 442l-114 -61q-238 -126 -238 -322zM514 1139q0 -49 18.5 -92t61.5 -99l59 -78l121 70q197 115 197 266q0 85 -56 137t-145 52q-106 0 -181 -71.5t-75 -184.5z" />
<glyph unicode="'" horiz-adv-x="382" d="M238 985l73 535h117l-115 -535h-75z" />
<glyph unicode="(" horiz-adv-x="649" d="M154 231q0 385 143 742q127 316 346 549h129q-236 -236 -371 -578q-137 -341 -137 -704q0 -384 156 -676h-113q-153 277 -153 667z" />
<glyph unicode=")" horiz-adv-x="649" d="M-94 -436q235 235 370 577q138 343 138 705q0 384 -156 676h113q153 -277 153 -668q0 -384 -143 -741q-127 -316 -346 -549h-129z" />
<glyph unicode="*" horiz-adv-x="931" d="M184 1169l45 93l285 -113l-31 -62zM233 793l254 256l45 -39l-231 -277zM551 1167l41 326h96l-76 -326h-61zM575 1010l58 34l158 -258l-86 -53zM643 1143l322 119l12 -95l-326 -84z" />
<glyph unicode="+" horiz-adv-x="1179" d="M145 692l17 99h428l80 454h100l-80 -454h428l-16 -99h-428l-80 -454h-100l79 454h-428z" />
<glyph unicode="," horiz-adv-x="606" d="M6 -301l215 559h129l-254 -559h-90z" />
<glyph unicode="-" horiz-adv-x="722" d="M129 514l18 107h467l-18 -107h-467z" />
<glyph unicode="." horiz-adv-x="606" d="M123 76q0 44 32 75t76 31q42 0 71.5 -29t29.5 -69q0 -44 -32.5 -75.5t-76.5 -31.5q-42 0 -71 29.5t-29 69.5z" />
<glyph unicode="/" horiz-adv-x="886" d="M-66 -78l990 1639h108l-989 -1639h-109z" />
<glyph unicode="0" horiz-adv-x="1138" d="M117 487q0 153 37.5 341t105.5 337q157 340 451 340q192 0 293.5 -134.5t101.5 -375.5q0 -153 -37.5 -341t-105.5 -337q-157 -340 -451 -340q-192 0 -293.5 134.5t-101.5 375.5zM242 502q0 -93 15 -165.5t47 -128t85.5 -85t126.5 -29.5q116 0 197 70t139 198 q57 128 93 299.5t36 319.5q0 93 -15 165.5t-47 128t-85.5 85t-126.5 29.5q-215 0 -336 -269q-57 -128 -93 -299t-36 -319z" />
<glyph unicode="1" horiz-adv-x="1011" d="M272 1192l23 125l524 188l-266 -1505h-125l236 1331z" />
<glyph unicode="2" horiz-adv-x="1120" d="M-20 4l604 580q81 79 133.5 136t105 128t78 140.5t25.5 139.5q0 124 -81 192.5t-212 68.5q-174 0 -334 -107l25 139q143 84 315 84q83 0 156 -22.5t131 -67t91.5 -117t33.5 -165.5q0 -153 -91 -293t-274 -316l-422 -405l2 -4h670l-21 -115h-933z" />
<glyph unicode="3" horiz-adv-x="1073" d="M31 115l24 135q87 -73 193.5 -114.5t210.5 -41.5q158 0 264 88q111 94 111 234q0 127 -101 200.5t-248 73.5h-137l21 111h141q97 0 175 33t128 88q78 85 78 202q0 123 -85 194t-218 71q-74 0 -151 -22.5t-136 -59.5l23 131q130 65 274 65q111 0 204 -38.5t153.5 -123 t60.5 -202.5q0 -145 -90 -256q-72 -87 -215 -135v-5q113 -37 180 -120.5t67 -206.5q0 -95 -41 -177.5t-114 -142.5q-145 -119 -354 -119q-116 0 -226.5 38t-191.5 100z" />
<glyph unicode="4" horiz-adv-x="1271" d="M31 395l954 1088h123l-172 -981h297l-21 -111h-297l-69 -391h-125l70 391h-758zM266 506l2 -4h543l68 381q57 323 71 399l-4 2q-94 -110 -309 -356z" />
<glyph unicode="5" horiz-adv-x="1101" d="M49 106l33 130q188 -142 393 -142q177 0 279 96q98 91 98 232q0 199 -225 287q-148 59 -406 59l182 715h658l-29 -115h-534l-127 -498q220 -10 377 -88q229 -116 229 -354q0 -95 -37 -180t-106 -146q-140 -125 -365 -125q-235 0 -420 129z" />
<glyph unicode="6" horiz-adv-x="1171" d="M98 410q0 72 19 142t58.5 140t75.5 122t95 128l420 541h152l-455 -580q-44 -55 -88 -119l2 -2q48 37 115.5 59.5t138.5 22.5q184 0 308 -112t124 -293q0 -99 -39.5 -187.5t-110.5 -154.5q-147 -140 -362 -140q-125 0 -226.5 49t-164 149t-62.5 235zM225 403 q0 -134 90 -223.5t242 -89.5q160 0 272 109q109 106 109 254q0 134 -92 218.5t-238 84.5q-155 0 -270 -103q-113 -107 -113 -250z" />
<glyph unicode="7" horiz-adv-x="1085" d="M156 0l825 1368h-735l20 115h916l2 -4l-891 -1479h-137z" />
<glyph unicode="8" horiz-adv-x="1224" d="M104 371q0 157 100 271t263 155v6q-85 41 -135.5 118t-50.5 177q0 82 38.5 160.5t112.5 138.5q137 108 324 108q84 0 158.5 -24t132 -67.5t91 -111.5t33.5 -151q0 -133 -82.5 -235t-220.5 -140v-4q103 -45 163.5 -134.5t60.5 -203.5q0 -96 -43 -182.5t-117 -149.5 q-144 -122 -365 -122q-129 0 -233 45t-167 135.5t-63 210.5zM229 379q0 -129 93.5 -209t246.5 -80q170 0 281 96q117 100 117 246q0 130 -99.5 214.5t-246.5 84.5q-88 0 -165.5 -32.5t-131.5 -88.5q-95 -98 -95 -231zM401 1092q0 -118 91.5 -190.5t214.5 -72.5q147 0 251 97 q93 86 93 207q0 122 -87.5 191t-213.5 69q-142 0 -240 -82q-109 -88 -109 -219z" />
<glyph unicode="9" horiz-adv-x="1144" d="M170 1024q0 203 152 346q68 64 160 99.5t192 35.5q120 0 221.5 -48.5t165 -144t63.5 -220.5q0 -73 -18 -142.5t-56 -139t-76 -123.5t-97 -130l-435 -557h-151l469 596q52 66 74 96l-5 4q-110 -75 -247 -75q-109 0 -202 47.5t-151.5 141t-58.5 214.5zM295 1034 q0 -137 90 -221t229 -84q152 0 269 105q112 103 112 249q0 133 -91.5 221.5t-237.5 88.5q-147 0 -260 -101q-111 -105 -111 -258z" />
<glyph unicode=":" horiz-adv-x="606" d="M125 76q0 44 31 74t75 30q43 0 71 -28t28 -70q0 -44 -31.5 -74.5t-75.5 -30.5q-42 0 -70 28.5t-28 70.5zM276 926q0 44 31 74t76 30q42 0 70 -28t28 -70q0 -44 -31 -74.5t-75 -30.5q-42 0 -70.5 28.5t-28.5 70.5z" />
<glyph unicode=";" horiz-adv-x="606" d="M6 -301l215 559h129l-254 -559h-90zM276 926q0 44 31 74t76 30q42 0 70 -28t28 -70q0 -44 -31 -74.5t-75 -30.5q-42 0 -70.5 28.5t-28.5 70.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1355" d="M223 705l13 73l1062 447l-18 -109l-891 -375v-4l756 -377l-19 -104z" />
<glyph unicode="=" horiz-adv-x="1179" d="M111 492l16 98h956l-16 -98h-956zM182 893l17 98h956l-16 -98h-957z" />
<glyph unicode="&#x3e;" horiz-adv-x="1355" d="M143 256l19 109l891 374v4l-756 377l18 105l904 -449l-13 -74z" />
<glyph unicode="?" horiz-adv-x="958" d="M229 74q0 42 31 72t74 30q41 0 68.5 -28t27.5 -68q0 -43 -30.5 -73t-73.5 -30q-41 0 -69 28.5t-28 68.5zM276 1331l23 131q100 43 219 43q127 0 232 -51t168 -148t63 -223q0 -192 -140 -316t-352 -142l-53 -240h-110l59 342q126 0 230.5 37t172.5 118.5t68 194.5 q0 139 -97 225.5t-247 86.5q-120 0 -236 -58z" />
<glyph unicode="@" horiz-adv-x="1638" d="M84 436q0 161 60 312t163.5 266t253.5 184.5t320 69.5q194 0 352 -87.5t248 -243.5t90 -349q0 -93 -22 -178.5t-63.5 -152.5t-106.5 -106.5t-144 -39.5q-72 0 -129 32.5t-82 96.5h-4q-61 -64 -136 -97.5t-151 -33.5q-129 0 -210 89t-81 230q0 86 32 169.5t88.5 151 t142.5 109t184 41.5q150 0 287 -94l-70 -395q-10 -58 -10 -84q0 -66 38.5 -104t104.5 -38q82 0 142 58t87 145.5t27 190.5q0 170 -76 310t-217.5 223t-320.5 83q-150 0 -284.5 -60.5t-229.5 -162t-151 -240t-56 -289.5q0 -276 172.5 -452t443.5 -176q214 0 391 118l33 -63 q-190 -129 -426 -129q-146 0 -273.5 52t-219 143t-144.5 221t-53 280zM537 430q0 -107 58 -172t155 -65q72 0 141.5 36t116.5 97q0 36 10 98l59 338q-96 53 -186 53q-103 0 -186 -58t-125.5 -145t-42.5 -182z" />
<glyph unicode="A" horiz-adv-x="1380" d="M-66 0l867 1483h121l346 -1483h-129l-101 442h-714l-254 -442h-136zM391 557h621l-90 395q-31 135 -84 375h-4q-140 -250 -211 -371z" />
<glyph unicode="B" horiz-adv-x="1265" d="M94 0l262 1483h375q241 0 361 -86q129 -95 129 -264q0 -135 -84.5 -237t-223.5 -134v-4q116 -34 182 -120t66 -204q0 -89 -37.5 -170.5t-103.5 -140.5q-138 -123 -436 -123h-490zM240 115h364q236 0 340 98q43 40 67.5 98t24.5 121q0 125 -96 201t-248 76h-348zM362 813 h381q65 0 126.5 18t113 53t82.5 93t31 131q0 125 -95 195q-85 65 -286 65h-254z" />
<glyph unicode="C" horiz-adv-x="1372" d="M141 625q0 174 66 344.5t194 298.5q234 237 592 237q210 0 402 -92l-23 -131q-180 107 -389 107q-297 0 -494 -197q-108 -108 -165.5 -255.5t-57.5 -299.5q0 -249 153.5 -396t430.5 -147q197 0 362 80l-24 -135q-153 -62 -348 -62q-154 0 -283 43.5t-221 125.5 t-143.5 204.5t-51.5 274.5z" />
<glyph unicode="D" horiz-adv-x="1488" d="M94 0l262 1483h338q361 0 561 -197q92 -92 139.5 -212.5t47.5 -256.5q0 -146 -54 -293.5t-163 -263.5q-241 -260 -672 -260h-459zM240 115h321q378 0 582 229q82 94 128 216t46 247q0 112 -39.5 215t-114.5 178q-165 168 -481 168h-221z" />
<glyph unicode="E" horiz-adv-x="1128" d="M94 0l262 1483h820l-21 -115h-694l-96 -541h606l-21 -114h-606l-104 -598h712l-20 -115h-838z" />
<glyph unicode="F" horiz-adv-x="1134" d="M94 0l262 1483h830l-21 -115h-704l-101 -569h627l-20 -115h-627l-121 -684h-125z" />
<glyph unicode="G" horiz-adv-x="1488" d="M137 629q0 183 73 359.5t210 303.5q232 213 565 213q227 0 449 -112l-23 -131q-198 127 -434 127q-273 0 -471 -179q-117 -108 -180.5 -260t-63.5 -311q0 -247 149 -397t410 -150q170 0 314 60l79 444h-360l20 115h484l-113 -643q-89 -41 -203.5 -66t-228.5 -25 q-199 0 -351.5 77t-238.5 225.5t-86 349.5z" />
<glyph unicode="H" horiz-adv-x="1492" d="M94 0l262 1483h125l-114 -656h876l115 656h125l-262 -1483h-125l125 713h-877l-125 -713h-125z" />
<glyph unicode="I" horiz-adv-x="491" d="M94 0l262 1483h125l-262 -1483h-125z" />
<glyph unicode="J" horiz-adv-x="776" d="M-27 29l21 121q120 -60 213 -60q59 0 101 20.5t68 61.5t41 87t27 114l197 1110h125l-199 -1125q-34 -184 -94 -260q-93 -121 -270 -121q-121 0 -230 52z" />
<glyph unicode="K" horiz-adv-x="1245" d="M94 0l262 1483h125l-119 -668h5l817 668h164l-844 -688l624 -795h-153l-613 786h-4l-139 -786h-125z" />
<glyph unicode="L" horiz-adv-x="1099" d="M94 0l262 1483h125l-241 -1368h735l-21 -115h-860z" />
<glyph unicode="M" horiz-adv-x="1734" d="M94 0l262 1483h111l420 -828l708 828h129l-262 -1483h-125l152 862q50 284 76 416h-4q-116 -137 -273 -322l-432 -503l-248 489q-104 207 -166 336h-4q-45 -274 -71 -416l-152 -862h-121z" />
<glyph unicode="N" horiz-adv-x="1544" d="M94 0l262 1483h115l479 -867q110 -196 232 -423h4q77 439 86 491l141 799h121l-262 -1483h-115l-479 866q-95 169 -232 424h-4q-77 -439 -86 -491l-141 -799h-121z" />
<glyph unicode="O" d="M141 664q0 150 50 296.5t145 264.5q106 131 256.5 205.5t327.5 74.5q140 0 262.5 -49.5t211.5 -138t140 -217.5t51 -281q0 -151 -49.5 -297t-144.5 -264q-106 -131 -256.5 -206t-327.5 -75q-140 0 -263 49.5t-212 138t-140 218t-51 281.5zM266 672q0 -122 39 -228 t109 -183.5t173 -122t226 -44.5q145 0 267.5 61t211.5 173q80 99 124 225t44 258q0 122 -39 228t-109 183.5t-173 122t-226 44.5q-145 0 -267.5 -61t-211.5 -173q-80 -99 -124 -225t-44 -258z" />
<glyph unicode="P" horiz-adv-x="1245" d="M94 0l262 1483h379q280 0 412 -123q115 -106 115 -273q0 -89 -35 -175t-105 -152q-74 -71 -182.5 -106.5t-269.5 -35.5h-342l-109 -618h-125zM348 729h338q129 0 214 26t140 83q97 100 97 237q0 123 -82 199q-100 94 -322 94h-272z" />
<glyph unicode="Q" d="M141 664q0 150 50 296.5t145 264.5q106 131 256.5 205.5t327.5 74.5q140 0 262.5 -49.5t211.5 -138t140 -217.5t51 -281q0 -151 -49.5 -297t-144.5 -264q-89 -110 -212 -181t-264 -91v-4q242 -165 506 -252l-131 -74q-302 116 -559 326q-259 25 -424.5 211.5 t-165.5 470.5zM266 672q0 -122 39 -228t109 -183.5t173 -122t226 -44.5q145 0 267.5 61t211.5 173q80 99 124 225t44 258q0 122 -39 228t-109 183.5t-173 122t-226 44.5q-145 0 -267.5 -61t-211.5 -173q-80 -99 -124 -225t-44 -258z" />
<glyph unicode="R" horiz-adv-x="1269" d="M94 0l262 1483h381q264 0 389 -111q115 -100 115 -268q0 -82 -32 -162.5t-97 -142.5q-114 -107 -313 -129l321 -670h-139l-313 664h-332l-117 -664h-125zM356 770h342q223 0 328 102q90 90 90 220q0 122 -86 196q-99 80 -305 80h-264z" />
<glyph unicode="S" horiz-adv-x="1110" d="M61 135l25 144q89 -85 206 -135t234 -50q155 0 250 80q48 40 75.5 99.5t27.5 127.5q0 35 -8.5 65t-20 52t-37 45.5t-44.5 38t-58.5 35t-63.5 31.5t-74 34q-51 23 -77.5 35.5t-70 36.5t-67 42.5t-53 48t-44.5 59.5t-25.5 70.5t-10.5 86.5q0 178 127 299q131 125 340 125 q107 0 217 -35t199 -100l-25 -135q-87 71 -193 112.5t-208 41.5q-149 0 -240 -82q-92 -82 -92 -211q0 -24 3 -45t12 -40t16.5 -34t24.5 -31t28 -27t36 -26t38.5 -23t45 -23t47.5 -22.5t52 -23.5q49 -22 79.5 -36.5t72 -37.5t66.5 -43.5t53.5 -50t43.5 -60.5t25.5 -71.5 t10.5 -87.5q0 -95 -39.5 -180t-112.5 -146q-132 -111 -332 -111q-124 0 -245.5 42.5t-213.5 115.5z" />
<glyph unicode="T" horiz-adv-x="1306" d="M242 1368l20 115h1129l-21 -115h-502l-241 -1368h-125l241 1368h-501z" />
<glyph unicode="U" horiz-adv-x="1454" d="M172 449q0 104 29 262l135 772h125l-135 -772q-27 -153 -27 -246q0 -176 93.5 -273.5t285.5 -97.5q254 0 383 178q87 119 141 414l141 797h121l-143 -809q-32 -179 -71.5 -289t-96.5 -186q-167 -222 -483 -222q-240 0 -369 127t-129 345z" />
<glyph unicode="V" horiz-adv-x="1302" d="M197 1483h129l174 -858q46 -225 90 -459h4q96 181 252 463l471 854h135l-823 -1483h-127z" />
<glyph unicode="W" horiz-adv-x="2023" d="M199 1483h129l114 -840q44 -330 58 -455h4q115 244 221 459l412 836h92l121 -856q40 -290 57 -439h4q43 93 207 435l420 860h133l-725 -1483h-129l-121 856q-30 214 -49 379h-4q-95 -202 -180 -375l-424 -860h-133z" />
<glyph unicode="X" horiz-adv-x="1312" d="M-51 0l678 786l-353 697h138l153 -307q131 -264 150 -304h4q174 210 239 287l277 324h149l-598 -699l402 -784h-139l-197 387q-92 182 -156 311h-4q-129 -156 -248 -295l-346 -403h-149z" />
<glyph unicode="Y" horiz-adv-x="1292" d="M195 1483h139l190 -371q124 -241 168 -332h4q60 73 281 332l319 371h148l-703 -811l-118 -672h-125l118 670z" />
<glyph unicode="Z" horiz-adv-x="1183" d="M-55 4l1116 1360l-2 4h-823l20 115h1051l2 -4l-1116 -1360l2 -4h866l-21 -115h-1093z" />
<glyph unicode="[" horiz-adv-x="653" d="M18 -436l347 1958h409l-16 -99h-299l-312 -1761h297l-16 -98h-410z" />
<glyph unicode="\" horiz-adv-x="888" d="M244 1561h98l387 -1639h-98z" />
<glyph unicode="]" horiz-adv-x="653" d="M-106 -436l16 98h299l311 1761h-297l17 99h409l-346 -1958h-409z" />
<glyph unicode="^" horiz-adv-x="1177" d="M203 741l516 764h92l248 -764h-103l-208 633h-3l-434 -633h-108z" />
<glyph unicode="_" horiz-adv-x="927" d="M-139 -274l14 75h928l-15 -75h-927z" />
<glyph unicode="`" horiz-adv-x="983" d="M469 1489h131l117 -336h-92z" />
<glyph unicode="a" horiz-adv-x="1193" d="M100 403q0 121 45.5 244t135.5 215q159 168 399 168q201 0 405 -125l-159 -905h-121l20 117l-4 2q-158 -142 -329 -142q-164 0 -278 115t-114 311zM221 412q0 -154 83 -241t208 -87q171 0 338 170l102 586q-131 82 -282 82q-186 0 -310 -136q-67 -76 -103 -175.5 t-36 -198.5z" />
<glyph unicode="b" horiz-adv-x="1179" d="M98 45l269 1520h120l-112 -637l4 -2q139 104 297 104q173 0 289 -120t116 -318q0 -115 -43.5 -232.5t-128.5 -205.5q-169 -177 -454 -177q-194 0 -357 68zM233 127q121 -43 238 -43q225 0 356 143q62 68 98 164.5t36 192.5q0 153 -85.5 245.5t-220.5 92.5 q-154 0 -303 -121z" />
<glyph unicode="c" horiz-adv-x="1028" d="M102 424q0 123 48.5 247t146.5 214q161 145 389 145q160 0 291 -69l-21 -121q-138 80 -276 80q-180 0 -301 -115q-72 -68 -114 -168t-42 -205q0 -158 93 -251t259 -93q72 0 149.5 19t135.5 51l-22 -125q-115 -56 -273 -56q-202 0 -332.5 119t-130.5 328z" />
<glyph unicode="d" horiz-adv-x="1191" d="M100 406q0 121 44 240t128 210q80 87 183 130.5t217 43.5q165 0 295 -86l4 2l108 619h121l-276 -1565h-121l20 117l-4 2q-153 -142 -327 -142q-165 0 -278.5 115.5t-113.5 313.5zM221 416q0 -155 82 -243.5t209 -88.5q168 0 336 168l102 579q-129 93 -282 93 q-191 0 -316 -144q-62 -73 -96.5 -169.5t-34.5 -194.5z" />
<glyph unicode="e" horiz-adv-x="1126" d="M100 436q0 108 37.5 214.5t104.5 189.5q75 92 177 141t216 49q179 0 289 -114t110 -302q0 -78 -16 -143h-799v-29q0 -168 105 -262t272 -94q150 0 303 80l-22 -125q-125 -64 -291 -64q-133 0 -241.5 50.5t-176.5 156.5t-68 252zM233 571h687q2 18 2 39q0 135 -74 223.5 t-219 88.5q-181 0 -301 -152q-69 -90 -95 -199z" />
<glyph unicode="f" horiz-adv-x="661" d="M121 901l18 107h170l49 274q31 165 113 234q79 71 215 71q73 0 156 -22l-19 -113q-77 25 -139 25q-87 0 -135 -45q-52 -46 -74 -170l-45 -254h277l-19 -107h-276l-160 -901h-121l160 901h-170z" />
<glyph unicode="g" horiz-adv-x="1179" d="M41 -381l22 125q162 -106 338 -106q146 0 248 81t133 248l27 146l-4 2q-143 -115 -301 -115q-173 0 -288.5 115t-115.5 309q0 119 44.5 236.5t129.5 203.5q160 166 404 166q202 0 395 -110l-168 -951q-21 -121 -70 -209t-118 -138t-148.5 -72.5t-173.5 -22.5 q-194 0 -354 92zM221 432q0 -151 84 -238.5t219 -87.5q156 0 307 130l109 616q-123 70 -272 70q-187 0 -312 -134q-65 -71 -100 -165.5t-35 -190.5z" />
<glyph unicode="h" horiz-adv-x="1202" d="M90 0l277 1565h120l-122 -699l4 -2q200 166 393 166q135 0 216 -79.5t81 -217.5q0 -45 -19 -162l-100 -571h-121l101 567q18 99 18 148q0 93 -51.5 148t-155.5 55q-96 0 -191 -50.5t-202 -144.5l-127 -723h-121z" />
<glyph unicode="i" horiz-adv-x="479" d="M90 0l178 1008h121l-178 -1008h-121zM297 1354q0 41 29 69.5t69 28.5q38 0 63 -25.5t25 -62.5q0 -41 -29 -69.5t-69 -28.5q-38 0 -63 25.5t-25 62.5z" />
<glyph unicode="j" horiz-adv-x="479" d="M-252 -451l19 111q81 -22 122 -22q81 0 119.5 49.5t57.5 150.5l206 1170h121l-209 -1184q-26 -142 -82 -205q-79 -92 -217 -92q-76 0 -137 22zM301 1354q0 41 29 69.5t69 28.5q38 0 63 -25.5t25 -62.5q0 -41 -29 -69.5t-69 -28.5q-38 0 -63 25.5t-25 62.5z" />
<glyph unicode="k" horiz-adv-x="1034" d="M90 0l277 1565h120l-174 -985h4l594 428h170l-628 -451l465 -557h-152l-455 549h-4l-96 -549h-121z" />
<glyph unicode="l" horiz-adv-x="479" d="M90 0l277 1565h120l-276 -1565h-121z" />
<glyph unicode="m" horiz-adv-x="1896" d="M90 0l178 1008h121l-27 -150l5 -2q197 174 385 174q107 0 182 -54t98 -149q225 203 426 203q133 0 213 -77t80 -208q0 -55 -18 -157l-105 -588h-121l103 582q16 91 16 143q0 88 -49.5 140.5t-146.5 52.5q-175 0 -388 -199q-3 -50 -18 -131l-104 -588h-121l102 582 q17 97 17 143q0 88 -50 140.5t-147 52.5q-95 0 -187.5 -51.5t-195.5 -147.5l-127 -719h-121z" />
<glyph unicode="n" horiz-adv-x="1204" d="M90 0l178 1008h121l-24 -142l4 -2q200 166 393 166q135 0 216 -79.5t81 -217.5q0 -45 -19 -162l-100 -571h-121l101 567q18 99 18 148q0 93 -51.5 148t-155.5 55q-96 0 -191 -50.5t-202 -144.5l-127 -723h-121z" />
<glyph unicode="o" horiz-adv-x="1208" d="M100 446q0 111 41 224t125 202q152 158 375 158q207 0 337 -130t130 -339q0 -111 -41 -224t-125 -202q-152 -158 -375 -158q-207 0 -337 130t-130 339zM221 455q0 -160 94 -263.5t256 -103.5q170 0 283 119q63 66 98 158.5t35 187.5q0 160 -94 263.5t-256 103.5 q-170 0 -283 -119q-63 -66 -98 -158.5t-35 -187.5z" />
<glyph unicode="p" horiz-adv-x="1181" d="M10 -451l258 1459h121l-14 -80l4 -2q137 104 293 104q178 0 294.5 -124t116.5 -326q0 -119 -43 -235.5t-125 -201.5q-156 -166 -403 -166q-164 0 -289 78l-4 -2l-88 -504h-121zM240 168q127 -84 280 -84q190 0 311 133q62 68 97 163t35 193q0 157 -87 253t-227 96 q-161 0 -297 -119z" />
<glyph unicode="q" horiz-adv-x="1185" d="M100 414q0 119 45 239t131 207q167 170 433 170q196 0 374 -88l-245 -1393h-121l94 533l-4 2q-130 -107 -301 -107q-173 0 -289.5 119.5t-116.5 317.5zM221 420q0 -154 85 -244t218 -90q163 0 310 131l114 651q-122 56 -252 56q-207 0 -338 -140q-64 -72 -100.5 -169.5 t-36.5 -194.5z" />
<glyph unicode="r" horiz-adv-x="737" d="M90 0l178 1008h121l-29 -158l2 -2q69 75 160 122.5t185 47.5q35 0 59 -6l-21 -119q-24 4 -57 4q-188 0 -354 -195l-123 -702h-121z" />
<glyph unicode="s" horiz-adv-x="964" d="M39 98l22 129q75 -66 183 -103.5t215 -37.5q118 0 188 51q72 54 72 142q0 34 -13.5 60.5t-34.5 44t-53 31t-63.5 23t-72.5 19.5q-11 3 -17 4q-42 11 -70 18.5t-65 20.5t-61 26.5t-50 33.5t-41 42.5t-25 53.5t-10 67q0 68 30 129t85 102q102 76 266 76q209 0 371 -102 l-21 -117q-75 52 -172 81.5t-194 29.5q-111 0 -174 -48q-70 -53 -70 -137q0 -50 35 -83.5t70.5 -46t120.5 -34.5q20 -5 30 -8q56 -14 95.5 -27t85.5 -35.5t73.5 -49t46.5 -67.5t19 -91q0 -69 -32 -135.5t-93 -110.5q-100 -72 -262 -72q-112 0 -223.5 32.5t-190.5 88.5z" />
<glyph unicode="t" horiz-adv-x="749" d="M115 901l18 107h199l51 288l121 7l-51 -295h325l-18 -107h-326l-90 -512q-16 -94 -16 -135q0 -81 44.5 -123.5t123.5 -42.5q61 0 122 23l-20 -115q-63 -19 -119 -19q-117 0 -194.5 66t-77.5 193q0 58 16 149l90 516h-198z" />
<glyph unicode="u" horiz-adv-x="1198" d="M139 270q0 61 19 172l100 566h119l-98 -562q-17 -100 -17 -155q0 -92 50.5 -147.5t152.5 -55.5q179 0 395 199l127 721h121l-178 -1008h-121l25 143l-5 2q-203 -168 -387 -168q-136 0 -219.5 78.5t-83.5 214.5z" />
<glyph unicode="v" horiz-adv-x="1091" d="M152 1008h129l114 -480q30 -122 86 -376h4q64 113 220 376l284 480h129l-600 -1008h-121z" />
<glyph unicode="w" horiz-adv-x="1654" d="M150 1008h129l88 -504q40 -234 55 -338h4q22 42 176 332l275 510h84l94 -510q46 -253 59 -332h4q14 28 176 344l260 498h129l-534 -1008h-119l-92 498q-38 209 -51 297h-4q-68 -130 -164 -308l-262 -487h-125z" />
<glyph unicode="x" horiz-adv-x="989" d="M-78 0l498 526l-287 482h137l103 -174q71 -120 135 -234h4q126 139 219 238l160 170h143l-456 -484l311 -524h-137l-117 199q-133 229 -146 252h-4q-68 -76 -221 -240l-198 -211h-144z" />
<glyph unicode="y" horiz-adv-x="1091" d="M41 -451l375 566l-269 893h129l115 -385q60 -201 109 -379h4q89 141 239 374l252 390h131l-950 -1459h-135z" />
<glyph unicode="z" horiz-adv-x="985" d="M-33 4l805 891l-2 4h-590l19 109h819l2 -4l-805 -891l2 -4h635l-18 -109h-865z" />
<glyph unicode="{" horiz-adv-x="706" d="M59 494l17 98h67q52 0 85.5 11.5t53.5 38t30 56.5t18 80l82 465q27 154 96.5 227.5t200.5 73.5q72 0 137 -20l-17 -94q-50 16 -114 16q-89 0 -131.5 -49.5t-63.5 -169.5l-80 -453q-17 -99 -51.5 -155.5t-107.5 -75.5v-2q86 -33 86 -144q0 -28 -13 -108l-71 -408 q-13 -73 -13 -106q0 -74 39 -104.5t119 -30.5q48 0 86 6l-16 -99q-34 -6 -82 -6q-128 0 -193 52.5t-65 166.5q0 52 12 117l76 428q8 44 8 84q0 55 -29.5 80t-107.5 25h-58z" />
<glyph unicode="|" horiz-adv-x="647" d="M104 -451l369 2089h100l-368 -2089h-101z" />
<glyph unicode="}" horiz-adv-x="706" d="M-125 -438l16 94q50 -16 115 -16q89 0 131.5 49.5t63.5 169.5l80 452q17 99 51.5 156t107.5 76v2q-86 33 -86 143q0 29 13 109l71 407q13 73 13 107q0 74 -39 104.5t-119 30.5q-49 0 -86 -6l16 98q34 6 82 6q128 0 193 -52.5t65 -166.5q0 -53 -12 -117l-76 -428 q-8 -44 -8 -84q0 -55 29 -79.5t108 -24.5h58l-17 -98h-67q-52 0 -85.5 -11.5t-53.5 -38.5t-30 -57t-18 -80l-82 -465q-27 -154 -96.5 -227.5t-200.5 -73.5q-69 0 -137 21z" />
<glyph unicode="~" horiz-adv-x="1116" d="M182 633l27 147q77 90 192 90q14 0 27.5 -1t28.5 -5t25 -6t26 -9t23 -10t25 -12.5t23 -12t25.5 -14.5t24.5 -14q60 -36 100.5 -52.5t85.5 -16.5q118 0 199 135l4 -2l-27 -148q-77 -90 -192 -90q-41 0 -84 13.5t-68 27t-76 43.5q-41 24 -64.5 36.5t-57 23t-64.5 10.5 q-118 0 -199 -135z" />
<glyph unicode="&#xa1;" horiz-adv-x="634" d="M76 -475l215 1053h78l-156 -1053h-137zM293 928q0 43 30.5 72.5t73.5 29.5q41 0 69 -28t28 -68q0 -43 -31 -73t-74 -30q-41 0 -68.5 28.5t-27.5 68.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1048" d="M137 662q0 122 48.5 246t146.5 214q139 122 319 142l47 256h101l-45 -254q140 -6 258 -68l-21 -121q-138 80 -276 80q-180 0 -301 -115q-72 -68 -114 -167.5t-42 -204.5q0 -158 93 -251t259 -93q73 0 150 18.5t135 50.5l-23 -125q-119 -58 -305 -55l-45 -252h-100l47 266 q-151 35 -241.5 147.5t-90.5 285.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1122" d="M59 0l17 98q127 96 196.5 226.5t69.5 287.5q0 34 -2 50h-164l19 102h135q-3 20 -9.5 64.5t-10 69.5t-6.5 61t-3 65q0 218 134.5 349.5t348.5 131.5q93 0 183 -28t153 -72l-22 -133q-149 117 -318 117q-163 0 -258.5 -98t-95.5 -263q0 -27 3 -62.5t6.5 -62t10 -71 t9.5 -68.5h393l-19 -102h-364q2 -16 2 -46q0 -152 -59.5 -281t-159.5 -216l2 -4h715l-21 -115h-885z" />
<glyph unicode="&#xa4;" horiz-adv-x="1196" d="M74 299l188 164q-55 93 -55 213q0 185 127 325l-115 146l90 70l111 -140q113 76 248 76q165 0 276 -102l193 166l63 -82l-188 -164q55 -93 55 -213q0 -186 -127 -326l115 -145l-90 -70l-111 139q-112 -75 -248 -75q-165 0 -276 102l-193 -166zM307 682 q0 -130 85.5 -216.5t219.5 -86.5q148 0 251.5 109t103.5 264q0 130 -85.5 216.5t-219.5 86.5q-148 0 -251.5 -109t-103.5 -264z" />
<glyph unicode="&#xa5;" horiz-adv-x="1329" d="M180 473l19 100h417l31 170l-59 115h-338l16 98h271l-273 527h139l156 -303q137 -272 164 -326h4q54 65 279 330l258 299h147l-457 -527h273l-17 -98h-342l-96 -113l-31 -172h420l-18 -100h-418l-84 -473h-125l84 473h-420z" />
<glyph unicode="&#xa6;" horiz-adv-x="647" d="M104 -451l142 803h100l-141 -803h-101zM332 836l141 802h100l-141 -802h-100z" />
<glyph unicode="&#xa7;" horiz-adv-x="1122" d="M111 -215l22 123q67 -53 151 -85t167 -32q114 0 189 59t75 162q0 28 -7 53t-16 43.5t-32 40t-38.5 34.5t-53.5 35.5t-59.5 34t-72.5 38.5q-49 25 -76.5 41t-67.5 41.5t-63 49.5t-46 56t-33.5 70t-10.5 82q0 121 78 206.5t201 114.5v4q-94 87 -94 209q0 141 105.5 235.5 t268.5 94.5q158 0 306 -88l-21 -117q-61 42 -138 68t-149 26q-106 0 -180 -56t-74 -157q0 -36 13 -66t31 -52.5t58.5 -50t73 -45.5t97.5 -52q60 -30 97.5 -52t81.5 -55.5t68.5 -66.5t41 -78.5t16.5 -99.5q0 -123 -72 -209.5t-196 -114.5v-4q82 -82 82 -205q0 -151 -109 -245 t-276 -94q-182 0 -338 104zM258 645q0 -29 7.5 -54.5t17 -45t31.5 -41t38.5 -35t50.5 -33.5t55 -31.5t64 -33.5q12 -6 58 -31.5t69 -38.5q108 11 180 75.5t72 164.5q0 41 -15 77t-35.5 61t-62 54t-72.5 46.5t-89 47.5q-12 6 -101 54q-113 -12 -190.5 -71t-77.5 -165z" />
<glyph unicode="&#xa8;" horiz-adv-x="983" d="M365 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -36 -27 -62t-64 -26q-34 0 -56.5 23t-22.5 57zM737 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57z" />
<glyph unicode="&#xa9;" horiz-adv-x="1675" d="M125 741q0 158 57.5 299.5t157 243.5t240.5 161.5t301 59.5q213 0 386.5 -101.5t271 -276t97.5 -386.5t-97.5 -386.5t-271 -276t-386.5 -101.5q-160 0 -301 59.5t-240.5 161.5t-157 243.5t-57.5 299.5zM199 741q0 -192 86 -350t243 -250t353 -92t353 92t243 250t86 350 t-86 350t-243 250.5t-353 92.5t-353 -92.5t-243 -250.5t-86 -350zM492 748q0 181 120 301t306 120q120 0 221 -53v-96q-100 65 -224 65q-147 0 -240 -95.5t-93 -239.5q0 -146 94 -239t242 -93q134 0 233 76v-99q-98 -61 -238 -61q-185 0 -303 118t-118 296z" />
<glyph unicode="&#xaa;" horiz-adv-x="792" d="M197 1106q0 75 27 147t74.5 128t119 90t153.5 34q141 0 273 -98l-101 -571h-94l10 57l-4 2q-86 -74 -192 -74q-116 0 -191 78t-75 207zM291 1114q0 -95 53 -151t141 -56q107 0 191 84l65 371q-86 57 -178 57q-117 0 -194.5 -93t-77.5 -212z" />
<glyph unicode="&#xab;" horiz-adv-x="1093" d="M94 526l414 426h119l-424 -436l266 -461h-107zM494 526l413 426h119l-424 -436l266 -461h-106z" />
<glyph unicode="&#xac;" horiz-adv-x="1179" d="M145 692l17 99h956l-98 -553h-102l79 454h-852z" />
<glyph unicode="&#xad;" horiz-adv-x="722" d="M129 514l18 107h467l-18 -107h-467z" />
<glyph unicode="&#xae;" horiz-adv-x="856" d="M156 1124q0 164 108.5 272.5t274.5 108.5t274.5 -108.5t108.5 -272.5t-108.5 -272.5t-274.5 -108.5t-274.5 108.5t-108.5 272.5zM205 1124q0 -145 94 -239t240 -94t239.5 94t93.5 239t-93.5 239.5t-239.5 94.5t-240 -94.5t-94 -239.5zM403 918v413h138q70 0 112.5 -34 t42.5 -95q0 -44 -26 -74.5t-70 -44.5l113 -165h-66l-104 157h-82v-157h-58zM461 1124h80q46 0 71 20.5t25 57.5q0 36 -25.5 57t-70.5 21h-80v-156z" />
<glyph unicode="&#xaf;" horiz-adv-x="983" d="M358 1274l17 94h538l-16 -94h-539z" />
<glyph unicode="&#xb0;" horiz-adv-x="700" d="M168 1204q0 127 87 214t218 87t218 -87t87 -214t-87 -214t-218 -87t-218 87t-87 214zM240 1204q0 -98 66.5 -164.5t166.5 -66.5q101 0 167.5 66.5t66.5 164.5t-67 165t-167 67t-166.5 -67t-66.5 -165z" />
<glyph unicode="&#xb1;" horiz-adv-x="1179" d="M82 178l16 98h903l-16 -98h-903zM205 881l16 98h402l69 393h101l-70 -393h401l-16 -98h-401l-70 -394h-100l69 394h-401z" />
<glyph unicode="&#xb2;" horiz-adv-x="720" d="M104 868l385 361q100 95 148.5 163t48.5 140q0 71 -48 107t-128 36q-54 0 -110 -21.5t-99 -56.5l21 115q88 55 200 55q113 0 189.5 -59.5t76.5 -169.5q0 -92 -52.5 -172.5t-157.5 -179.5l-242 -225l2 -5h387l-16 -92h-603z" />
<glyph unicode="&#xb3;" horiz-adv-x="688" d="M154 938l18 109q51 -48 115.5 -76.5t130.5 -28.5q93 0 155 49q64 50 64 127q0 70 -59.5 110t-147.5 40h-84l14 84h89q111 0 178 67q47 50 47 109q0 68 -51.5 107.5t-130.5 39.5q-100 0 -179 -49l19 100q76 39 170 39q70 0 130 -22t101 -73.5t41 -123.5q0 -89 -55 -151 q-50 -55 -133 -80v-4q68 -23 110.5 -73t42.5 -122q0 -112 -102 -194q-92 -72 -225 -72q-153 0 -258 88z" />
<glyph unicode="&#xb4;" horiz-adv-x="983" d="M494 1153l235 336h135l-274 -336h-96z" />
<glyph unicode="&#xb5;" horiz-adv-x="1245" d="M219 -451v1459h121v-586q0 -187 63 -258q67 -76 191 -76q189 0 364 186v734h121v-1008h-121v143l-4 2q-175 -168 -383 -168q-70 0 -132.5 21.5t-100.5 62.5l-4 -2l12 -510h-127z" />
<glyph unicode="&#xb6;" horiz-adv-x="1116" d="M231 1065q0 104 56 200.5t153 157t207 60.5h406l-340 -1934h-103l324 1837h-182l-324 -1837h-102l211 1192q-134 7 -220 99t-86 225z" />
<glyph unicode="&#xb7;" horiz-adv-x="606" d="M219 614q0 44 32.5 75.5t76.5 31.5q42 0 71 -29t29 -69q0 -44 -32.5 -75.5t-76.5 -31.5q-42 0 -71 29t-29 69z" />
<glyph unicode="&#xb8;" horiz-adv-x="983" d="M182 -401l35 71q75 -49 133 -49q46 0 82.5 26t36.5 72q0 93 -168 123l98 203l64 -22l-62 -129q152 -44 152 -166q0 -77 -57.5 -129t-145.5 -52q-84 0 -168 52z" />
<glyph unicode="&#xb9;" horiz-adv-x="645" d="M291 1556l16 97l346 114l-164 -903h-98l135 770z" />
<glyph unicode="&#xba;" horiz-adv-x="841" d="M201 1128q0 156 100 266.5t256 110.5q135 0 224 -84t89 -223q0 -156 -100 -266.5t-256 -110.5q-135 0 -224 84t-89 223zM295 1137q0 -99 59.5 -164.5t161.5 -65.5q110 0 185 82t75 201q0 99 -59 164t-162 65q-110 0 -185 -81.5t-75 -200.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1093" d="M68 55l424 437l-267 460h107l268 -471l-414 -426h-118zM467 55l424 437l-266 460h106l268 -471l-413 -426h-119z" />
<glyph unicode="&#xbc;" horiz-adv-x="1867" d="M262 -27l1237 1510h119l-1237 -1510h-119zM262 1294l17 97l346 114l-164 -903h-99l136 770zM973 229l594 660h96l-102 -580h172l-15 -84h-172l-39 -225h-98l39 225h-473zM1157 313l2 -4h303l35 199q14 76 43 227l-4 2q-54 -62 -156 -176z" />
<glyph unicode="&#xbd;" horiz-adv-x="1959" d="M262 -27l1237 1510h119l-1237 -1510h-119zM262 1294l17 97l346 114l-164 -903h-99l136 770zM1135 4l385 361q99 94 147.5 162.5t48.5 140.5q0 71 -48 107t-128 36q-54 0 -110 -21.5t-99 -56.5l21 115q88 55 200 55q113 0 190 -59.5t77 -169.5q0 -92 -53 -172t-158 -180 l-242 -226l2 -4h387l-16 -92h-602z" />
<glyph unicode="&#xbe;" horiz-adv-x="1990" d="M162 676l18 108q51 -48 115.5 -76t130.5 -28q94 0 156 49q63 49 63 127q0 70 -59.5 110t-147.5 40h-84l15 84h88q111 0 178 67q47 50 47 109q0 68 -51.5 107.5t-130.5 39.5q-99 0 -178 -49l18 100q76 39 170 39q70 0 130 -22t101 -73.5t41 -123.5q0 -89 -55 -151 q-50 -55 -133 -80v-4q69 -23 111.5 -73t42.5 -122q0 -112 -103 -195q-91 -71 -225 -71q-153 0 -258 88zM385 -27l1237 1510h119l-1237 -1510h-119zM1096 229l594 660h96l-103 -580h172l-14 -84h-172l-39 -225h-98l39 225h-473zM1280 313l2 -4h303l35 199q14 76 43 227l-4 2 q-54 -62 -156 -176z" />
<glyph unicode="&#xbf;" horiz-adv-x="958" d="M-14 -76q0 128 66 230t176.5 159.5t248.5 69.5l53 240h111l-59 -342q-93 0 -176.5 -22t-150 -64.5t-105.5 -110.5t-39 -154q0 -139 97 -225t247 -86q121 0 235 57l-22 -131q-100 -43 -219 -43q-127 0 -232 51t-168 148t-63 223zM537 928q0 43 30.5 72.5t73.5 29.5 q41 0 68.5 -28t27.5 -68q0 -43 -30.5 -73t-73.5 -30q-41 0 -68.5 28.5t-27.5 68.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1380" d="M-66 0l867 1483h121l346 -1483h-129l-101 442h-714l-254 -442h-136zM391 557h621l-90 395q-31 135 -84 375h-4q-140 -250 -211 -371zM752 1964h131l116 -336h-92z" />
<glyph unicode="&#xc1;" horiz-adv-x="1380" d="M-66 0l867 1483h121l346 -1483h-129l-101 442h-714l-254 -442h-136zM391 557h621l-90 395q-31 135 -84 375h-4q-140 -250 -211 -371zM776 1628l236 336h135l-275 -336h-96z" />
<glyph unicode="&#xc2;" horiz-adv-x="1380" d="M-66 0l867 1483h121l346 -1483h-129l-101 442h-714l-254 -442h-136zM391 557h621l-90 395q-31 135 -84 375h-4q-140 -250 -211 -371zM588 1628l297 336h129l178 -336h-96l-158 256l-242 -256h-108z" />
<glyph unicode="&#xc3;" horiz-adv-x="1380" d="M-66 0l867 1483h121l346 -1483h-129l-101 442h-714l-254 -442h-136zM391 557h621l-90 395q-31 135 -84 375h-4q-140 -250 -211 -371zM602 1702l21 121q25 38 67.5 60t93.5 22q37 0 71.5 -14t86.5 -44q83 -49 131 -49q92 0 160 105l4 -2l-20 -121q-25 -38 -68 -60t-94 -22 q-60 0 -158 57q-83 49 -131 49q-93 0 -160 -104z" />
<glyph unicode="&#xc4;" horiz-adv-x="1380" d="M-66 0l867 1483h121l346 -1483h-129l-101 442h-714l-254 -442h-136zM391 557h621l-90 395q-31 135 -84 375h-4q-140 -250 -211 -371zM647 1792q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM1020 1792 q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57z" />
<glyph unicode="&#xc5;" horiz-adv-x="1380" d="M-66 0l867 1483h121l346 -1483h-129l-101 442h-714l-254 -442h-136zM391 557h621l-90 395q-31 135 -84 375h-4q-140 -250 -211 -371zM698 1819q0 99 69.5 172t170.5 73q92 0 152.5 -58.5t60.5 -150.5q0 -99 -69.5 -172t-170.5 -73q-92 0 -152.5 58.5t-60.5 150.5z M776 1823q0 -58 36 -99t101 -41t112.5 48.5t47.5 119.5q0 58 -36 99t-101 41t-112.5 -48.5t-47.5 -119.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1851" d="M-61 0l1099 1483h860l-20 -115h-653l-97 -541h566l-21 -114h-565l-104 -598h671l-20 -115h-797l78 444h-528l-332 -444h-137zM489 555h467l74 418q72 394 74 405h-4q-102 -141 -262 -356z" />
<glyph unicode="&#xc7;" horiz-adv-x="1372" d="M141 625q0 174 66 344.5t194 298.5q234 237 592 237q210 0 402 -92l-23 -131q-180 107 -389 107q-297 0 -494 -197q-108 -108 -165.5 -255.5t-57.5 -299.5q0 -249 153.5 -396t430.5 -147q197 0 362 80l-24 -135q-153 -62 -348 -62q-12 0 -80 5l-41 -88q151 -44 151 -166 q0 -77 -57 -129t-145 -52q-84 0 -168 52l35 71q75 -49 133 -49q46 0 82 26t36 72q0 93 -168 123l70 148q-250 38 -398.5 204t-148.5 431z" />
<glyph unicode="&#xc8;" horiz-adv-x="1128" d="M94 0l262 1483h820l-21 -115h-694l-96 -541h606l-21 -114h-606l-104 -598h712l-20 -115h-838zM653 1964h131l117 -336h-92z" />
<glyph unicode="&#xc9;" horiz-adv-x="1128" d="M94 0l262 1483h820l-21 -115h-694l-96 -541h606l-21 -114h-606l-104 -598h712l-20 -115h-838zM678 1628l235 336h136l-275 -336h-96z" />
<glyph unicode="&#xca;" horiz-adv-x="1128" d="M94 0l262 1483h820l-21 -115h-694l-96 -541h606l-21 -114h-606l-104 -598h712l-20 -115h-838zM489 1628l297 336h129l179 -336h-97l-157 256l-242 -256h-109z" />
<glyph unicode="&#xcb;" horiz-adv-x="1128" d="M94 0l262 1483h820l-21 -115h-694l-96 -541h606l-21 -114h-606l-104 -598h712l-20 -115h-838zM549 1792q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM922 1792q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57 q0 -36 -27 -62t-64 -26q-34 0 -56.5 23t-22.5 57z" />
<glyph unicode="&#xcc;" horiz-adv-x="491" d="M94 0l262 1483h125l-262 -1483h-125zM307 1964h131l117 -336h-92z" />
<glyph unicode="&#xcd;" horiz-adv-x="491" d="M94 0l262 1483h125l-262 -1483h-125zM332 1628l235 336h135l-274 -336h-96z" />
<glyph unicode="&#xce;" horiz-adv-x="491" d="M94 0l262 1483h125l-262 -1483h-125zM143 1628l297 336h129l179 -336h-97l-157 256l-242 -256h-109z" />
<glyph unicode="&#xcf;" horiz-adv-x="491" d="M94 0l262 1483h125l-262 -1483h-125zM203 1792q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM575 1792q0 36 27 62t64 26q34 0 56.5 -23t22.5 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57z" />
<glyph unicode="&#xd0;" horiz-adv-x="1509" d="M66 705l18 104h174l119 674h338q361 0 561 -197q91 -91 138.5 -212t47.5 -257q0 -146 -54 -293.5t-163 -263.5q-241 -260 -672 -260h-458l125 705h-174zM260 115h322q377 0 581 229q82 94 128 216t46 247q0 113 -39 215.5t-114 177.5q-165 168 -482 168h-221l-98 -559 h356l-18 -104h-356z" />
<glyph unicode="&#xd1;" horiz-adv-x="1544" d="M94 0l262 1483h115l479 -867q110 -196 232 -423h4q77 439 86 491l141 799h121l-262 -1483h-115l-479 866q-95 169 -232 424h-4q-77 -439 -86 -491l-141 -799h-121zM684 1702l21 121q25 38 67.5 60t93.5 22q37 0 71.5 -14t86.5 -44q83 -49 131 -49q92 0 160 105l4 -2 l-21 -121q-25 -38 -67.5 -60t-93.5 -22q-60 0 -158 57q-83 49 -131 49q-93 0 -160 -104z" />
<glyph unicode="&#xd2;" d="M141 664q0 150 50 296.5t145 264.5q106 131 256.5 205.5t327.5 74.5q140 0 262.5 -49.5t211.5 -138t140 -217.5t51 -281q0 -151 -49.5 -297t-144.5 -264q-106 -131 -256.5 -206t-327.5 -75q-140 0 -263 49.5t-212 138t-140 218t-51 281.5zM266 672q0 -122 39 -228 t109 -183.5t173 -122t226 -44.5q145 0 267.5 61t211.5 173q80 99 124 225t44 258q0 122 -39 228t-109 183.5t-173 122t-226 44.5q-145 0 -267.5 -61t-211.5 -173q-80 -99 -124 -225t-44 -258zM883 1964h131l116 -336h-92z" />
<glyph unicode="&#xd3;" d="M141 664q0 150 50 296.5t145 264.5q106 131 256.5 205.5t327.5 74.5q140 0 262.5 -49.5t211.5 -138t140 -217.5t51 -281q0 -151 -49.5 -297t-144.5 -264q-106 -131 -256.5 -206t-327.5 -75q-140 0 -263 49.5t-212 138t-140 218t-51 281.5zM266 672q0 -122 39 -228 t109 -183.5t173 -122t226 -44.5q145 0 267.5 61t211.5 173q80 99 124 225t44 258q0 122 -39 228t-109 183.5t-173 122t-226 44.5q-145 0 -267.5 -61t-211.5 -173q-80 -99 -124 -225t-44 -258zM907 1628l236 336h135l-274 -336h-97z" />
<glyph unicode="&#xd4;" d="M141 664q0 150 50 296.5t145 264.5q106 131 256.5 205.5t327.5 74.5q140 0 262.5 -49.5t211.5 -138t140 -217.5t51 -281q0 -151 -49.5 -297t-144.5 -264q-106 -131 -256.5 -206t-327.5 -75q-140 0 -263 49.5t-212 138t-140 218t-51 281.5zM266 672q0 -122 39 -228 t109 -183.5t173 -122t226 -44.5q145 0 267.5 61t211.5 173q80 99 124 225t44 258q0 122 -39 228t-109 183.5t-173 122t-226 44.5q-145 0 -267.5 -61t-211.5 -173q-80 -99 -124 -225t-44 -258zM719 1628l297 336h129l178 -336h-96l-158 256l-242 -256h-108z" />
<glyph unicode="&#xd5;" d="M141 664q0 150 50 296.5t145 264.5q106 131 256.5 205.5t327.5 74.5q140 0 262.5 -49.5t211.5 -138t140 -217.5t51 -281q0 -151 -49.5 -297t-144.5 -264q-106 -131 -256.5 -206t-327.5 -75q-140 0 -263 49.5t-212 138t-140 218t-51 281.5zM266 672q0 -122 39 -228 t109 -183.5t173 -122t226 -44.5q145 0 267.5 61t211.5 173q80 99 124 225t44 258q0 122 -39 228t-109 183.5t-173 122t-226 44.5q-145 0 -267.5 -61t-211.5 -173q-80 -99 -124 -225t-44 -258zM733 1702l21 121q25 38 67.5 60t93.5 22q37 0 71.5 -14t86.5 -44q83 -49 131 -49 q92 0 160 105l4 -2l-20 -121q-25 -38 -68 -60t-94 -22q-60 0 -158 57q-83 49 -131 49q-93 0 -160 -104z" />
<glyph unicode="&#xd6;" d="M141 664q0 150 50 296.5t145 264.5q106 131 256.5 205.5t327.5 74.5q140 0 262.5 -49.5t211.5 -138t140 -217.5t51 -281q0 -151 -49.5 -297t-144.5 -264q-106 -131 -256.5 -206t-327.5 -75q-140 0 -263 49.5t-212 138t-140 218t-51 281.5zM266 672q0 -122 39 -228 t109 -183.5t173 -122t226 -44.5q145 0 267.5 61t211.5 173q80 99 124 225t44 258q0 122 -39 228t-109 183.5t-173 122t-226 44.5q-145 0 -267.5 -61t-211.5 -173q-80 -99 -124 -225t-44 -258zM778 1792q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5 t-63.5 -25.5q-34 0 -57 23t-23 57zM1151 1792q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57z" />
<glyph unicode="&#xd7;" horiz-adv-x="1179" d="M158 397l405 346l-280 344l77 70l281 -346l406 346l59 -74l-406 -346l281 -344l-78 -69l-280 346l-406 -346z" />
<glyph unicode="&#xd8;" d="M80 47l194 189q-133 179 -133 428q0 150 50 296.5t145 264.5q106 131 256.5 205.5t327.5 74.5q281 0 466 -182l191 184l70 -71l-195 -189q133 -179 133 -428q0 -151 -49.5 -297t-144.5 -264q-106 -131 -256.5 -206t-327.5 -75q-137 0 -257 47.5t-210 135.5l-190 -185z M266 672q0 -197 99 -348l933 911q-151 154 -385 154q-145 0 -267.5 -61t-211.5 -173q-80 -99 -124 -225t-44 -258zM428 248q151 -154 385 -154q145 0 267.5 61t211.5 173q80 99 124 225t44 258q0 199 -98 348z" />
<glyph unicode="&#xd9;" horiz-adv-x="1454" d="M172 449q0 104 29 262l135 772h125l-135 -772q-27 -153 -27 -246q0 -176 93.5 -273.5t285.5 -97.5q254 0 383 178q87 119 141 414l141 797h121l-143 -809q-32 -179 -71.5 -289t-96.5 -186q-167 -222 -483 -222q-240 0 -369 127t-129 345zM791 1964h131l116 -336h-92z" />
<glyph unicode="&#xda;" horiz-adv-x="1454" d="M172 449q0 104 29 262l135 772h125l-135 -772q-27 -153 -27 -246q0 -176 93.5 -273.5t285.5 -97.5q254 0 383 178q87 119 141 414l141 797h121l-143 -809q-32 -179 -71.5 -289t-96.5 -186q-167 -222 -483 -222q-240 0 -369 127t-129 345zM815 1628l236 336h135l-275 -336 h-96z" />
<glyph unicode="&#xdb;" horiz-adv-x="1454" d="M172 449q0 104 29 262l135 772h125l-135 -772q-27 -153 -27 -246q0 -176 93.5 -273.5t285.5 -97.5q254 0 383 178q87 119 141 414l141 797h121l-143 -809q-32 -179 -71.5 -289t-96.5 -186q-167 -222 -483 -222q-240 0 -369 127t-129 345zM627 1628l297 336h129l178 -336 h-96l-158 256l-242 -256h-108z" />
<glyph unicode="&#xdc;" horiz-adv-x="1454" d="M172 449q0 104 29 262l135 772h125l-135 -772q-27 -153 -27 -246q0 -176 93.5 -273.5t285.5 -97.5q254 0 383 178q87 119 141 414l141 797h121l-143 -809q-32 -179 -71.5 -289t-96.5 -186q-167 -222 -483 -222q-240 0 -369 127t-129 345zM686 1792q0 37 26.5 62.5 t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM1059 1792q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57z" />
<glyph unicode="&#xdd;" horiz-adv-x="1292" d="M195 1483h139l190 -371q124 -241 168 -332h4q60 73 281 332l319 371h148l-703 -811l-118 -672h-125l118 670zM735 1628l236 336h135l-275 -336h-96z" />
<glyph unicode="&#xde;" horiz-adv-x="1245" d="M94 0l262 1483h125l-53 -305h254q278 0 410 -123q116 -107 116 -275q0 -89 -34 -176t-103 -153q-73 -70 -182.5 -104t-274.5 -34h-340l-55 -313h-125zM295 424h336q135 0 216.5 26t137.5 82q98 98 98 238q0 123 -82 199q-101 94 -321 94h-272z" />
<glyph unicode="&#xdf;" horiz-adv-x="1189" d="M90 0l211 1192q38 213 156 303q118 92 319 92q198 0 291 -86q80 -73 80 -201q0 -135 -94 -294q-52 6 -95 6q-56 0 -108 -9.5t-103.5 -31.5t-82.5 -65t-31 -103q0 -26 5.5 -46.5t21 -39t29.5 -31.5t45 -30.5t53 -28t67 -31.5l55.5 -27t49.5 -25.5t46 -28t37 -30t33 -36.5 t22.5 -41t16 -50t4.5 -59q0 -72 -31.5 -139t-93.5 -111q-100 -72 -262 -72q-192 0 -350 82l22 121q148 -94 334 -94q119 0 189 51q71 54 71 148q0 34 -11 61.5t-25 45.5t-47.5 39t-57 33t-75.5 36q-9 4 -13 6q-54 25 -88 43t-70 44t-56 53t-33 63.5t-13 81.5 q0 80 36.5 143.5t98 102.5t136.5 59t157 20q31 0 49 -2q41 100 41 170q0 85 -51 133q-66 60 -207 60q-158 0 -240 -70q-80 -69 -108 -223l-209 -1184h-121z" />
<glyph unicode="&#xe0;" horiz-adv-x="1193" d="M100 403q0 121 45.5 244t135.5 215q159 168 399 168q201 0 405 -125l-159 -905h-121l20 117l-4 2q-158 -142 -329 -142q-164 0 -278 115t-114 311zM221 412q0 -154 83 -241t208 -87q171 0 338 170l102 586q-131 82 -282 82q-186 0 -310 -136q-67 -76 -103 -175.5 t-36 -198.5zM586 1489h131l117 -336h-93z" />
<glyph unicode="&#xe1;" horiz-adv-x="1193" d="M100 403q0 121 45.5 244t135.5 215q159 168 399 168q201 0 405 -125l-159 -905h-121l20 117l-4 2q-158 -142 -329 -142q-164 0 -278 115t-114 311zM221 412q0 -154 83 -241t208 -87q171 0 338 170l102 586q-131 82 -282 82q-186 0 -310 -136q-67 -76 -103 -175.5 t-36 -198.5zM608 1153l236 336h135l-274 -336h-97z" />
<glyph unicode="&#xe2;" horiz-adv-x="1193" d="M100 403q0 121 45.5 244t135.5 215q159 168 399 168q201 0 405 -125l-159 -905h-121l20 117l-4 2q-158 -142 -329 -142q-164 0 -278 115t-114 311zM221 412q0 -154 83 -241t208 -87q171 0 338 170l102 586q-131 82 -282 82q-186 0 -310 -136q-67 -76 -103 -175.5 t-36 -198.5zM420 1153l297 336h129l178 -336h-96l-158 256l-242 -256h-108z" />
<glyph unicode="&#xe3;" horiz-adv-x="1193" d="M100 403q0 121 45.5 244t135.5 215q159 168 399 168q201 0 405 -125l-159 -905h-121l20 117l-4 2q-158 -142 -329 -142q-164 0 -278 115t-114 311zM221 412q0 -154 83 -241t208 -87q171 0 338 170l102 586q-131 82 -282 82q-186 0 -310 -136q-67 -76 -103 -175.5 t-36 -198.5zM434 1227l21 121q25 38 67.5 60t93.5 22q37 0 71.5 -14t86.5 -44q83 -49 131 -49q93 0 160 104l4 -2l-20 -120q-25 -38 -68 -60t-94 -22q-60 0 -158 57q-83 49 -131 49q-93 0 -160 -104z" />
<glyph unicode="&#xe4;" horiz-adv-x="1193" d="M100 403q0 121 45.5 244t135.5 215q159 168 399 168q201 0 405 -125l-159 -905h-121l20 117l-4 2q-158 -142 -329 -142q-164 0 -278 115t-114 311zM221 412q0 -154 83 -241t208 -87q171 0 338 170l102 586q-131 82 -282 82q-186 0 -310 -136q-67 -76 -103 -175.5 t-36 -198.5zM479 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM852 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57z" />
<glyph unicode="&#xe5;" horiz-adv-x="1193" d="M100 403q0 121 45.5 244t135.5 215q159 168 399 168q201 0 405 -125l-159 -905h-121l20 117l-4 2q-158 -142 -329 -142q-164 0 -278 115t-114 311zM221 412q0 -154 83 -241t208 -87q171 0 338 170l102 586q-131 82 -282 82q-186 0 -310 -136q-67 -76 -103 -175.5 t-36 -198.5zM530 1343q0 99 69.5 172.5t170.5 73.5q92 0 152.5 -58.5t60.5 -150.5q0 -99 -69.5 -172t-170.5 -73q-92 0 -152.5 58.5t-60.5 149.5zM608 1348q0 -58 36 -99t101 -41t112.5 48.5t47.5 119.5q0 58 -36 99t-101 41t-112.5 -48.5t-47.5 -119.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1845" d="M100 416q0 117 43.5 234.5t128.5 207.5q163 172 441 172q168 0 321 -63l-20 -115l4 -2q60 84 151 132t201 48q169 0 276 -114t107 -300q0 -67 -16 -145h-779v-27q0 -169 102.5 -263.5t264.5 -94.5q153 0 293 82l-23 -127q-121 -64 -280 -64q-130 0 -237.5 51t-168.5 148 q-82 -98 -184 -148.5t-213 -50.5q-178 0 -295 120.5t-117 318.5zM221 426q0 -154 84.5 -247t224.5 -93q99 0 184.5 47.5t149.5 136.5q-22 74 -22 160q0 53 10 115l59 338q-100 41 -211 41q-218 0 -346 -140q-64 -69 -98.5 -164.5t-34.5 -193.5zM973 571h665q2 10 2 37 q0 135 -70.5 224.5t-209.5 89.5q-175 0 -293 -152q-65 -82 -94 -199z" />
<glyph unicode="&#xe7;" horiz-adv-x="1028" d="M102 424q0 123 48.5 247t146.5 214q161 145 389 145q160 0 291 -69l-21 -121q-138 80 -276 80q-180 0 -301 -115q-72 -68 -114 -168t-42 -205q0 -158 93 -251t259 -93q72 0 149.5 19t135.5 51l-22 -125q-115 -56 -273 -56h-20l-41 -86q151 -44 151 -165q0 -77 -57 -129 t-145 -52q-84 0 -168 52l34 71q75 -49 134 -49q46 0 82 26t36 72q0 93 -168 123l70 146q-167 26 -269 140t-102 298z" />
<glyph unicode="&#xe8;" horiz-adv-x="1126" d="M100 436q0 108 37.5 214.5t104.5 189.5q75 92 177 141t216 49q179 0 289 -114t110 -302q0 -78 -16 -143h-799v-29q0 -168 105 -262t272 -94q150 0 303 80l-22 -125q-125 -64 -291 -64q-133 0 -241.5 50.5t-176.5 156.5t-68 252zM233 571h687q2 18 2 39q0 135 -74 223.5 t-219 88.5q-181 0 -301 -152q-69 -90 -95 -199zM551 1489h131l117 -336h-92z" />
<glyph unicode="&#xe9;" horiz-adv-x="1126" d="M100 436q0 108 37.5 214.5t104.5 189.5q75 92 177 141t216 49q179 0 289 -114t110 -302q0 -78 -16 -143h-799v-29q0 -168 105 -262t272 -94q150 0 303 80l-22 -125q-125 -64 -291 -64q-133 0 -241.5 50.5t-176.5 156.5t-68 252zM233 571h687q2 18 2 39q0 135 -74 223.5 t-219 88.5q-181 0 -301 -152q-69 -90 -95 -199zM575 1153l236 336h135l-274 -336h-97z" />
<glyph unicode="&#xea;" horiz-adv-x="1126" d="M100 436q0 108 37.5 214.5t104.5 189.5q75 92 177 141t216 49q179 0 289 -114t110 -302q0 -78 -16 -143h-799v-29q0 -168 105 -262t272 -94q150 0 303 80l-22 -125q-125 -64 -291 -64q-133 0 -241.5 50.5t-176.5 156.5t-68 252zM233 571h687q2 18 2 39q0 135 -74 223.5 t-219 88.5q-181 0 -301 -152q-69 -90 -95 -199zM387 1153l297 336h129l178 -336h-96l-158 256l-241 -256h-109z" />
<glyph unicode="&#xeb;" horiz-adv-x="1126" d="M100 436q0 108 37.5 214.5t104.5 189.5q75 92 177 141t216 49q179 0 289 -114t110 -302q0 -78 -16 -143h-799v-29q0 -168 105 -262t272 -94q150 0 303 80l-22 -125q-125 -64 -291 -64q-133 0 -241.5 50.5t-176.5 156.5t-68 252zM233 571h687q2 18 2 39q0 135 -74 223.5 t-219 88.5q-181 0 -301 -152q-69 -90 -95 -199zM446 1317q0 36 27 62t64 26q34 0 56.5 -23t22.5 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM819 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57z" />
<glyph unicode="&#xec;" horiz-adv-x="479" d="M90 0l178 1008h121l-178 -1008h-121zM217 1489h131l117 -336h-92z" />
<glyph unicode="&#xed;" horiz-adv-x="479" d="M90 0l178 1008h121l-178 -1008h-121zM242 1153l235 336h135l-274 -336h-96z" />
<glyph unicode="&#xee;" horiz-adv-x="479" d="M53 1153l297 336h129l178 -336h-96l-158 256l-241 -256h-109zM90 0l178 1008h121l-178 -1008h-121z" />
<glyph unicode="&#xef;" horiz-adv-x="479" d="M90 0l178 1008h121l-178 -1008h-121zM113 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM485 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57z" />
<glyph unicode="&#xf0;" horiz-adv-x="1251" d="M119 436q0 115 44.5 228.5t129.5 193.5q68 66 159 101.5t185 35.5q110 0 207.5 -47.5t140.5 -122.5l4 2q-55 228 -190 402l-322 -99l-4 99l256 78q-95 99 -203 168l84 75q137 -92 240 -204l223 67l4 -98l-159 -49q206 -272 206 -631q0 -320 -163 -494 q-71 -75 -169 -119.5t-212 -44.5q-199 0 -330 129t-131 330zM240 444q0 -158 93.5 -257t252.5 -99q163 0 276 113q63 63 98 150.5t35 180.5q0 155 -95 255t-257 100q-160 0 -268 -105q-67 -64 -101 -155t-34 -183z" />
<glyph unicode="&#xf1;" horiz-adv-x="1204" d="M90 0l178 1008h121l-24 -142l4 -2q200 166 393 166q135 0 216 -79.5t81 -217.5q0 -45 -19 -162l-100 -571h-121l101 567q18 99 18 148q0 93 -51.5 148t-155.5 55q-96 0 -191 -50.5t-202 -144.5l-127 -723h-121zM430 1227l21 121q25 38 67.5 60t93.5 22q37 0 71.5 -14 t86.5 -44q83 -49 131 -49q93 0 160 104l4 -2l-21 -120q-25 -38 -67.5 -60t-93.5 -22q-60 0 -158 57q-83 49 -131 49q-93 0 -160 -104z" />
<glyph unicode="&#xf2;" horiz-adv-x="1208" d="M100 446q0 111 41 224t125 202q152 158 375 158q207 0 337 -130t130 -339q0 -111 -41 -224t-125 -202q-152 -158 -375 -158q-207 0 -337 130t-130 339zM221 455q0 -160 94 -263.5t256 -103.5q170 0 283 119q63 66 98 158.5t35 187.5q0 160 -94 263.5t-256 103.5 q-170 0 -283 -119q-63 -66 -98 -158.5t-35 -187.5zM582 1489h131l116 -336h-92z" />
<glyph unicode="&#xf3;" horiz-adv-x="1208" d="M100 446q0 111 41 224t125 202q152 158 375 158q207 0 337 -130t130 -339q0 -111 -41 -224t-125 -202q-152 -158 -375 -158q-207 0 -337 130t-130 339zM221 455q0 -160 94 -263.5t256 -103.5q170 0 283 119q63 66 98 158.5t35 187.5q0 160 -94 263.5t-256 103.5 q-170 0 -283 -119q-63 -66 -98 -158.5t-35 -187.5zM606 1153l236 336h135l-275 -336h-96z" />
<glyph unicode="&#xf4;" horiz-adv-x="1208" d="M100 446q0 111 41 224t125 202q152 158 375 158q207 0 337 -130t130 -339q0 -111 -41 -224t-125 -202q-152 -158 -375 -158q-207 0 -337 130t-130 339zM221 455q0 -160 94 -263.5t256 -103.5q170 0 283 119q63 66 98 158.5t35 187.5q0 160 -94 263.5t-256 103.5 q-170 0 -283 -119q-63 -66 -98 -158.5t-35 -187.5zM418 1153l297 336h129l178 -336h-96l-158 256l-242 -256h-108z" />
<glyph unicode="&#xf5;" horiz-adv-x="1208" d="M100 446q0 111 41 224t125 202q152 158 375 158q207 0 337 -130t130 -339q0 -111 -41 -224t-125 -202q-152 -158 -375 -158q-207 0 -337 130t-130 339zM221 455q0 -160 94 -263.5t256 -103.5q170 0 283 119q63 66 98 158.5t35 187.5q0 160 -94 263.5t-256 103.5 q-170 0 -283 -119q-63 -66 -98 -158.5t-35 -187.5zM432 1227l21 121q25 38 67.5 60t93.5 22q37 0 71.5 -14t86.5 -44q83 -49 131 -49q93 0 160 104l4 -2l-20 -120q-25 -38 -68 -60t-94 -22q-60 0 -158 57q-83 49 -131 49q-93 0 -160 -104z" />
<glyph unicode="&#xf6;" horiz-adv-x="1208" d="M100 446q0 111 41 224t125 202q152 158 375 158q207 0 337 -130t130 -339q0 -111 -41 -224t-125 -202q-152 -158 -375 -158q-207 0 -337 130t-130 339zM221 455q0 -160 94 -263.5t256 -103.5q170 0 283 119q63 66 98 158.5t35 187.5q0 160 -94 263.5t-256 103.5 q-170 0 -283 -119q-63 -66 -98 -158.5t-35 -187.5zM477 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM850 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23 t-23 57z" />
<glyph unicode="&#xf7;" horiz-adv-x="1179" d="M145 692l17 99h956l-16 -99h-957zM463 289q0 40 28 68t68 28q37 0 62.5 -25.5t25.5 -62.5q0 -40 -28 -68t-68 -28q-37 0 -62.5 25.5t-25.5 62.5zM616 1186q0 40 28.5 68t68.5 28q37 0 62.5 -25.5t25.5 -62.5q0 -40 -28 -68t-68 -28q-37 0 -63 25.5t-26 62.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1208" d="M55 39l129 123q-84 120 -84 284q0 111 41 224t125 202q152 158 375 158q198 0 326 -119l125 119l61 -61l-129 -123q84 -120 84 -285q0 -111 -41 -224t-125 -202q-152 -158 -375 -158q-197 0 -325 119l-125 -119zM221 455q0 -121 53 -207l607 579q-96 93 -244 93 q-170 0 -283 -119q-63 -66 -98 -158.5t-35 -187.5zM328 180q95 -92 243 -92q170 0 283 119q63 66 98 158.5t35 187.5q0 121 -53 207z" />
<glyph unicode="&#xf9;" horiz-adv-x="1198" d="M139 270q0 61 19 172l100 566h119l-98 -562q-17 -100 -17 -155q0 -92 50.5 -147.5t152.5 -55.5q179 0 395 199l127 721h121l-178 -1008h-121l25 143l-5 2q-203 -168 -387 -168q-136 0 -219.5 78.5t-83.5 214.5zM575 1489h132l116 -336h-92z" />
<glyph unicode="&#xfa;" horiz-adv-x="1198" d="M139 270q0 61 19 172l100 566h119l-98 -562q-17 -100 -17 -155q0 -92 50.5 -147.5t152.5 -55.5q179 0 395 199l127 721h121l-178 -1008h-121l25 143l-5 2q-203 -168 -387 -168q-136 0 -219.5 78.5t-83.5 214.5zM600 1153l236 336h135l-275 -336h-96z" />
<glyph unicode="&#xfb;" horiz-adv-x="1198" d="M139 270q0 61 19 172l100 566h119l-98 -562q-17 -100 -17 -155q0 -92 50.5 -147.5t152.5 -55.5q179 0 395 199l127 721h121l-178 -1008h-121l25 143l-5 2q-203 -168 -387 -168q-136 0 -219.5 78.5t-83.5 214.5zM412 1153l297 336h129l178 -336h-96l-158 256l-242 -256 h-108z" />
<glyph unicode="&#xfc;" horiz-adv-x="1198" d="M139 270q0 61 19 172l100 566h119l-98 -562q-17 -100 -17 -155q0 -92 50.5 -147.5t152.5 -55.5q179 0 395 199l127 721h121l-178 -1008h-121l25 143l-5 2q-203 -168 -387 -168q-136 0 -219.5 78.5t-83.5 214.5zM471 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57 q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM844 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57z" />
<glyph unicode="&#xfd;" horiz-adv-x="1091" d="M41 -451l375 566l-269 893h129l115 -385q60 -201 109 -379h4q89 141 239 374l252 390h131l-950 -1459h-135zM547 1153l235 336h136l-275 -336h-96z" />
<glyph unicode="&#xfe;" horiz-adv-x="1181" d="M10 -451l357 2016h120l-110 -633l4 -2q145 100 293 100q176 0 292.5 -122t116.5 -322q0 -119 -43.5 -236t-126.5 -203q-164 -170 -417 -170q-145 0 -273 62l-4 -2l-88 -488h-121zM238 152q123 -68 264 -68q202 0 327 137q64 70 99 163t35 191q0 157 -86.5 252t-225.5 95 q-159 0 -299 -117z" />
<glyph unicode="&#xff;" horiz-adv-x="1091" d="M41 -451l375 566l-269 893h129l115 -385q60 -201 109 -379h4q89 141 239 374l252 390h131l-950 -1459h-135zM418 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM791 1317q0 37 26.5 62.5t63.5 25.5q34 0 57 -23 t23 -57q0 -36 -27 -62t-64 -26q-34 0 -56.5 23t-22.5 57z" />
<glyph unicode="&#x152;" horiz-adv-x="1845" d="M141 666q0 148 54 294.5t163 262.5q241 260 652 260h882l-20 -115h-653l-97 -541h566l-21 -114h-565l-105 -598h672l-20 -115h-783q-153 0 -285.5 45.5t-230 129t-153.5 210t-56 281.5zM266 676q0 -255 167 -408t439 -153l222 1253h-93q-361 0 -561 -229 q-81 -96 -127.5 -217t-46.5 -246z" />
<glyph unicode="&#x153;" horiz-adv-x="1957" d="M100 442q0 110 39 220.5t113 195.5q150 172 375 172q153 0 261.5 -79.5t147.5 -198.5h4q58 116 173.5 197t263.5 81q173 0 281 -114t108 -298q0 -64 -17 -147h-780v-29q0 -167 102.5 -261.5t264.5 -94.5q153 0 295 82l-23 -127q-56 -29 -132 -46.5t-149 -17.5 q-152 0 -272 70.5t-166 196.5h-4q-69 -125 -182 -196t-256 -71q-197 0 -322 130t-125 335zM221 453q0 -157 90.5 -261t245.5 -104q174 0 285 133q55 65 85.5 154t30.5 180q0 156 -89 260.5t-246 104.5q-172 0 -285 -134q-55 -67 -86 -155t-31 -178zM1085 571h666q2 10 2 37 q0 135 -70.5 224.5t-209.5 89.5q-175 0 -293 -152q-67 -87 -95 -199z" />
<glyph unicode="&#x178;" horiz-adv-x="1292" d="M195 1483h139l190 -371q124 -241 168 -332h4q60 73 281 332l319 371h148l-703 -811l-118 -672h-125l118 670zM606 1792q0 37 26.5 62.5t63.5 25.5q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57zM979 1792q0 37 26.5 62.5t63.5 25.5 q34 0 57 -23t23 -57q0 -37 -26.5 -62.5t-63.5 -25.5q-34 0 -57 23t-23 57z" />
<glyph unicode="&#x2c6;" horiz-adv-x="983" d="M305 1153l297 336h129l178 -336h-96l-158 256l-241 -256h-109z" />
<glyph unicode="&#x2dc;" horiz-adv-x="983" d="M319 1227l21 121q25 38 68 60t94 22q37 0 71 -14t86 -44q83 -49 132 -49q92 0 159 104l4 -2l-20 -120q-25 -38 -68 -60t-94 -22q-60 0 -158 57q-83 49 -131 49q-92 0 -159 -104z" />
<glyph unicode="&#x2000;" horiz-adv-x="1032" />
<glyph unicode="&#x2001;" horiz-adv-x="2064" />
<glyph unicode="&#x2002;" horiz-adv-x="1032" />
<glyph unicode="&#x2003;" horiz-adv-x="2064" />
<glyph unicode="&#x2004;" horiz-adv-x="688" />
<glyph unicode="&#x2005;" horiz-adv-x="516" />
<glyph unicode="&#x2006;" horiz-adv-x="344" />
<glyph unicode="&#x2007;" horiz-adv-x="344" />
<glyph unicode="&#x2008;" horiz-adv-x="258" />
<glyph unicode="&#x2009;" horiz-adv-x="412" />
<glyph unicode="&#x200a;" horiz-adv-x="114" />
<glyph unicode="&#x2010;" horiz-adv-x="722" d="M129 514l18 107h467l-18 -107h-467z" />
<glyph unicode="&#x2011;" horiz-adv-x="722" d="M129 514l18 107h467l-18 -107h-467z" />
<glyph unicode="&#x2012;" horiz-adv-x="722" d="M129 514l18 107h467l-18 -107h-467z" />
<glyph unicode="&#x2013;" horiz-adv-x="1007" d="M117 518l16 98h782l-16 -98h-782z" />
<glyph unicode="&#x2014;" horiz-adv-x="1593" d="M84 524l16 88h1434l-16 -88h-1434z" />
<glyph unicode="&#x2018;" horiz-adv-x="544" d="M227 985l248 535h84l-209 -535h-123z" />
<glyph unicode="&#x2019;" horiz-adv-x="544" d="M248 985l209 535h123l-248 -535h-84z" />
<glyph unicode="&#x201a;" horiz-adv-x="544" d="M25 -199l208 535h123l-247 -535h-84z" />
<glyph unicode="&#x201c;" horiz-adv-x="847" d="M227 985l248 535h84l-209 -535h-123zM528 985l248 535h84l-209 -535h-123z" />
<glyph unicode="&#x201d;" horiz-adv-x="847" d="M248 985l209 535h123l-248 -535h-84zM549 985l209 535h123l-248 -535h-84z" />
<glyph unicode="&#x201e;" horiz-adv-x="847" d="M25 -199l208 535h123l-247 -535h-84zM326 -199l209 535h122l-247 -535h-84z" />
<glyph unicode="&#x2022;" horiz-adv-x="786" d="M207 608q0 85 60 145t145 60t144.5 -60t59.5 -145t-59.5 -145t-144.5 -60t-145 60t-60 145z" />
<glyph unicode="&#x2026;" horiz-adv-x="1691" d="M123 76q0 44 32 75t76 31q42 0 71.5 -29t29.5 -69q0 -44 -32.5 -75.5t-76.5 -31.5q-42 0 -71 29.5t-29 69.5zM666 76q0 44 32 75t76 31q42 0 71 -29t29 -69q0 -44 -32 -75.5t-76 -31.5q-42 0 -71 29.5t-29 69.5zM1208 76q0 44 32 75t77 31q42 0 71 -29t29 -69 q0 -44 -32 -75.5t-76 -31.5q-42 0 -71.5 29.5t-29.5 69.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="412" />
<glyph unicode="&#x2039;" horiz-adv-x="694" d="M94 526l414 426h119l-424 -436l266 -461h-107z" />
<glyph unicode="&#x203a;" horiz-adv-x="694" d="M68 55l424 437l-267 460h107l268 -471l-414 -426h-118z" />
<glyph unicode="&#x205f;" horiz-adv-x="516" />
<glyph unicode="&#x20ac;" horiz-adv-x="1198" d="M31 532l18 101h170q0 105 33 237h-162l16 99h179q92 240 286 388t445 148q136 0 266 -45l-22 -123q-127 52 -252 52q-194 0 -350 -112t-242 -308h452l-16 -99h-473q-35 -119 -35 -237h467l-18 -101h-443q25 -206 158 -322t350 -116q126 0 211 31l-22 -125 q-94 -23 -199 -23q-267 0 -433.5 150t-191.5 405h-192z" />
<glyph unicode="&#x2122;" horiz-adv-x="1282" d="M162 1413v70h452v-70h-188v-524h-76v524h-188zM717 889v594h76l206 -299l211 299h76v-594h-76v461l-4 2l-207 -293l-204 297l-4 -2v-465h-74z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x2a;" u2="&#x2026;" k="231" />
<hkern u1="&#x2a;" u2="&#x2e;" k="231" />
<hkern u1="&#x2a;" u2="&#x2c;" k="231" />
<hkern u1="&#x2c;" u2="V" k="227" />
<hkern u1="&#x2c;" u2="&#x39;" k="113" />
<hkern u1="&#x2c;" u2="&#x38;" k="51" />
<hkern u1="&#x2c;" u2="&#x37;" k="94" />
<hkern u1="&#x2c;" u2="&#x31;" k="90" />
<hkern u1="&#x2c;" u2="&#x2a;" k="231" />
<hkern u1="&#x2d;" u2="x" k="119" />
<hkern u1="&#x2d;" u2="X" k="70" />
<hkern u1="&#x2d;" u2="V" k="119" />
<hkern u1="&#x2d;" u2="&#x39;" k="18" />
<hkern u1="&#x2d;" u2="&#x37;" k="100" />
<hkern u1="&#x2d;" u2="&#x32;" k="156" />
<hkern u1="&#x2d;" u2="&#x31;" k="137" />
<hkern u1="&#x2e;" u2="V" k="227" />
<hkern u1="&#x2e;" u2="&#x39;" k="113" />
<hkern u1="&#x2e;" u2="&#x38;" k="51" />
<hkern u1="&#x2e;" u2="&#x37;" k="94" />
<hkern u1="&#x2e;" u2="&#x31;" k="90" />
<hkern u1="&#x2e;" u2="&#x2a;" k="231" />
<hkern u1="&#x2f;" u2="&#x153;" k="154" />
<hkern u1="&#x2f;" u2="&#xf8;" k="154" />
<hkern u1="&#x2f;" u2="&#xf6;" k="154" />
<hkern u1="&#x2f;" u2="&#xf5;" k="154" />
<hkern u1="&#x2f;" u2="&#xf4;" k="154" />
<hkern u1="&#x2f;" u2="&#xf3;" k="154" />
<hkern u1="&#x2f;" u2="&#xf2;" k="154" />
<hkern u1="&#x2f;" u2="&#xf0;" k="154" />
<hkern u1="&#x2f;" u2="&#xeb;" k="154" />
<hkern u1="&#x2f;" u2="&#xea;" k="154" />
<hkern u1="&#x2f;" u2="&#xe9;" k="154" />
<hkern u1="&#x2f;" u2="&#xe8;" k="154" />
<hkern u1="&#x2f;" u2="&#xe7;" k="154" />
<hkern u1="&#x2f;" u2="&#xe6;" k="154" />
<hkern u1="&#x2f;" u2="&#xe5;" k="154" />
<hkern u1="&#x2f;" u2="&#xe4;" k="154" />
<hkern u1="&#x2f;" u2="&#xe3;" k="154" />
<hkern u1="&#x2f;" u2="&#xe2;" k="154" />
<hkern u1="&#x2f;" u2="&#xe1;" k="154" />
<hkern u1="&#x2f;" u2="&#xe0;" k="154" />
<hkern u1="&#x2f;" u2="&#xc5;" k="182" />
<hkern u1="&#x2f;" u2="&#xc4;" k="182" />
<hkern u1="&#x2f;" u2="&#xc3;" k="182" />
<hkern u1="&#x2f;" u2="&#xc2;" k="182" />
<hkern u1="&#x2f;" u2="&#xc1;" k="182" />
<hkern u1="&#x2f;" u2="&#xc0;" k="182" />
<hkern u1="&#x2f;" u2="q" k="154" />
<hkern u1="&#x2f;" u2="o" k="154" />
<hkern u1="&#x2f;" u2="g" k="154" />
<hkern u1="&#x2f;" u2="e" k="154" />
<hkern u1="&#x2f;" u2="d" k="154" />
<hkern u1="&#x2f;" u2="c" k="154" />
<hkern u1="&#x2f;" u2="a" k="154" />
<hkern u1="&#x2f;" u2="A" k="182" />
<hkern u1="&#x2f;" u2="&#x34;" k="143" />
<hkern u1="&#x30;" u2="_" k="68" />
<hkern u1="&#x32;" u2="&#x2014;" k="55" />
<hkern u1="&#x32;" u2="&#x2013;" k="55" />
<hkern u1="&#x32;" u2="&#xad;" k="55" />
<hkern u1="&#x32;" u2="&#x2d;" k="55" />
<hkern u1="&#x36;" u2="&#x37;" k="45" />
<hkern u1="&#x36;" u2="&#x31;" k="47" />
<hkern u1="&#x37;" u2="&#x2026;" k="236" />
<hkern u1="&#x37;" u2="&#x2014;" k="96" />
<hkern u1="&#x37;" u2="&#x2013;" k="96" />
<hkern u1="&#x37;" u2="&#xad;" k="96" />
<hkern u1="&#x37;" u2="&#x34;" k="129" />
<hkern u1="&#x37;" u2="&#x2f;" k="205" />
<hkern u1="&#x37;" u2="&#x2e;" k="236" />
<hkern u1="&#x37;" u2="&#x2d;" k="96" />
<hkern u1="&#x37;" u2="&#x2c;" k="236" />
<hkern u1="&#x38;" u2="&#x2026;" k="29" />
<hkern u1="&#x38;" u2="&#x2e;" k="29" />
<hkern u1="&#x38;" u2="&#x2c;" k="29" />
<hkern u1="&#x39;" u2="&#x2026;" k="182" />
<hkern u1="&#x39;" u2="&#x2e;" k="182" />
<hkern u1="&#x39;" u2="&#x2c;" k="182" />
<hkern u1="A" u2="x" k="-27" />
<hkern u1="A" u2="\" k="182" />
<hkern u1="A" u2="V" k="207" />
<hkern u1="B" u2="&#x203a;" k="31" />
<hkern u1="B" u2="&#x201c;" k="31" />
<hkern u1="B" u2="&#x2018;" k="31" />
<hkern u1="B" u2="&#x178;" k="66" />
<hkern u1="B" u2="&#xff;" k="41" />
<hkern u1="B" u2="&#xfd;" k="41" />
<hkern u1="B" u2="&#xdd;" k="66" />
<hkern u1="B" u2="&#xc6;" k="16" />
<hkern u1="B" u2="&#xc5;" k="12" />
<hkern u1="B" u2="&#xc4;" k="12" />
<hkern u1="B" u2="&#xc3;" k="12" />
<hkern u1="B" u2="&#xc2;" k="12" />
<hkern u1="B" u2="&#xc1;" k="12" />
<hkern u1="B" u2="&#xc0;" k="12" />
<hkern u1="B" u2="&#xbb;" k="31" />
<hkern u1="B" u2="z" k="20" />
<hkern u1="B" u2="y" k="41" />
<hkern u1="B" u2="w" k="35" />
<hkern u1="B" u2="v" k="41" />
<hkern u1="B" u2="Y" k="66" />
<hkern u1="B" u2="W" k="18" />
<hkern u1="B" u2="V" k="37" />
<hkern u1="B" u2="A" k="12" />
<hkern u1="D" u2="x" k="10" />
<hkern u1="D" u2="_" k="115" />
<hkern u1="D" u2="X" k="29" />
<hkern u1="D" u2="V" k="92" />
<hkern u1="F" u2="&#x203a;" k="55" />
<hkern u1="F" u2="&#x2039;" k="47" />
<hkern u1="F" u2="&#x2026;" k="209" />
<hkern u1="F" u2="&#x201e;" k="109" />
<hkern u1="F" u2="&#x201c;" k="14" />
<hkern u1="F" u2="&#x201a;" k="109" />
<hkern u1="F" u2="&#x2018;" k="14" />
<hkern u1="F" u2="&#x153;" k="57" />
<hkern u1="F" u2="&#xff;" k="47" />
<hkern u1="F" u2="&#xfd;" k="47" />
<hkern u1="F" u2="&#xfc;" k="51" />
<hkern u1="F" u2="&#xfb;" k="51" />
<hkern u1="F" u2="&#xfa;" k="51" />
<hkern u1="F" u2="&#xf9;" k="51" />
<hkern u1="F" u2="&#xf8;" k="57" />
<hkern u1="F" u2="&#xf6;" k="57" />
<hkern u1="F" u2="&#xf5;" k="57" />
<hkern u1="F" u2="&#xf4;" k="57" />
<hkern u1="F" u2="&#xf3;" k="57" />
<hkern u1="F" u2="&#xf2;" k="57" />
<hkern u1="F" u2="&#xf1;" k="84" />
<hkern u1="F" u2="&#xf0;" k="57" />
<hkern u1="F" u2="&#xef;" k="-43" />
<hkern u1="F" u2="&#xee;" k="-23" />
<hkern u1="F" u2="&#xeb;" k="57" />
<hkern u1="F" u2="&#xea;" k="57" />
<hkern u1="F" u2="&#xe9;" k="57" />
<hkern u1="F" u2="&#xe8;" k="57" />
<hkern u1="F" u2="&#xe7;" k="57" />
<hkern u1="F" u2="&#xe6;" k="57" />
<hkern u1="F" u2="&#xe5;" k="57" />
<hkern u1="F" u2="&#xe4;" k="57" />
<hkern u1="F" u2="&#xe3;" k="57" />
<hkern u1="F" u2="&#xe2;" k="57" />
<hkern u1="F" u2="&#xe1;" k="57" />
<hkern u1="F" u2="&#xe0;" k="57" />
<hkern u1="F" u2="&#xc5;" k="88" />
<hkern u1="F" u2="&#xc4;" k="88" />
<hkern u1="F" u2="&#xc3;" k="88" />
<hkern u1="F" u2="&#xc2;" k="88" />
<hkern u1="F" u2="&#xc1;" k="88" />
<hkern u1="F" u2="&#xc0;" k="88" />
<hkern u1="F" u2="&#xbb;" k="55" />
<hkern u1="F" u2="&#xab;" k="47" />
<hkern u1="F" u2="z" k="55" />
<hkern u1="F" u2="y" k="47" />
<hkern u1="F" u2="x" k="45" />
<hkern u1="F" u2="w" k="45" />
<hkern u1="F" u2="v" k="47" />
<hkern u1="F" u2="u" k="51" />
<hkern u1="F" u2="t" k="31" />
<hkern u1="F" u2="s" k="41" />
<hkern u1="F" u2="r" k="84" />
<hkern u1="F" u2="q" k="57" />
<hkern u1="F" u2="p" k="84" />
<hkern u1="F" u2="o" k="57" />
<hkern u1="F" u2="n" k="84" />
<hkern u1="F" u2="m" k="84" />
<hkern u1="F" u2="g" k="57" />
<hkern u1="F" u2="e" k="57" />
<hkern u1="F" u2="d" k="57" />
<hkern u1="F" u2="c" k="57" />
<hkern u1="F" u2="a" k="57" />
<hkern u1="F" u2="A" k="88" />
<hkern u1="F" u2="&#x2e;" k="209" />
<hkern u1="F" u2="&#x2c;" k="209" />
<hkern u1="K" u2="&#xf8;" k="74" />
<hkern u1="K" u2="x" k="-20" />
<hkern u1="K" u2="a" k="14" />
<hkern u1="L" u2="&#x2019;" k="238" />
<hkern u1="L" u2="x" k="-6" />
<hkern u1="L" u2="V" k="188" />
<hkern u1="O" u2="x" k="10" />
<hkern u1="O" u2="_" k="115" />
<hkern u1="O" u2="X" k="29" />
<hkern u1="O" u2="V" k="92" />
<hkern u1="P" u2="&#x203a;" k="18" />
<hkern u1="P" u2="&#x2039;" k="53" />
<hkern u1="P" u2="&#x2026;" k="266" />
<hkern u1="P" u2="&#x201e;" k="145" />
<hkern u1="P" u2="&#x201a;" k="145" />
<hkern u1="P" u2="&#x153;" k="37" />
<hkern u1="P" u2="&#xff;" k="6" />
<hkern u1="P" u2="&#xfd;" k="6" />
<hkern u1="P" u2="&#xfc;" k="18" />
<hkern u1="P" u2="&#xfb;" k="18" />
<hkern u1="P" u2="&#xfa;" k="18" />
<hkern u1="P" u2="&#xf9;" k="18" />
<hkern u1="P" u2="&#xf8;" k="37" />
<hkern u1="P" u2="&#xf6;" k="37" />
<hkern u1="P" u2="&#xf5;" k="37" />
<hkern u1="P" u2="&#xf4;" k="37" />
<hkern u1="P" u2="&#xf3;" k="37" />
<hkern u1="P" u2="&#xf2;" k="37" />
<hkern u1="P" u2="&#xf1;" k="18" />
<hkern u1="P" u2="&#xf0;" k="37" />
<hkern u1="P" u2="&#xef;" k="-41" />
<hkern u1="P" u2="&#xee;" k="-66" />
<hkern u1="P" u2="&#xeb;" k="37" />
<hkern u1="P" u2="&#xea;" k="37" />
<hkern u1="P" u2="&#xe9;" k="37" />
<hkern u1="P" u2="&#xe8;" k="37" />
<hkern u1="P" u2="&#xe7;" k="37" />
<hkern u1="P" u2="&#xe6;" k="37" />
<hkern u1="P" u2="&#xe5;" k="37" />
<hkern u1="P" u2="&#xe4;" k="37" />
<hkern u1="P" u2="&#xe3;" k="37" />
<hkern u1="P" u2="&#xe2;" k="37" />
<hkern u1="P" u2="&#xe1;" k="37" />
<hkern u1="P" u2="&#xe0;" k="37" />
<hkern u1="P" u2="&#xc5;" k="51" />
<hkern u1="P" u2="&#xc4;" k="51" />
<hkern u1="P" u2="&#xc3;" k="51" />
<hkern u1="P" u2="&#xc2;" k="51" />
<hkern u1="P" u2="&#xc1;" k="51" />
<hkern u1="P" u2="&#xc0;" k="51" />
<hkern u1="P" u2="&#xbb;" k="18" />
<hkern u1="P" u2="&#xab;" k="53" />
<hkern u1="P" u2="z" k="14" />
<hkern u1="P" u2="y" k="6" />
<hkern u1="P" u2="x" k="16" />
<hkern u1="P" u2="v" k="6" />
<hkern u1="P" u2="u" k="18" />
<hkern u1="P" u2="s" k="8" />
<hkern u1="P" u2="r" k="18" />
<hkern u1="P" u2="q" k="37" />
<hkern u1="P" u2="p" k="18" />
<hkern u1="P" u2="o" k="37" />
<hkern u1="P" u2="n" k="18" />
<hkern u1="P" u2="m" k="18" />
<hkern u1="P" u2="g" k="37" />
<hkern u1="P" u2="e" k="37" />
<hkern u1="P" u2="d" k="37" />
<hkern u1="P" u2="c" k="37" />
<hkern u1="P" u2="a" k="20" />
<hkern u1="P" u2="W" k="20" />
<hkern u1="P" u2="V" k="33" />
<hkern u1="P" u2="J" k="139" />
<hkern u1="P" u2="A" k="51" />
<hkern u1="P" u2="&#x2e;" k="266" />
<hkern u1="P" u2="&#x2c;" k="266" />
<hkern u1="Q" u2="x" k="10" />
<hkern u1="Q" u2="_" k="115" />
<hkern u1="Q" u2="X" k="29" />
<hkern u1="Q" u2="V" k="92" />
<hkern u1="R" u2="&#xf8;" k="63" />
<hkern u1="R" u2="V" k="49" />
<hkern u1="S" u2="_" k="39" />
<hkern u1="S" u2="V" k="39" />
<hkern u1="T" u2="&#xff;" k="106" />
<hkern u1="T" u2="&#xfc;" k="139" />
<hkern u1="T" u2="&#xf6;" k="188" />
<hkern u1="T" u2="&#xf1;" k="199" />
<hkern u1="T" u2="&#xee;" k="-6" />
<hkern u1="T" u2="&#xec;" k="-10" />
<hkern u1="T" u2="&#xeb;" k="145" />
<hkern u1="T" u2="&#xea;" k="193" />
<hkern u1="T" u2="&#xe4;" k="182" />
<hkern u1="T" u2="&#xe3;" k="201" />
<hkern u1="T" u2="&#xe2;" k="229" />
<hkern u1="T" u2="x" k="141" />
<hkern u1="T" u2="_" k="121" />
<hkern u1="T" u2="&#x2f;" k="182" />
<hkern u1="U" u2="x" k="12" />
<hkern u1="U" u2="a" k="14" />
<hkern u1="V" u2="&#x203a;" k="33" />
<hkern u1="V" u2="&#x2039;" k="63" />
<hkern u1="V" u2="&#x2026;" k="182" />
<hkern u1="V" u2="&#x2014;" k="115" />
<hkern u1="V" u2="&#x2013;" k="115" />
<hkern u1="V" u2="&#x153;" k="129" />
<hkern u1="V" u2="&#x152;" k="23" />
<hkern u1="V" u2="&#xff;" k="66" />
<hkern u1="V" u2="&#xfd;" k="66" />
<hkern u1="V" u2="&#xfc;" k="94" />
<hkern u1="V" u2="&#xfb;" k="94" />
<hkern u1="V" u2="&#xfa;" k="94" />
<hkern u1="V" u2="&#xf9;" k="94" />
<hkern u1="V" u2="&#xf8;" k="129" />
<hkern u1="V" u2="&#xf6;" k="129" />
<hkern u1="V" u2="&#xf5;" k="129" />
<hkern u1="V" u2="&#xf4;" k="129" />
<hkern u1="V" u2="&#xf3;" k="129" />
<hkern u1="V" u2="&#xf2;" k="129" />
<hkern u1="V" u2="&#xf1;" k="102" />
<hkern u1="V" u2="&#xf0;" k="129" />
<hkern u1="V" u2="&#xeb;" k="129" />
<hkern u1="V" u2="&#xea;" k="129" />
<hkern u1="V" u2="&#xe9;" k="129" />
<hkern u1="V" u2="&#xe8;" k="129" />
<hkern u1="V" u2="&#xe7;" k="129" />
<hkern u1="V" u2="&#xe6;" k="129" />
<hkern u1="V" u2="&#xe5;" k="129" />
<hkern u1="V" u2="&#xe4;" k="129" />
<hkern u1="V" u2="&#xe3;" k="129" />
<hkern u1="V" u2="&#xe2;" k="129" />
<hkern u1="V" u2="&#xe1;" k="129" />
<hkern u1="V" u2="&#xe0;" k="129" />
<hkern u1="V" u2="&#xd8;" k="23" />
<hkern u1="V" u2="&#xd6;" k="23" />
<hkern u1="V" u2="&#xd5;" k="23" />
<hkern u1="V" u2="&#xd4;" k="23" />
<hkern u1="V" u2="&#xd3;" k="23" />
<hkern u1="V" u2="&#xd2;" k="23" />
<hkern u1="V" u2="&#xc7;" k="23" />
<hkern u1="V" u2="&#xc6;" k="250" />
<hkern u1="V" u2="&#xc5;" k="150" />
<hkern u1="V" u2="&#xc4;" k="150" />
<hkern u1="V" u2="&#xc3;" k="150" />
<hkern u1="V" u2="&#xc2;" k="150" />
<hkern u1="V" u2="&#xc1;" k="150" />
<hkern u1="V" u2="&#xc0;" k="150" />
<hkern u1="V" u2="&#xbb;" k="33" />
<hkern u1="V" u2="&#xad;" k="115" />
<hkern u1="V" u2="&#xab;" k="63" />
<hkern u1="V" u2="y" k="66" />
<hkern u1="V" u2="x" k="63" />
<hkern u1="V" u2="w" k="76" />
<hkern u1="V" u2="v" k="66" />
<hkern u1="V" u2="u" k="94" />
<hkern u1="V" u2="t" k="31" />
<hkern u1="V" u2="s" k="88" />
<hkern u1="V" u2="r" k="102" />
<hkern u1="V" u2="q" k="129" />
<hkern u1="V" u2="p" k="102" />
<hkern u1="V" u2="o" k="129" />
<hkern u1="V" u2="n" k="102" />
<hkern u1="V" u2="m" k="102" />
<hkern u1="V" u2="g" k="129" />
<hkern u1="V" u2="e" k="129" />
<hkern u1="V" u2="d" k="129" />
<hkern u1="V" u2="c" k="129" />
<hkern u1="V" u2="a" k="129" />
<hkern u1="V" u2="_" k="117" />
<hkern u1="V" u2="S" k="41" />
<hkern u1="V" u2="Q" k="23" />
<hkern u1="V" u2="O" k="23" />
<hkern u1="V" u2="J" k="74" />
<hkern u1="V" u2="G" k="23" />
<hkern u1="V" u2="C" k="23" />
<hkern u1="V" u2="A" k="150" />
<hkern u1="V" u2="&#x2f;" k="154" />
<hkern u1="V" u2="&#x2e;" k="182" />
<hkern u1="V" u2="&#x2d;" k="115" />
<hkern u1="V" u2="&#x2c;" k="182" />
<hkern u1="W" u2="&#xe4;" k="47" />
<hkern u1="W" u2="x" k="35" />
<hkern u1="W" u2="_" k="90" />
<hkern u1="W" u2="&#x2f;" k="115" />
<hkern u1="X" u2="&#x203a;" k="35" />
<hkern u1="X" u2="&#x2039;" k="84" />
<hkern u1="X" u2="&#x201d;" k="23" />
<hkern u1="X" u2="&#x201c;" k="59" />
<hkern u1="X" u2="&#x2019;" k="23" />
<hkern u1="X" u2="&#x2018;" k="59" />
<hkern u1="X" u2="&#x2014;" k="76" />
<hkern u1="X" u2="&#x2013;" k="76" />
<hkern u1="X" u2="&#x153;" k="74" />
<hkern u1="X" u2="&#x152;" k="29" />
<hkern u1="X" u2="&#xff;" k="152" />
<hkern u1="X" u2="&#xfd;" k="152" />
<hkern u1="X" u2="&#xf8;" k="74" />
<hkern u1="X" u2="&#xf6;" k="74" />
<hkern u1="X" u2="&#xf5;" k="74" />
<hkern u1="X" u2="&#xf4;" k="74" />
<hkern u1="X" u2="&#xf3;" k="74" />
<hkern u1="X" u2="&#xf2;" k="74" />
<hkern u1="X" u2="&#xf0;" k="74" />
<hkern u1="X" u2="&#xeb;" k="74" />
<hkern u1="X" u2="&#xea;" k="74" />
<hkern u1="X" u2="&#xe9;" k="74" />
<hkern u1="X" u2="&#xe8;" k="74" />
<hkern u1="X" u2="&#xe7;" k="74" />
<hkern u1="X" u2="&#xe6;" k="74" />
<hkern u1="X" u2="&#xe5;" k="74" />
<hkern u1="X" u2="&#xe4;" k="74" />
<hkern u1="X" u2="&#xe3;" k="74" />
<hkern u1="X" u2="&#xe2;" k="74" />
<hkern u1="X" u2="&#xe1;" k="74" />
<hkern u1="X" u2="&#xe0;" k="74" />
<hkern u1="X" u2="&#xd8;" k="29" />
<hkern u1="X" u2="&#xd6;" k="29" />
<hkern u1="X" u2="&#xd5;" k="29" />
<hkern u1="X" u2="&#xd4;" k="29" />
<hkern u1="X" u2="&#xd3;" k="29" />
<hkern u1="X" u2="&#xd2;" k="29" />
<hkern u1="X" u2="&#xc7;" k="29" />
<hkern u1="X" u2="&#xbb;" k="35" />
<hkern u1="X" u2="&#xad;" k="76" />
<hkern u1="X" u2="&#xab;" k="84" />
<hkern u1="X" u2="y" k="152" />
<hkern u1="X" u2="w" k="41" />
<hkern u1="X" u2="v" k="152" />
<hkern u1="X" u2="q" k="74" />
<hkern u1="X" u2="o" k="74" />
<hkern u1="X" u2="g" k="74" />
<hkern u1="X" u2="e" k="74" />
<hkern u1="X" u2="d" k="74" />
<hkern u1="X" u2="c" k="74" />
<hkern u1="X" u2="a" k="74" />
<hkern u1="X" u2="Q" k="29" />
<hkern u1="X" u2="O" k="29" />
<hkern u1="X" u2="G" k="29" />
<hkern u1="X" u2="C" k="29" />
<hkern u1="X" u2="&#x2d;" k="76" />
<hkern u1="Y" u2="&#xff;" k="104" />
<hkern u1="Y" u2="&#xf6;" k="184" />
<hkern u1="Y" u2="&#xf5;" k="184" />
<hkern u1="Y" u2="&#xf4;" k="225" />
<hkern u1="Y" u2="&#xee;" k="-39" />
<hkern u1="Y" u2="&#xec;" k="-41" />
<hkern u1="Y" u2="&#xeb;" k="166" />
<hkern u1="Y" u2="&#xea;" k="174" />
<hkern u1="Y" u2="&#xe8;" k="186" />
<hkern u1="Y" u2="&#xe4;" k="170" />
<hkern u1="Y" u2="&#xe3;" k="184" />
<hkern u1="Y" u2="&#xe2;" k="209" />
<hkern u1="Y" u2="&#xe0;" k="186" />
<hkern u1="Y" u2="x" k="92" />
<hkern u1="Y" u2="_" k="139" />
<hkern u1="Y" u2="&#x34;" k="180" />
<hkern u1="Y" u2="&#x2f;" k="197" />
<hkern u1="Z" u2="x" k="-10" />
<hkern u1="\" u2="&#x178;" k="197" />
<hkern u1="\" u2="&#xdd;" k="197" />
<hkern u1="\" u2="Y" k="197" />
<hkern u1="\" u2="W" k="139" />
<hkern u1="\" u2="V" k="168" />
<hkern u1="\" u2="T" k="182" />
<hkern u1="\" u2="&#x37;" k="68" />
<hkern u1="_" u2="&#x178;" k="139" />
<hkern u1="_" u2="&#x153;" k="119" />
<hkern u1="_" u2="&#x152;" k="115" />
<hkern u1="_" u2="&#xff;" k="100" />
<hkern u1="_" u2="&#xfd;" k="100" />
<hkern u1="_" u2="&#xf8;" k="119" />
<hkern u1="_" u2="&#xf6;" k="119" />
<hkern u1="_" u2="&#xf5;" k="119" />
<hkern u1="_" u2="&#xf4;" k="119" />
<hkern u1="_" u2="&#xf3;" k="119" />
<hkern u1="_" u2="&#xf2;" k="119" />
<hkern u1="_" u2="&#xf0;" k="119" />
<hkern u1="_" u2="&#xeb;" k="119" />
<hkern u1="_" u2="&#xea;" k="119" />
<hkern u1="_" u2="&#xe9;" k="119" />
<hkern u1="_" u2="&#xe8;" k="119" />
<hkern u1="_" u2="&#xe7;" k="119" />
<hkern u1="_" u2="&#xe6;" k="119" />
<hkern u1="_" u2="&#xe5;" k="119" />
<hkern u1="_" u2="&#xe4;" k="119" />
<hkern u1="_" u2="&#xe3;" k="119" />
<hkern u1="_" u2="&#xe2;" k="119" />
<hkern u1="_" u2="&#xe1;" k="119" />
<hkern u1="_" u2="&#xe0;" k="119" />
<hkern u1="_" u2="&#xdd;" k="139" />
<hkern u1="_" u2="&#xd8;" k="115" />
<hkern u1="_" u2="&#xd6;" k="115" />
<hkern u1="_" u2="&#xd5;" k="115" />
<hkern u1="_" u2="&#xd4;" k="115" />
<hkern u1="_" u2="&#xd3;" k="115" />
<hkern u1="_" u2="&#xd2;" k="115" />
<hkern u1="_" u2="&#xc7;" k="115" />
<hkern u1="_" u2="y" k="100" />
<hkern u1="_" u2="w" k="92" />
<hkern u1="_" u2="v" k="100" />
<hkern u1="_" u2="q" k="119" />
<hkern u1="_" u2="o" k="119" />
<hkern u1="_" u2="g" k="119" />
<hkern u1="_" u2="e" k="119" />
<hkern u1="_" u2="d" k="119" />
<hkern u1="_" u2="c" k="119" />
<hkern u1="_" u2="a" k="119" />
<hkern u1="_" u2="Y" k="139" />
<hkern u1="_" u2="W" k="92" />
<hkern u1="_" u2="V" k="115" />
<hkern u1="_" u2="T" k="121" />
<hkern u1="_" u2="Q" k="115" />
<hkern u1="_" u2="O" k="115" />
<hkern u1="_" u2="G" k="115" />
<hkern u1="_" u2="C" k="115" />
<hkern u1="_" u2="&#x30;" k="68" />
<hkern u1="b" u2="y" k="96" />
<hkern u1="b" u2="x" k="51" />
<hkern u1="b" u2="_" k="66" />
<hkern u1="b" u2="X" k="59" />
<hkern u1="b" u2="V" k="174" />
<hkern u1="c" u2="V" k="43" />
<hkern u1="e" u2="x" k="29" />
<hkern u1="e" u2="V" k="104" />
<hkern u1="f" u2="_" k="18" />
<hkern u1="f" u2="&#x3f;" k="-33" />
<hkern u1="f" u2="&#x2a;" k="-61" />
<hkern u1="f" u2="&#x21;" k="-8" />
<hkern u1="g" u2="&#x201d;" k="33" />
<hkern u1="g" u2="&#x201c;" k="74" />
<hkern u1="g" u2="&#x2019;" k="33" />
<hkern u1="g" u2="&#x2018;" k="74" />
<hkern u1="g" u2="j" k="-12" />
<hkern u1="h" u2="y" k="43" />
<hkern u1="h" u2="V" k="166" />
<hkern u1="j" u2="j" k="-18" />
<hkern u1="k" u2="&#xf8;" k="39" />
<hkern u1="m" u2="y" k="43" />
<hkern u1="m" u2="V" k="166" />
<hkern u1="n" u2="&#x201c;" k="49" />
<hkern u1="n" u2="&#x2018;" k="49" />
<hkern u1="n" u2="y" k="43" />
<hkern u1="n" u2="V" k="166" />
<hkern u1="o" u2="y" k="96" />
<hkern u1="o" u2="x" k="51" />
<hkern u1="o" u2="_" k="66" />
<hkern u1="o" u2="X" k="59" />
<hkern u1="o" u2="V" k="174" />
<hkern u1="p" u2="&#x201c;" k="57" />
<hkern u1="p" u2="&#x2018;" k="57" />
<hkern u1="p" u2="y" k="96" />
<hkern u1="p" u2="x" k="51" />
<hkern u1="p" u2="_" k="66" />
<hkern u1="p" u2="X" k="59" />
<hkern u1="p" u2="V" k="174" />
<hkern u1="q" u2="&#x201c;" k="18" />
<hkern u1="q" u2="&#x2018;" k="18" />
<hkern u1="s" u2="x" k="23" />
<hkern u1="s" u2="V" k="55" />
<hkern u1="u" u2="V" k="111" />
<hkern u1="v" u2="_" k="102" />
<hkern u1="v" u2="X" k="127" />
<hkern u1="v" u2="V" k="53" />
<hkern u1="w" u2="V" k="49" />
<hkern u1="x" u2="&#x203a;" k="4" />
<hkern u1="x" u2="&#x2039;" k="102" />
<hkern u1="x" u2="&#x2014;" k="119" />
<hkern u1="x" u2="&#x2013;" k="119" />
<hkern u1="x" u2="&#x153;" k="45" />
<hkern u1="x" u2="&#xf8;" k="45" />
<hkern u1="x" u2="&#xf6;" k="45" />
<hkern u1="x" u2="&#xf5;" k="45" />
<hkern u1="x" u2="&#xf4;" k="45" />
<hkern u1="x" u2="&#xf3;" k="45" />
<hkern u1="x" u2="&#xf2;" k="45" />
<hkern u1="x" u2="&#xf0;" k="45" />
<hkern u1="x" u2="&#xeb;" k="45" />
<hkern u1="x" u2="&#xea;" k="45" />
<hkern u1="x" u2="&#xe9;" k="45" />
<hkern u1="x" u2="&#xe8;" k="45" />
<hkern u1="x" u2="&#xe7;" k="45" />
<hkern u1="x" u2="&#xe6;" k="45" />
<hkern u1="x" u2="&#xe5;" k="45" />
<hkern u1="x" u2="&#xe4;" k="45" />
<hkern u1="x" u2="&#xe3;" k="45" />
<hkern u1="x" u2="&#xe2;" k="45" />
<hkern u1="x" u2="&#xe1;" k="45" />
<hkern u1="x" u2="&#xe0;" k="45" />
<hkern u1="x" u2="&#xbb;" k="4" />
<hkern u1="x" u2="&#xad;" k="119" />
<hkern u1="x" u2="&#xab;" k="102" />
<hkern u1="x" u2="q" k="45" />
<hkern u1="x" u2="o" k="45" />
<hkern u1="x" u2="g" k="45" />
<hkern u1="x" u2="e" k="45" />
<hkern u1="x" u2="d" k="45" />
<hkern u1="x" u2="c" k="45" />
<hkern u1="x" u2="a" k="45" />
<hkern u1="x" u2="W" k="23" />
<hkern u1="x" u2="V" k="53" />
<hkern u1="x" u2="T" k="92" />
<hkern u1="x" u2="&#x2d;" k="119" />
<hkern u1="y" u2="&#x153;" k="31" />
<hkern u1="y" u2="&#xf8;" k="31" />
<hkern u1="y" u2="&#xf6;" k="31" />
<hkern u1="y" u2="&#xf5;" k="31" />
<hkern u1="y" u2="&#xf4;" k="31" />
<hkern u1="y" u2="&#xf3;" k="31" />
<hkern u1="y" u2="&#xf2;" k="31" />
<hkern u1="y" u2="&#xf0;" k="31" />
<hkern u1="y" u2="&#xeb;" k="31" />
<hkern u1="y" u2="&#xea;" k="31" />
<hkern u1="y" u2="&#xe9;" k="31" />
<hkern u1="y" u2="&#xe8;" k="31" />
<hkern u1="y" u2="&#xe7;" k="31" />
<hkern u1="y" u2="&#xe6;" k="31" />
<hkern u1="y" u2="&#xe5;" k="31" />
<hkern u1="y" u2="&#xe4;" k="31" />
<hkern u1="y" u2="&#xe3;" k="31" />
<hkern u1="y" u2="&#xe2;" k="31" />
<hkern u1="y" u2="&#xe1;" k="31" />
<hkern u1="y" u2="&#xe0;" k="31" />
<hkern u1="y" u2="q" k="31" />
<hkern u1="y" u2="o" k="31" />
<hkern u1="y" u2="g" k="31" />
<hkern u1="y" u2="e" k="31" />
<hkern u1="y" u2="d" k="31" />
<hkern u1="y" u2="c" k="31" />
<hkern u1="y" u2="a" k="31" />
<hkern u1="y" u2="_" k="102" />
<hkern u1="y" u2="X" k="127" />
<hkern u1="y" u2="V" k="53" />
<hkern u1="&#xab;" u2="x" k="10" />
<hkern u1="&#xab;" u2="X" k="18" />
<hkern u1="&#xab;" u2="V" k="53" />
<hkern u1="&#xad;" u2="x" k="119" />
<hkern u1="&#xad;" u2="X" k="70" />
<hkern u1="&#xad;" u2="V" k="119" />
<hkern u1="&#xad;" u2="&#x39;" k="18" />
<hkern u1="&#xad;" u2="&#x37;" k="100" />
<hkern u1="&#xad;" u2="&#x32;" k="156" />
<hkern u1="&#xad;" u2="&#x31;" k="137" />
<hkern u1="&#xbb;" u2="x" k="102" />
<hkern u1="&#xbb;" u2="a" k="10" />
<hkern u1="&#xbb;" u2="X" k="86" />
<hkern u1="&#xbb;" u2="V" k="135" />
<hkern u1="&#xbb;" u2="J" k="27" />
<hkern u1="&#xc0;" u2="x" k="-27" />
<hkern u1="&#xc0;" u2="\" k="182" />
<hkern u1="&#xc0;" u2="V" k="207" />
<hkern u1="&#xc1;" u2="x" k="-27" />
<hkern u1="&#xc1;" u2="\" k="182" />
<hkern u1="&#xc1;" u2="V" k="207" />
<hkern u1="&#xc2;" u2="x" k="-27" />
<hkern u1="&#xc2;" u2="\" k="182" />
<hkern u1="&#xc2;" u2="V" k="207" />
<hkern u1="&#xc3;" u2="x" k="-27" />
<hkern u1="&#xc3;" u2="\" k="182" />
<hkern u1="&#xc3;" u2="V" k="207" />
<hkern u1="&#xc4;" u2="x" k="-27" />
<hkern u1="&#xc4;" u2="\" k="182" />
<hkern u1="&#xc4;" u2="V" k="207" />
<hkern u1="&#xc5;" u2="x" k="-27" />
<hkern u1="&#xc5;" u2="\" k="182" />
<hkern u1="&#xc5;" u2="V" k="207" />
<hkern u1="&#xd2;" u2="x" k="10" />
<hkern u1="&#xd2;" u2="_" k="115" />
<hkern u1="&#xd2;" u2="X" k="29" />
<hkern u1="&#xd2;" u2="V" k="92" />
<hkern u1="&#xd3;" u2="x" k="10" />
<hkern u1="&#xd3;" u2="_" k="115" />
<hkern u1="&#xd3;" u2="X" k="29" />
<hkern u1="&#xd3;" u2="V" k="92" />
<hkern u1="&#xd4;" u2="x" k="10" />
<hkern u1="&#xd4;" u2="_" k="115" />
<hkern u1="&#xd4;" u2="X" k="29" />
<hkern u1="&#xd4;" u2="V" k="92" />
<hkern u1="&#xd5;" u2="x" k="10" />
<hkern u1="&#xd5;" u2="_" k="115" />
<hkern u1="&#xd5;" u2="X" k="29" />
<hkern u1="&#xd5;" u2="V" k="92" />
<hkern u1="&#xd6;" u2="x" k="10" />
<hkern u1="&#xd6;" u2="_" k="115" />
<hkern u1="&#xd6;" u2="X" k="29" />
<hkern u1="&#xd6;" u2="V" k="92" />
<hkern u1="&#xd8;" u2="x" k="10" />
<hkern u1="&#xd8;" u2="_" k="115" />
<hkern u1="&#xd8;" u2="X" k="29" />
<hkern u1="&#xd8;" u2="V" k="92" />
<hkern u1="&#xd9;" u2="x" k="12" />
<hkern u1="&#xd9;" u2="a" k="14" />
<hkern u1="&#xda;" u2="x" k="12" />
<hkern u1="&#xda;" u2="a" k="14" />
<hkern u1="&#xdb;" u2="x" k="12" />
<hkern u1="&#xdb;" u2="a" k="14" />
<hkern u1="&#xdc;" u2="x" k="12" />
<hkern u1="&#xdc;" u2="a" k="14" />
<hkern u1="&#xdd;" u2="&#xff;" k="104" />
<hkern u1="&#xdd;" u2="&#xf6;" k="184" />
<hkern u1="&#xdd;" u2="&#xf5;" k="184" />
<hkern u1="&#xdd;" u2="&#xf4;" k="225" />
<hkern u1="&#xdd;" u2="&#xee;" k="-39" />
<hkern u1="&#xdd;" u2="&#xec;" k="-41" />
<hkern u1="&#xdd;" u2="&#xeb;" k="166" />
<hkern u1="&#xdd;" u2="&#xea;" k="174" />
<hkern u1="&#xdd;" u2="&#xe8;" k="186" />
<hkern u1="&#xdd;" u2="&#xe4;" k="170" />
<hkern u1="&#xdd;" u2="&#xe3;" k="184" />
<hkern u1="&#xdd;" u2="&#xe2;" k="209" />
<hkern u1="&#xdd;" u2="&#xe0;" k="186" />
<hkern u1="&#xdd;" u2="x" k="92" />
<hkern u1="&#xdd;" u2="_" k="139" />
<hkern u1="&#xdd;" u2="&#x34;" k="180" />
<hkern u1="&#xdd;" u2="&#x2f;" k="197" />
<hkern u1="&#xde;" u2="&#x178;" k="139" />
<hkern u1="&#xde;" u2="&#xdd;" k="139" />
<hkern u1="&#xde;" u2="&#xc5;" k="43" />
<hkern u1="&#xde;" u2="&#xc4;" k="43" />
<hkern u1="&#xde;" u2="&#xc3;" k="43" />
<hkern u1="&#xde;" u2="&#xc2;" k="43" />
<hkern u1="&#xde;" u2="&#xc1;" k="43" />
<hkern u1="&#xde;" u2="&#xc0;" k="43" />
<hkern u1="&#xde;" u2="z" k="8" />
<hkern u1="&#xde;" u2="_" k="133" />
<hkern u1="&#xde;" u2="Y" k="139" />
<hkern u1="&#xde;" u2="W" k="57" />
<hkern u1="&#xde;" u2="V" k="88" />
<hkern u1="&#xde;" u2="T" k="88" />
<hkern u1="&#xde;" u2="A" k="43" />
<hkern u1="&#xdf;" u2="&#xff;" k="72" />
<hkern u1="&#xdf;" u2="&#xfd;" k="72" />
<hkern u1="&#xdf;" u2="y" k="72" />
<hkern u1="&#xdf;" u2="w" k="66" />
<hkern u1="&#xdf;" u2="v" k="72" />
<hkern u1="&#xe6;" u2="x" k="29" />
<hkern u1="&#xe6;" u2="V" k="104" />
<hkern u1="&#xe7;" u2="V" k="43" />
<hkern u1="&#xe8;" u2="x" k="29" />
<hkern u1="&#xe8;" u2="V" k="104" />
<hkern u1="&#xe9;" u2="x" k="29" />
<hkern u1="&#xe9;" u2="V" k="104" />
<hkern u1="&#xea;" u2="x" k="29" />
<hkern u1="&#xea;" u2="V" k="104" />
<hkern u1="&#xeb;" u2="x" k="29" />
<hkern u1="&#xeb;" u2="V" k="104" />
<hkern u1="&#xed;" u2="T" k="-20" />
<hkern u1="&#xee;" u2="&#x178;" k="-4" />
<hkern u1="&#xee;" u2="&#xdd;" k="-4" />
<hkern u1="&#xee;" u2="Y" k="-4" />
<hkern u1="&#xef;" u2="V" k="-31" />
<hkern u1="&#xf2;" u2="y" k="96" />
<hkern u1="&#xf2;" u2="x" k="51" />
<hkern u1="&#xf2;" u2="_" k="66" />
<hkern u1="&#xf2;" u2="X" k="59" />
<hkern u1="&#xf2;" u2="V" k="174" />
<hkern u1="&#xf3;" u2="y" k="96" />
<hkern u1="&#xf3;" u2="x" k="51" />
<hkern u1="&#xf3;" u2="_" k="66" />
<hkern u1="&#xf3;" u2="X" k="59" />
<hkern u1="&#xf3;" u2="V" k="174" />
<hkern u1="&#xf4;" u2="y" k="96" />
<hkern u1="&#xf4;" u2="x" k="51" />
<hkern u1="&#xf4;" u2="_" k="66" />
<hkern u1="&#xf4;" u2="X" k="59" />
<hkern u1="&#xf4;" u2="V" k="174" />
<hkern u1="&#xf5;" u2="y" k="96" />
<hkern u1="&#xf5;" u2="x" k="51" />
<hkern u1="&#xf5;" u2="_" k="66" />
<hkern u1="&#xf5;" u2="X" k="59" />
<hkern u1="&#xf5;" u2="V" k="174" />
<hkern u1="&#xf6;" u2="y" k="96" />
<hkern u1="&#xf6;" u2="x" k="51" />
<hkern u1="&#xf6;" u2="_" k="66" />
<hkern u1="&#xf6;" u2="X" k="59" />
<hkern u1="&#xf6;" u2="V" k="174" />
<hkern u1="&#xf8;" u2="y" k="96" />
<hkern u1="&#xf8;" u2="x" k="51" />
<hkern u1="&#xf8;" u2="_" k="66" />
<hkern u1="&#xf8;" u2="X" k="59" />
<hkern u1="&#xf8;" u2="V" k="174" />
<hkern u1="&#xf9;" u2="V" k="111" />
<hkern u1="&#xfa;" u2="V" k="111" />
<hkern u1="&#xfb;" u2="V" k="111" />
<hkern u1="&#xfc;" u2="V" k="111" />
<hkern u1="&#xfd;" u2="_" k="102" />
<hkern u1="&#xfd;" u2="X" k="127" />
<hkern u1="&#xfd;" u2="V" k="53" />
<hkern u1="&#xfe;" u2="y" k="96" />
<hkern u1="&#xfe;" u2="x" k="51" />
<hkern u1="&#xfe;" u2="_" k="66" />
<hkern u1="&#xfe;" u2="X" k="59" />
<hkern u1="&#xfe;" u2="V" k="174" />
<hkern u1="&#xff;" u2="_" k="102" />
<hkern u1="&#xff;" u2="X" k="127" />
<hkern u1="&#xff;" u2="V" k="53" />
<hkern u1="&#x153;" u2="x" k="29" />
<hkern u1="&#x153;" u2="V" k="104" />
<hkern u1="&#x178;" u2="&#xff;" k="104" />
<hkern u1="&#x178;" u2="&#xf6;" k="184" />
<hkern u1="&#x178;" u2="&#xf5;" k="184" />
<hkern u1="&#x178;" u2="&#xf4;" k="225" />
<hkern u1="&#x178;" u2="&#xee;" k="-39" />
<hkern u1="&#x178;" u2="&#xec;" k="-41" />
<hkern u1="&#x178;" u2="&#xeb;" k="166" />
<hkern u1="&#x178;" u2="&#xea;" k="174" />
<hkern u1="&#x178;" u2="&#xe8;" k="186" />
<hkern u1="&#x178;" u2="&#xe4;" k="170" />
<hkern u1="&#x178;" u2="&#xe3;" k="184" />
<hkern u1="&#x178;" u2="&#xe2;" k="209" />
<hkern u1="&#x178;" u2="&#xe0;" k="186" />
<hkern u1="&#x178;" u2="x" k="92" />
<hkern u1="&#x178;" u2="_" k="139" />
<hkern u1="&#x178;" u2="&#x34;" k="180" />
<hkern u1="&#x178;" u2="&#x2f;" k="197" />
<hkern u1="&#x2013;" u2="x" k="119" />
<hkern u1="&#x2013;" u2="X" k="70" />
<hkern u1="&#x2013;" u2="V" k="119" />
<hkern u1="&#x2013;" u2="&#x39;" k="18" />
<hkern u1="&#x2013;" u2="&#x37;" k="100" />
<hkern u1="&#x2013;" u2="&#x32;" k="156" />
<hkern u1="&#x2013;" u2="&#x31;" k="137" />
<hkern u1="&#x2014;" u2="x" k="119" />
<hkern u1="&#x2014;" u2="X" k="70" />
<hkern u1="&#x2014;" u2="V" k="119" />
<hkern u1="&#x2014;" u2="&#x39;" k="18" />
<hkern u1="&#x2014;" u2="&#x37;" k="100" />
<hkern u1="&#x2014;" u2="&#x32;" k="156" />
<hkern u1="&#x2014;" u2="&#x31;" k="137" />
<hkern u1="&#x2018;" u2="x" k="51" />
<hkern u1="&#x2018;" u2="j" k="39" />
<hkern u1="&#x2018;" u2="g" k="59" />
<hkern u1="&#x2018;" u2="X" k="4" />
<hkern u1="&#x2018;" u2="V" k="8" />
<hkern u1="&#x2019;" u2="x" k="39" />
<hkern u1="&#x2019;" u2="j" k="37" />
<hkern u1="&#x2019;" u2="a" k="80" />
<hkern u1="&#x2019;" u2="X" k="18" />
<hkern u1="&#x2019;" u2="V" k="10" />
<hkern u1="&#x201a;" u2="V" k="104" />
<hkern u1="&#x201c;" u2="x" k="51" />
<hkern u1="&#x201c;" u2="j" k="39" />
<hkern u1="&#x201c;" u2="g" k="59" />
<hkern u1="&#x201c;" u2="X" k="4" />
<hkern u1="&#x201c;" u2="V" k="8" />
<hkern u1="&#x201d;" u2="x" k="39" />
<hkern u1="&#x201d;" u2="j" k="37" />
<hkern u1="&#x201d;" u2="a" k="80" />
<hkern u1="&#x201d;" u2="X" k="18" />
<hkern u1="&#x201d;" u2="V" k="10" />
<hkern u1="&#x201e;" u2="V" k="104" />
<hkern u1="&#x2026;" u2="V" k="227" />
<hkern u1="&#x2026;" u2="&#x39;" k="113" />
<hkern u1="&#x2026;" u2="&#x38;" k="51" />
<hkern u1="&#x2026;" u2="&#x37;" k="94" />
<hkern u1="&#x2026;" u2="&#x31;" k="90" />
<hkern u1="&#x2026;" u2="&#x2a;" k="231" />
<hkern u1="&#x2039;" u2="x" k="10" />
<hkern u1="&#x2039;" u2="X" k="18" />
<hkern u1="&#x2039;" u2="V" k="53" />
<hkern u1="&#x203a;" u2="x" k="102" />
<hkern u1="&#x203a;" u2="a" k="10" />
<hkern u1="&#x203a;" u2="X" k="86" />
<hkern u1="&#x203a;" u2="V" k="135" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="94" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="123" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="23" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="12" />
<hkern g1="C,Ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,y,yacute,ydieresis" 	k="55" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="4" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="G" 	g2="w" 	k="59" />
<hkern g1="G" 	g2="v,y,yacute,ydieresis" 	k="72" />
<hkern g1="G" 	g2="t" 	k="27" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="39" />
<hkern g1="G" 	g2="z" 	k="18" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="8" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="92" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="84" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="78" />
<hkern g1="K" 	g2="w" 	k="129" />
<hkern g1="K" 	g2="v,y,yacute,ydieresis" 	k="145" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="156" />
<hkern g1="K" 	g2="t" 	k="92" />
<hkern g1="K" 	g2="s" 	k="31" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="104" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="111" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-4" />
<hkern g1="K" 	g2="z" 	k="-10" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="297" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="113" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="74" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="178" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="33" />
<hkern g1="L" 	g2="w" 	k="139" />
<hkern g1="L" 	g2="v,y,yacute,ydieresis" 	k="162" />
<hkern g1="L" 	g2="t" 	k="78" />
<hkern g1="L" 	g2="s" 	k="16" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="317" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="201" />
<hkern g1="L" 	g2="T" 	k="225" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="57" />
<hkern g1="L" 	g2="W" 	k="139" />
<hkern g1="L" 	g2="z" 	k="14" />
<hkern g1="L" 	g2="J" 	k="-10" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="25" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="49" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="137" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="119" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="59" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="27" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="18" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="72" />
<hkern g1="D,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="53" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="59" />
<hkern g1="R" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="76" />
<hkern g1="R" 	g2="W" 	k="37" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="S" 	g2="w" 	k="51" />
<hkern g1="S" 	g2="v,y,yacute,ydieresis" 	k="68" />
<hkern g1="S" 	g2="t" 	k="14" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="25" />
<hkern g1="S" 	g2="W" 	k="29" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="S" 	g2="z" 	k="25" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="23" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="248" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="221" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="223" />
<hkern g1="T" 	g2="w" 	k="203" />
<hkern g1="T" 	g2="v,y,yacute,ydieresis" 	k="211" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="223" />
<hkern g1="T" 	g2="s" 	k="186" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-14" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="205" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="217" />
<hkern g1="T" 	g2="z" 	k="139" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="180" />
<hkern g1="T" 	g2="J" 	k="158" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="207" />
<hkern g1="T" 	g2="AE" 	k="242" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="209" />
<hkern g1="T" 	g2="idieresis" 	k="-63" />
<hkern g1="T" 	g2="S" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="43" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="98" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="68" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="66" />
<hkern g1="W" 	g2="w" 	k="70" />
<hkern g1="W" 	g2="v,y,yacute,ydieresis" 	k="33" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="66" />
<hkern g1="W" 	g2="t" 	k="23" />
<hkern g1="W" 	g2="s" 	k="68" />
<hkern g1="W" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="78" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="86" />
<hkern g1="W" 	g2="J" 	k="66" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="121" />
<hkern g1="W" 	g2="AE" 	k="219" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="66" />
<hkern g1="W" 	g2="S" 	k="33" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="29" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="172" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="137" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,y,yacute,ydieresis" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="182" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="39" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="168" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="221" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="207" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="104" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="139" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="111" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="219" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="268" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="145" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="idieresis" 	k="-47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="14" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="78" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="59" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="29" />
<hkern g1="Z" 	g2="w" 	k="96" />
<hkern g1="Z" 	g2="v,y,yacute,ydieresis" 	k="117" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="123" />
<hkern g1="Z" 	g2="s" 	k="8" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="Z" 	g2="idieresis" 	k="-47" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="86" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="35" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v,y,yacute,ydieresis" 	k="35" />
<hkern g1="a,g,q,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="quoteright,quotedblright" 	k="264" />
<hkern g1="comma,period,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="43" />
<hkern g1="comma,period,ellipsis" 	g2="quotedbl,quotesingle" 	k="131" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="182" />
<hkern g1="comma,period,ellipsis" 	g2="v,y,yacute,ydieresis" 	k="223" />
<hkern g1="comma,period,ellipsis" 	g2="s" 	k="2" />
<hkern g1="comma,period,ellipsis" 	g2="quoteleft,quotedblleft" 	k="236" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="219" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="207" />
<hkern g1="comma,period,ellipsis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="143" />
<hkern g1="comma,period,ellipsis" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="39" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="94" />
<hkern g1="comma,period,ellipsis" 	g2="parenright,bracketright,braceright" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="137" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="63" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v,y,yacute,ydieresis" 	k="74" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="29" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="147" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="184" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="201" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotesinglbase,quotedblbase" 	k="18" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f,uniFB01,uniFB02" 	k="12" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="f" 	g2="w" 	k="-6" />
<hkern g1="f" 	g2="v,y,yacute,ydieresis" 	k="-6" />
<hkern g1="f" 	g2="t" 	k="6" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="57" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="84" />
<hkern g1="f" 	g2="parenright,bracketright,braceright" 	k="-61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="w" 	k="18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v,y,yacute,ydieresis" 	k="45" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="s" 	k="55" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="184" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="221" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="45" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="z" 	k="14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Z" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="S" 	k="8" />
<hkern g1="guillemotright,guilsinglright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="70" />
<hkern g1="guillemotright,guilsinglright" 	g2="v,y,yacute,ydieresis" 	k="92" />
<hkern g1="guillemotright,guilsinglright" 	g2="s" 	k="35" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="172" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="248" />
<hkern g1="guillemotright,guilsinglright" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="80" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="47" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="70" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="66" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="w" 	k="47" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="v,y,yacute,ydieresis" 	k="66" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="182" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="223" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="70" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="53" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="129" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="61" />
<hkern g1="idieresis" 	g2="Y,Yacute,Ydieresis" 	k="-27" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="88" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="k" 	g2="w" 	k="14" />
<hkern g1="k" 	g2="v,y,yacute,ydieresis" 	k="12" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="166" />
<hkern g1="k" 	g2="s" 	k="37" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="104" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="27" />
<hkern g1="h,m,n" 	g2="quoteright,quotedblright" 	k="184" />
<hkern g1="h,m,n" 	g2="quotedbl,quotesingle" 	k="84" />
<hkern g1="h,m,n" 	g2="w" 	k="78" />
<hkern g1="h,m,n" 	g2="v,y,yacute,ydieresis" 	k="84" />
<hkern g1="h,m,n" 	g2="t" 	k="27" />
<hkern g1="h,m,n" 	g2="quoteleft,quotedblleft" 	k="137" />
<hkern g1="h,m,n" 	g2="Y,Yacute,Ydieresis" 	k="152" />
<hkern g1="h,m,n" 	g2="T" 	k="221" />
<hkern g1="h,m,n" 	g2="W" 	k="106" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="141" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="98" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="74" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v,y,yacute,ydieresis" 	k="84" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="86" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="246" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="195" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="W" 	k="113" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="33" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Z" 	k="8" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotesinglbase,quotedblbase" 	k="23" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,ellipsis" 	k="45" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="parenright,bracketright,braceright" 	k="43" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="104" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="53" />
<hkern g1="quoteleft,quotedblleft" 	g2="v,y,yacute,ydieresis" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="135" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="6" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="223" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="254" />
<hkern g1="quoteleft,quotedblleft" 	g2="z" 	k="84" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,ellipsis" 	k="270" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde" 	k="117" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="33" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="90" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="100" />
<hkern g1="quoteright,quotedblright" 	g2="w" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="v,y,yacute,ydieresis" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="t" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="188" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="236" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="240" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="68" />
<hkern g1="quoteright,quotedblright" 	g2="Z" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,ellipsis" 	k="340" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="137" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="72" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v,y,yacute,ydieresis" 	k="117" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="t" 	k="45" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="10" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="117" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="147" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="74" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="quotedbl,quotesingle" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="98" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="166" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,ellipsis" 	k="131" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,p,r,ntilde" 	k="31" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="76" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="84" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="170" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="197" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="100" />
<hkern g1="s" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="s" 	g2="guillemotright,guilsinglright" 	k="29" />
<hkern g1="s" 	g2="w" 	k="43" />
<hkern g1="s" 	g2="v,y,yacute,ydieresis" 	k="55" />
<hkern g1="s" 	g2="hyphen,uni00AD,endash,emdash" 	k="39" />
<hkern g1="s" 	g2="t" 	k="23" />
<hkern g1="s" 	g2="quoteleft,quotedblleft" 	k="84" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="211" />
<hkern g1="s" 	g2="T" 	k="188" />
<hkern g1="s" 	g2="W" 	k="43" />
<hkern g1="s" 	g2="z" 	k="25" />
<hkern g1="s" 	g2="Z" 	k="23" />
<hkern g1="s" 	g2="quotesinglbase,quotedblbase" 	k="16" />
<hkern g1="s" 	g2="f,uniFB01,uniFB02" 	k="16" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="55" />
<hkern g1="t" 	g2="quotedbl,quotesingle" 	k="23" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="82" />
<hkern g1="w" 	g2="hyphen,uni00AD,endash,emdash" 	k="27" />
<hkern g1="w" 	g2="s" 	k="27" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="w" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="w" 	g2="W" 	k="14" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="74" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="111" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="180" />
<hkern g1="zero" 	g2="comma,period,ellipsis" 	k="94" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="v,y,yacute,ydieresis" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="v,y,yacute,ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="63" />
<hkern g1="v,y,yacute,ydieresis" 	g2="s" 	k="25" />
<hkern g1="v,y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="150" />
<hkern g1="v,y,yacute,ydieresis" 	g2="T" 	k="188" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="v,y,yacute,ydieresis" 	g2="W" 	k="31" />
<hkern g1="v,y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="88" />
<hkern g1="v,y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="111" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="223" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="z" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="74" />
<hkern g1="c,ccedilla" 	g2="w" 	k="16" />
<hkern g1="c,ccedilla" 	g2="v,y,yacute,ydieresis" 	k="16" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="98" />
<hkern g1="c,ccedilla" 	g2="T" 	k="143" />
<hkern g1="c,ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="c,ccedilla" 	g2="W" 	k="29" />
</font>
</defs></svg> 