/**
 * Responsive Menu System
 * --------------------------------------------------
 */

// Active Menu ?
var active_menu = false;

// Responsive Menu
var $responsive_menu = $('[data-responsive-menu]');

// Main Container
var $main_container = $('[data-container]');

// Open the menu !
$('[data-responsive-menu-trigger]').click(function(e) {

	e.preventDefault();

	// Elements
	$burger_icon = $(this).find('.burger-menu');

	// Add Forward Class to the container
	$main_container.toggleClass('forward');

	// Move Responsive Menu, but keep the last position in memory.
	$responsive_menu.prependTo('body').show();

	$responsive_menu.find('div.links').addClass('fadeIn');

	// No scroll allowed here
	$('body').css({
		'overflow': 'hidden',
		'position': 'fixed',
		'height': $(window).height()
	});

	// Active Menu ?
	setTimeout(function() {
		active_menu = ($main_container.hasClass('forward')) ? true : false;
	}, 100);

});

// Close the Menu !
$('[data-container]').click(function() {

	if(!active_menu) return;
	$('body').css({
		'overflow': 'auto',
		'position': 'initial',
		'height': 'auto'
	});
	$responsive_menu.hide();
	$(this).removeClass('forward');
	active_menu = false;

});