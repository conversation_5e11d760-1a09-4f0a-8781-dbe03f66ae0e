/**
 * Simple JS Tabs System
 * ---------------------
 */

/**
 * Tabs initialization
 * 
 * @return void
 */
function initTabs() {
	$('[data-tab]').hide();
	$('[data-tab-target]').each(function() {
		if($(this).hasClass('active')) {
			$('[data-tab="' + $(this).attr('data-tab-target') + '"]').show();
		}
	});
}

/**
 * Update specific tabs
 * 
 * @return void
 */
$.fn.updateTabs = function() {
	var $tab = $('[data-tab="' + $(this).attr('data-tab-target') + '"]');
	$(this).parent().parent().parent().parent().parent().parent().find('[data-tab]').hide();
	// $('[data-tab]').hide();
	$(this).parent().parent().parent().parent().parent().parent().find('[data-tab-target]').removeClass('active');
	// $('[data-tab-target]').removeClass('active');
	$('[data-tab-target="' + $(this).attr('data-tab-target') + '"]').addClass('active');
	$tab.fadeIn();
};

$(document).ready(function() {

	initTabs();

	$(document).on('click', '[data-tab-target]', function(e) {
		e.preventDefault();
		$(this).parent().parent().find('[data-tab-target]').removeClass('active');
		$(this).addClass('active');
		$(this).updateTabs();
	});
	
});