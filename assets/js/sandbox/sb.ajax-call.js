/**
 * Simple JS Ajax Call System
 * --------------------------
 */

// Ajax Call Instance
function ajaxCallsInstance() {
	$("[data-ajax-call]").each(function() {
		var _self = $(this);
		var _template = '/ajax/' + $(this).attr('data-ajax-call');
		var _delete_attr = $(this).attr('data-ajax-auto-delete');
		var _type_attr = $(this).attr('data-ajax-type');
		var _delete = (typeof _delete_attr !== typeof undefined && _delete_attr !== false) ? true : false;
		var _type = (typeof _type_attr !== typeof undefined && _type_attr !== false) ? _type_attr : 'get';
		var _xid = $(this).attr('data-ajax-xid-hash');
		var _data = $(this).attr('data-ajax-data');
		if(_type == 'get') {
			$.get(_template, function(data) {
				if(_delete) {
					_self.after(data);
					_self.remove();
				} else {
					_self.html(data);
				}
				_self.removeAttr('data-ajax-call');
				_self.removeAttr('data-ajax-type');
				_self.removeAttr('data-ajax-xid-hash');
				_self.removeAttr('data-ajax-data');
			});
		} else {
			// Post Request
			$.post(_template, { XID: _xid, data: _data }, function(data) {
				if(_delete) {
					_self.after(data);
					_self.remove();
				} else {
					_self.html(data);
				}
				_self.removeAttr('data-ajax-call');
				_self.removeAttr('data-ajax-type');
				_self.removeAttr('data-ajax-xid-hash');
				_self.removeAttr('data-ajax-data');
			});
		}
	});
}

$(document).ready(function() {
	ajaxCallsInstance();
});