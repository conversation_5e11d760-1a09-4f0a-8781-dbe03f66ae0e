/**
 * SB Ajax Forms Management
 * -------------------------
 */

$(document).ajaxComplete(function() {

	// Forms Selector
	$forms = $('div.wt-ajax-form > form');

	// Form Submission
    $(document).on('submit', 'div.wt-ajax-form > form', function(e) {

		// Avoid multiple ajax requests
		e.stopImmediatePropagation();

		// Selectors
        var $form           = $(this);
        var $fields         = $form.find('input[type=text], input[type=email], input[type=password], input[type=date], input[type=datetime], select, textarea');
        var $submit_button  = $form.find('input[type="submit"]');
        var $success        = $form.find('.notice.success');
        var $field_error    = $form.find('.field-error');
        var $error          = $form.find('.notice.error');
        var $errors         = $form.find('.notice.error.field');
        var $form_container = $form.find('div.dform');
        var $loader         = $form.parent('.wt-ajax-form').find('.loading-spinner');
		
		// Some style ...
        $form.addClass('ajax-loading');
		$submit_button.attr('disabled', 'disabled');
		$submit_button.css('opacity', '0.5');
        $loader.show();

		// Send the request
		$.post(
            $form.attr("action"),
            $form.serialize(),
            function(data) {
                $loader.hide();
                $form.removeClass('ajax-loading');
                $errors.remove();
                $field_error.removeClass('field-error');
                $submit_button.removeAttr('disabled');
                $submit_button.css('opacity', '1');
                data = JSON.parse(data);
                if (data.success == "yes") {

                    // Success
                    $errors.remove();
                    $error.hide();
                    $success.show();
                    $form_container.remove();

                } else {

                    // Error
                    $.each(data.errors, function(i) {
                        var $field = $form.find('#forms_field_' + data.errors[i].field_id);
                        var msg = data.errors[i].msg;
                        msg = msg.replace('form:error:required_field', 'This Field is Required');
                        if(!$field.find('div.dform_error').length) {
                            $field.find('label').after('<div class="notice error field">' + msg + '</div>');
                            $field.find('input, textarea, select').addClass('field-error');
                        }
                    });
                    $success.hide();
                    $error.show();

                }
            }
        );

		e.preventDefault();

	});

});