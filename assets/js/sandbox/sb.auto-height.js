// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
// Auto Height Elements System
// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
// How to use :
// ------------
// These articles will get the same height :
// <article data-auto-height>Lorem Ipsum</article>
// <article data-auto-height>Lorem Ipsum</article>
// <article data-auto-height>Lorem Ipsum</article>
// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

var auto_height = function() {

	// Remove height, remove transitions
	$('[data-auto-height]').css('height', '');
	$('[data-auto-height]').addClass('no-transition');

	if($(window).width() > 500) {
		$("[data-auto-height]").each(function() {
			var _target = $(this).attr('data-auto-height');
			if(!_target) {
				var $parent = $(this).parent();
				var $target = $parent.find("> " + $(this).prop("tagName"));

				$target.css("height", "");
				
				var better_height = Math.max.apply(null, $target.map(function () {
				    return $(this).outerHeight();
				}));

				$target.css("height", better_height);
			} else {
				// Special target | ex: data-auto-height="toc_description"
				var better_height = Math.max.apply(null, $('[data-auto-height="' + _target + '"]').map(function () {
				    return $(this).outerHeight();
				}));
				$(this).css("height", better_height);
			}
		});
	} else {
		$("[data-auto-height]").each(function() {
			$(this).css("height", "");
		});
	}

	// Enable transitions
	$('[data-auto-height]').removeClass('no-transition');

}

$(window).load(function() {
	setTimeout(auto_height, 1);
});

$(window).resize((function() {
	var _timeout = null;
	return function() {
		if (_timeout) clearTimeout(_timeout);
			_timeout = setTimeout(auto_height, 250);
	};
})());

$(document).ajaxComplete(function() {
	auto_height();
});