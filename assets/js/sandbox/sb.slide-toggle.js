/**
 * Automatic Slide Toggle System
 * --------------------------------------------------
 * Example of use :
 * data-slide-toggle="#myselector"
 */

$(document).ready(function() {

	function scrollTo($target) {
		if($target.length) $("html, body").animate({ scrollTop: $target.offset().top }, 800);
	}

	// Hide all selectors
	$('[data-slide-toggle]').each(function() {
		var $target = $($(this).attr('data-slide-toggle'));
		if($target.length) $target.hide();
	});

	// On click !
	$(document).on('click', '[data-slide-toggle]', function(e) {
		e.preventDefault();

		// Target
		var $target = $($(this).attr('data-slide-toggle'));
		if(!$target.length) return;

		// Already open target ! Just close it !
		if($target.hasClass('js-deployed-slide-toggle')) {
			$target.slideToggle('slow', 'swing', enableScroll).removeClass('js-deployed-slide-toggle');
			return;
		}

		// Disable Scroll while slideToggling !
		disableScroll();

		// Close all hide Slide Toggles & Show the right one
		if($('.js-deployed-slide-toggle').length) {
			$('.js-deployed-slide-toggle').slideToggle('slow', 'swing', function() {
				$('.js-deployed-slide-toggle').removeClass('js-deployed-slide-toggle');
				$target.slideToggle('slow', 'swing', enableScroll).addClass('js-deployed-slide-toggle');
				scrollTo($target);
			});
		} else {
			$target.slideToggle('slow', 'swing', enableScroll).addClass('js-deployed-slide-toggle');
			scrollTo($target);
		}

	});

});