/**
 * Simple JS Breakpoints System (Based on Foundation)
 * --------------------------------------------------
 * Example of use :
 * onBreakpoint('small', function() {
 *     console.log('Small Breakpoint !');
 * });
 */

function onBreakpoint(bp, callback) {
	$(window).on('changed.zf.mediaquery', function(event, newSize, oldSize) {
		if(newSize == bp) return callback();
	});
	if(Foundation.MediaQuery.current == bp) return callback();
}