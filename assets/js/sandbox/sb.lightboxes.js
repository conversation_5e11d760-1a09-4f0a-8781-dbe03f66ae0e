/**
 * Lightbox System (Featherlight JS)
 * ---------------------------------
 */

// FeatherLight Global Object
var $featherLight = $.featherlight;

// Cancel featherLight loading
$.featherlight.autoBind = false;

// Custom configuration
var lightbox_settings = {
	namespace: 'js-lightbox',
	targetAttr: 'data-lightbox',
	closeIcon: '<i class="icon icon-close"></i>'
};

$(document).ready(function() {

	// Change the data-attribute
	$('[data-lightbox]').featherlight(lightbox_settings);

	// Ajax Call !
	$(document).on('click', '[data-lightbox-ajax]', function(e) {
		e.preventDefault();
		var _ajax_url = $(this).attr('data-lightbox-ajax');
		$featherLight('<div class="lightbox"><div data-ajax-call="' + _ajax_url + '" data-ajax-auto-delete><div class="loading-spinner"></div></div></div>', lightbox_settings);
		ajaxCallsInstance();
	});

	// Video Call !
	$(document).on('click', '[data-lightbox-video]', function(e) {

		// e.preventDefault();

		// Get Video URL
		var _video_url = $(this).attr('data-lightbox-video');

		// Get the video type
		var _video_type = getVideoType(_video_url);

		// Get the video ID
		var _video_id = getVideoID(_video_url);

		// Generate the iframe
		if(_video_type == 'youtube') {
			var _output = '<div class="video-container"><iframe src="//www.youtube.com/embed/' + _video_id + '?autoplay=1&showinfo=0" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe></div>';
		} else if(_video_type == 'vimeo') {
			var _output = '<div class="video-container"><iframe src="//player.vimeo.com/video/' + _video_id + '?title=0&amp;byline=0&amp;portrait=0&amp;color=96c159&amp;api=1&amp;autoplay=1&amp;player_id=video_' + _video_id + '" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe></div>';
		}

		// Generate the lightbox
		$featherLight(_output, {
			namespace: 'js-lightbox-video',
			targetAttr: 'data-lightbox',
			closeIcon: '<i class="icon icon-close"></i>'
		});

	});

});

// -------------------------------------------------------------------------------------
// Helpers
// -------------------------------------------------------------------------------------

// Get Video Type
function getVideoType(video_url) {
	if(video_url.indexOf('yout') >= 0) return 'youtube';
	if(video_url.indexOf('vimeo') >= 0) return 'vimeo';
}

// Get Video ID based on the URL
function getVideoID(video_url) {

	var _video_type = getVideoType(video_url);
	var _video_id;

	// Get youtube video ID
	if(_video_type == "youtube") {
		var regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
		var match = video_url.match(regExp);
		_video_id = (2 in match) ? match[2] : '';
	}

	// Get Vimeo video ID
	if(_video_type == "vimeo") {
		if(video_url.indexOf("clip_id") > -1) {
			var regExp = /(clip_id=(\d+))/;
			var match = video_url.match(regExp);
		} else if(video_url.indexOf("player") > -1) {
			var regExp = /player\.vimeo\.com\/video\/([0-9]*)/;
			var match = video_url.match(regExp);
			match[2] = match[1];
		} else {
			var regExp = /(?:http|https):\/\/(www\.)?vimeo.com\/(\d+)($|\/)/;
			var match = video_url.match(regExp);
		}
		_video_id = (2 in match) ? match[2] : '';
	}

	return _video_id;

}