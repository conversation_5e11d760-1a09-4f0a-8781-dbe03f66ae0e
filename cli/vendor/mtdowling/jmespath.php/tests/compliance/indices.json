[{"given": {"foo": {"bar": ["zero", "one", "two"]}}, "cases": [{"expression": "foo.bar[0]", "result": "zero"}, {"expression": "foo.bar[1]", "result": "one"}, {"expression": "foo.bar[2]", "result": "two"}, {"expression": "foo.bar[3]", "result": null}, {"expression": "foo.bar[-1]", "result": "two"}, {"expression": "foo.bar[-2]", "result": "one"}, {"expression": "foo.bar[-3]", "result": "zero"}, {"expression": "foo.bar[-4]", "result": null}]}, {"given": {"foo": [{"bar": "one"}, {"bar": "two"}, {"bar": "three"}, {"notbar": "four"}]}, "cases": [{"expression": "foo.bar", "result": null}, {"expression": "foo[0].bar", "result": "one"}, {"expression": "foo[1].bar", "result": "two"}, {"expression": "foo[2].bar", "result": "three"}, {"expression": "foo[3].notbar", "result": "four"}, {"expression": "foo[3].bar", "result": null}, {"expression": "foo[0]", "result": {"bar": "one"}}, {"expression": "foo[1]", "result": {"bar": "two"}}, {"expression": "foo[2]", "result": {"bar": "three"}}, {"expression": "foo[3]", "result": {"notbar": "four"}}, {"expression": "foo[4]", "result": null}]}, {"given": ["one", "two", "three"], "cases": [{"expression": "[0]", "result": "one"}, {"expression": "[1]", "result": "two"}, {"expression": "[2]", "result": "three"}, {"expression": "[-1]", "result": "three"}, {"expression": "[-2]", "result": "two"}, {"expression": "[-3]", "result": "one"}]}, {"given": {"reservations": [{"instances": [{"foo": 1}, {"foo": 2}]}]}, "cases": [{"expression": "reservations[].instances[].foo", "result": [1, 2]}, {"expression": "reservations[].instances[].bar", "result": []}, {"expression": "reservations[].notinstances[].foo", "result": []}, {"expression": "reservations[].notinstances[].foo", "result": []}]}, {"given": {"reservations": [{"instances": [{"foo": [{"bar": 1}, {"bar": 2}, {"notbar": 3}, {"bar": 4}]}, {"foo": [{"bar": 5}, {"bar": 6}, {"notbar": [7]}, {"bar": 8}]}, {"foo": "bar"}, {"notfoo": [{"bar": 20}, {"bar": 21}, {"notbar": [7]}, {"bar": 22}]}, {"bar": [{"baz": [1]}, {"baz": [2]}, {"baz": [3]}, {"baz": [4]}]}, {"baz": [{"baz": [1, 2]}, {"baz": []}, {"baz": []}, {"baz": [3, 4]}]}, {"qux": [{"baz": []}, {"baz": [1, 2, 3]}, {"baz": [4]}, {"baz": []}]}], "otherkey": {"foo": [{"bar": 1}, {"bar": 2}, {"notbar": 3}, {"bar": 4}]}}, {"instances": [{"a": [{"bar": 1}, {"bar": 2}, {"notbar": 3}, {"bar": 4}]}, {"b": [{"bar": 5}, {"bar": 6}, {"notbar": [7]}, {"bar": 8}]}, {"c": "bar"}, {"notfoo": [{"bar": 23}, {"bar": 24}, {"notbar": [7]}, {"bar": 25}]}, {"qux": [{"baz": []}, {"baz": [1, 2, 3]}, {"baz": [4]}, {"baz": []}]}], "otherkey": {"foo": [{"bar": 1}, {"bar": 2}, {"notbar": 3}, {"bar": 4}]}}]}, "cases": [{"expression": "reservations[].instances[].foo[].bar", "result": [1, 2, 4, 5, 6, 8]}, {"expression": "reservations[].instances[].foo[].baz", "result": []}, {"expression": "reservations[].instances[].notfoo[].bar", "result": [20, 21, 22, 23, 24, 25]}, {"expression": "reservations[].instances[].notfoo[].notbar", "result": [[7], [7]]}, {"expression": "reservations[].notinstances[].foo", "result": []}, {"expression": "reservations[].instances[].foo[].notbar", "result": [3, [7]]}, {"expression": "reservations[].instances[].bar[].baz", "result": [[1], [2], [3], [4]]}, {"expression": "reservations[].instances[].baz[].baz", "result": [[1, 2], [], [], [3, 4]]}, {"expression": "reservations[].instances[].qux[].baz", "result": [[], [1, 2, 3], [4], [], [], [1, 2, 3], [4], []]}, {"expression": "reservations[].instances[].qux[].baz[]", "result": [1, 2, 3, 4, 1, 2, 3, 4]}]}, {"given": {"foo": [[["one", "two"], ["three", "four"]], [["five", "six"], ["seven", "eight"]], [["nine"], ["ten"]]]}, "cases": [{"expression": "foo[]", "result": [["one", "two"], ["three", "four"], ["five", "six"], ["seven", "eight"], ["nine"], ["ten"]]}, {"expression": "foo[][0]", "result": ["one", "three", "five", "seven", "nine", "ten"]}, {"expression": "foo[][1]", "result": ["two", "four", "six", "eight"]}, {"expression": "foo[][0][0]", "result": []}, {"expression": "foo[][2][2]", "result": []}, {"expression": "foo[][0][0][100]", "result": []}]}, {"given": {"foo": [{"bar": [{"qux": 2, "baz": 1}, {"qux": 4, "baz": 3}]}, {"bar": [{"qux": 6, "baz": 5}, {"qux": 8, "baz": 7}]}]}, "cases": [{"expression": "foo", "result": [{"bar": [{"qux": 2, "baz": 1}, {"qux": 4, "baz": 3}]}, {"bar": [{"qux": 6, "baz": 5}, {"qux": 8, "baz": 7}]}]}, {"expression": "foo[]", "result": [{"bar": [{"qux": 2, "baz": 1}, {"qux": 4, "baz": 3}]}, {"bar": [{"qux": 6, "baz": 5}, {"qux": 8, "baz": 7}]}]}, {"expression": "foo[].bar", "result": [[{"qux": 2, "baz": 1}, {"qux": 4, "baz": 3}], [{"qux": 6, "baz": 5}, {"qux": 8, "baz": 7}]]}, {"expression": "foo[].bar[]", "result": [{"qux": 2, "baz": 1}, {"qux": 4, "baz": 3}, {"qux": 6, "baz": 5}, {"qux": 8, "baz": 7}]}, {"expression": "foo[].bar[].baz", "result": [1, 3, 5, 7]}]}, {"given": {"string": "string", "hash": {"foo": "bar", "bar": "baz"}, "number": 23, "nullvalue": null}, "cases": [{"expression": "string[]", "result": null}, {"expression": "hash[]", "result": null}, {"expression": "number[]", "result": null}, {"expression": "nullvalue[]", "result": null}, {"expression": "string[].foo", "result": null}, {"expression": "hash[].foo", "result": null}, {"expression": "number[].foo", "result": null}, {"expression": "nullvalue[].foo", "result": null}, {"expression": "nullvalue[].foo[].bar", "result": null}]}]