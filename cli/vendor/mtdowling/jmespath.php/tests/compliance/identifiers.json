[{"given": {"__L": true}, "cases": [{"expression": "__L", "result": true}]}, {"given": {"!\r": true}, "cases": [{"expression": "\"!\\r\"", "result": true}]}, {"given": {"Y_1623": true}, "cases": [{"expression": "Y_1623", "result": true}]}, {"given": {"x": true}, "cases": [{"expression": "x", "result": true}]}, {"given": {"\tF캻": true}, "cases": [{"expression": "\"\\tF\\uCebb\"", "result": true}]}, {"given": {" \t": true}, "cases": [{"expression": "\" \\t\"", "result": true}]}, {"given": {" ": true}, "cases": [{"expression": "\" \"", "result": true}]}, {"given": {"v2": true}, "cases": [{"expression": "v2", "result": true}]}, {"given": {"\t": true}, "cases": [{"expression": "\"\\t\"", "result": true}]}, {"given": {"_X": true}, "cases": [{"expression": "_X", "result": true}]}, {"given": {"\t4򆤕": true}, "cases": [{"expression": "\"\\t4\\ud9da\\udd15\"", "result": true}]}, {"given": {"v24_W": true}, "cases": [{"expression": "v24_W", "result": true}]}, {"given": {"H": true}, "cases": [{"expression": "\"H\"", "result": true}]}, {"given": {"\f": true}, "cases": [{"expression": "\"\\f\"", "result": true}]}, {"given": {"E4": true}, "cases": [{"expression": "\"E4\"", "result": true}]}, {"given": {"!": true}, "cases": [{"expression": "\"!\"", "result": true}]}, {"given": {"tM": true}, "cases": [{"expression": "tM", "result": true}]}, {"given": {" [": true}, "cases": [{"expression": "\" [\"", "result": true}]}, {"given": {"R!": true}, "cases": [{"expression": "\"R!\"", "result": true}]}, {"given": {"_6W": true}, "cases": [{"expression": "_6W", "result": true}]}, {"given": {"ꮡ\r": true}, "cases": [{"expression": "\"\\uaBA1\\r\"", "result": true}]}, {"given": {"tL7": true}, "cases": [{"expression": "tL7", "result": true}]}, {"given": {"<<U\t": true}, "cases": [{"expression": "\"<<U\\t\"", "result": true}]}, {"given": {"믎﫻": true}, "cases": [{"expression": "\"\\ubBcE\\ufAfB\"", "result": true}]}, {"given": {"sNA_": true}, "cases": [{"expression": "sNA_", "result": true}]}, {"given": {"9": true}, "cases": [{"expression": "\"9\"", "result": true}]}, {"given": {"\\\b񂲃": true}, "cases": [{"expression": "\"\\\\\\b\\ud8cb\\udc83\"", "result": true}]}, {"given": {"r": true}, "cases": [{"expression": "\"r\"", "result": true}]}, {"given": {"Q": true}, "cases": [{"expression": "Q", "result": true}]}, {"given": {"_Q__7GL8": true}, "cases": [{"expression": "_Q__7GL8", "result": true}]}, {"given": {"\\": true}, "cases": [{"expression": "\"\\\\\"", "result": true}]}, {"given": {"RR9_": true}, "cases": [{"expression": "RR9_", "result": true}]}, {"given": {"\r\f:": true}, "cases": [{"expression": "\"\\r\\f:\"", "result": true}]}, {"given": {"r7": true}, "cases": [{"expression": "r7", "result": true}]}, {"given": {"-": true}, "cases": [{"expression": "\"-\"", "result": true}]}, {"given": {"p9": true}, "cases": [{"expression": "p9", "result": true}]}, {"given": {"__": true}, "cases": [{"expression": "__", "result": true}]}, {"given": {"\b\t": true}, "cases": [{"expression": "\"\\b\\t\"", "result": true}]}, {"given": {"O_": true}, "cases": [{"expression": "O_", "result": true}]}, {"given": {"_r_8": true}, "cases": [{"expression": "_r_8", "result": true}]}, {"given": {"_j": true}, "cases": [{"expression": "_j", "result": true}]}, {"given": {":": true}, "cases": [{"expression": "\":\"", "result": true}]}, {"given": {"\rB": true}, "cases": [{"expression": "\"\\rB\"", "result": true}]}, {"given": {"Obf": true}, "cases": [{"expression": "Obf", "result": true}]}, {"given": {"\n": true}, "cases": [{"expression": "\"\\n\"", "result": true}]}, {"given": {"\f󥌳": true}, "cases": [{"expression": "\"\\f󥌳\"", "result": true}]}, {"given": {"\\俜": true}, "cases": [{"expression": "\"\\\\\\u4FDc\"", "result": true}]}, {"given": {"\r": true}, "cases": [{"expression": "\"\\r\"", "result": true}]}, {"given": {"m_": true}, "cases": [{"expression": "m_", "result": true}]}, {"given": {"\r\fB ": true}, "cases": [{"expression": "\"\\r\\fB \"", "result": true}]}, {"given": {"+\"\"": true}, "cases": [{"expression": "\"+\\\"\\\"\"", "result": true}]}, {"given": {"Mg": true}, "cases": [{"expression": "Mg", "result": true}]}, {"given": {"\"!/": true}, "cases": [{"expression": "\"\\\"!\\/\"", "result": true}]}, {"given": {"7\"": true}, "cases": [{"expression": "\"7\\\"\"", "result": true}]}, {"given": {"\\󞢤S": true}, "cases": [{"expression": "\"\\\\󞢤S\"", "result": true}]}, {"given": {"\"": true}, "cases": [{"expression": "\"\\\"\"", "result": true}]}, {"given": {"Kl": true}, "cases": [{"expression": "Kl", "result": true}]}, {"given": {"\b\b": true}, "cases": [{"expression": "\"\\b\\b\"", "result": true}]}, {"given": {">": true}, "cases": [{"expression": "\">\"", "result": true}]}, {"given": {"hvu": true}, "cases": [{"expression": "hvu", "result": true}]}, {"given": {"; !": true}, "cases": [{"expression": "\"; !\"", "result": true}]}, {"given": {"hU": true}, "cases": [{"expression": "hU", "result": true}]}, {"given": {"!I\n/": true}, "cases": [{"expression": "\"!I\\n\\/\"", "result": true}]}, {"given": {"": true}, "cases": [{"expression": "\"\\uEEbF\"", "result": true}]}, {"given": {"U)\t": true}, "cases": [{"expression": "\"U)\\t\"", "result": true}]}, {"given": {"fa0_9": true}, "cases": [{"expression": "fa0_9", "result": true}]}, {"given": {"/": true}, "cases": [{"expression": "\"/\"", "result": true}]}, {"given": {"Gy": true}, "cases": [{"expression": "Gy", "result": true}]}, {"given": {"\b": true}, "cases": [{"expression": "\"\\b\"", "result": true}]}, {"given": {"<": true}, "cases": [{"expression": "\"<\"", "result": true}]}, {"given": {"\t": true}, "cases": [{"expression": "\"\\t\"", "result": true}]}, {"given": {"\t&\\\r": true}, "cases": [{"expression": "\"\\t&\\\\\\r\"", "result": true}]}, {"given": {"#": true}, "cases": [{"expression": "\"#\"", "result": true}]}, {"given": {"B__": true}, "cases": [{"expression": "B__", "result": true}]}, {"given": {"\nS \n": true}, "cases": [{"expression": "\"\\nS \\n\"", "result": true}]}, {"given": {"Bp": true}, "cases": [{"expression": "Bp", "result": true}]}, {"given": {",\t;": true}, "cases": [{"expression": "\",\\t;\"", "result": true}]}, {"given": {"B_q": true}, "cases": [{"expression": "B_q", "result": true}]}, {"given": {"/+\t\n\b!Z": true}, "cases": [{"expression": "\"\\/+\\t\\n\\b!Z\"", "result": true}]}, {"given": {"󇟇\\ueFAc": true}, "cases": [{"expression": "\"󇟇\\\\ueFAc\"", "result": true}]}, {"given": {":\f": true}, "cases": [{"expression": "\":\\f\"", "result": true}]}, {"given": {"/": true}, "cases": [{"expression": "\"\\/\"", "result": true}]}, {"given": {"_BW_6Hg_Gl": true}, "cases": [{"expression": "_BW_6Hg_Gl", "result": true}]}, {"given": {"􃰂": true}, "cases": [{"expression": "\"􃰂\"", "result": true}]}, {"given": {"zs1DC": true}, "cases": [{"expression": "zs1DC", "result": true}]}, {"given": {"__434": true}, "cases": [{"expression": "__434", "result": true}]}, {"given": {"󵅁": true}, "cases": [{"expression": "\"󵅁\"", "result": true}]}, {"given": {"Z_5": true}, "cases": [{"expression": "Z_5", "result": true}]}, {"given": {"z_M_": true}, "cases": [{"expression": "z_M_", "result": true}]}, {"given": {"YU_2": true}, "cases": [{"expression": "YU_2", "result": true}]}, {"given": {"_0": true}, "cases": [{"expression": "_0", "result": true}]}, {"given": {"\b+": true}, "cases": [{"expression": "\"\\b+\"", "result": true}]}, {"given": {"\"": true}, "cases": [{"expression": "\"\\\"\"", "result": true}]}, {"given": {"D7": true}, "cases": [{"expression": "D7", "result": true}]}, {"given": {"_62L": true}, "cases": [{"expression": "_62L", "result": true}]}, {"given": {"\tK\t": true}, "cases": [{"expression": "\"\\tK\\t\"", "result": true}]}, {"given": {"\n\\\f": true}, "cases": [{"expression": "\"\\n\\\\\\f\"", "result": true}]}, {"given": {"I_": true}, "cases": [{"expression": "I_", "result": true}]}, {"given": {"W_a0_": true}, "cases": [{"expression": "W_a0_", "result": true}]}, {"given": {"BQ": true}, "cases": [{"expression": "BQ", "result": true}]}, {"given": {"\tX$ꮻ": true}, "cases": [{"expression": "\"\\tX$\\uABBb\"", "result": true}]}, {"given": {"Z9": true}, "cases": [{"expression": "Z9", "result": true}]}, {"given": {"\b%\"򞄏": true}, "cases": [{"expression": "\"\\b%\\\"򞄏\"", "result": true}]}, {"given": {"_F": true}, "cases": [{"expression": "_F", "result": true}]}, {"given": {"!,": true}, "cases": [{"expression": "\"!,\"", "result": true}]}, {"given": {"\"!": true}, "cases": [{"expression": "\"\\\"!\"", "result": true}]}, {"given": {"Hh": true}, "cases": [{"expression": "Hh", "result": true}]}, {"given": {"&": true}, "cases": [{"expression": "\"&\"", "result": true}]}, {"given": {"9\r\\R": true}, "cases": [{"expression": "\"9\\r\\\\R\"", "result": true}]}, {"given": {"M_k": true}, "cases": [{"expression": "M_k", "result": true}]}, {"given": {"!\b\n󑩒\"\"": true}, "cases": [{"expression": "\"!\\b\\n󑩒\\\"\\\"\"", "result": true}]}, {"given": {"6": true}, "cases": [{"expression": "\"6\"", "result": true}]}, {"given": {"_7": true}, "cases": [{"expression": "_7", "result": true}]}, {"given": {"0": true}, "cases": [{"expression": "\"0\"", "result": true}]}, {"given": {"\\8\\": true}, "cases": [{"expression": "\"\\\\8\\\\\"", "result": true}]}, {"given": {"b7eo": true}, "cases": [{"expression": "b7eo", "result": true}]}, {"given": {"xIUo9": true}, "cases": [{"expression": "xIUo9", "result": true}]}, {"given": {"5": true}, "cases": [{"expression": "\"5\"", "result": true}]}, {"given": {"?": true}, "cases": [{"expression": "\"?\"", "result": true}]}, {"given": {"sU": true}, "cases": [{"expression": "sU", "result": true}]}, {"given": {"VH2&H\\/": true}, "cases": [{"expression": "\"VH2&H\\\\\\/\"", "result": true}]}, {"given": {"_C": true}, "cases": [{"expression": "_C", "result": true}]}, {"given": {"_": true}, "cases": [{"expression": "_", "result": true}]}, {"given": {"<\t": true}, "cases": [{"expression": "\"<\\t\"", "result": true}]}, {"given": {"𝄞": true}, "cases": [{"expression": "\"\\uD834\\uDD1E\"", "result": true}]}]