[{"given": {"foo.bar": "dot", "foo bar": "space", "foo\nbar": "newline", "foo\"bar": "doublequote", "c:\\\\windows\\path": "windows", "/unix/path": "unix", "\"\"\"": "threequotes", "bar": {"baz": "qux"}}, "cases": [{"expression": "\"foo.bar\"", "result": "dot"}, {"expression": "\"foo bar\"", "result": "space"}, {"expression": "\"foo\\nbar\"", "result": "newline"}, {"expression": "\"foo\\\"bar\"", "result": "doublequote"}, {"expression": "\"c:\\\\\\\\windows\\\\path\"", "result": "windows"}, {"expression": "\"/unix/path\"", "result": "unix"}, {"expression": "\"\\\"\\\"\\\"\"", "result": "threequotes"}, {"expression": "\"bar\".\"baz\"", "result": "qux"}]}]