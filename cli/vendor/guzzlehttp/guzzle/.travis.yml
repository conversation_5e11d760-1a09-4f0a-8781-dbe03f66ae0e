language: php

sudo: false

php:
  - 5.5
  - 5.6
  - 7.0
  - hhvm

before_script:
  - curl --version
  - composer self-update
  - composer install --no-interaction --prefer-source --dev
  - ~/.nvm/nvm.sh install v0.6.14
  - ~/.nvm/nvm.sh run v0.6.14
  - '[ "$TRAVIS_PHP_VERSION" != "7.0" ] || echo "xdebug.overload_var_dump = 1" >> ~/.phpenv/versions/$(phpenv version-name)/etc/php.ini'

script: make test

matrix:
  allow_failures:
    - php: hhvm
  fast_finish: true

before_deploy:
  - rvm 1.9.3 do gem install mime-types -v 2.6.2
  - make package

deploy:
  provider: releases
  api_key:
    secure: UpypqlYgsU68QT/x40YzhHXvzWjFwCNo9d+G8KAdm7U9+blFfcWhV1aMdzugvPMl6woXgvJj7qHq5tAL4v6oswCORhpSBfLgOQVFaica5LiHsvWlAedOhxGmnJqMTwuepjBCxXhs3+I8Kof1n4oUL9gKytXjOVCX/f7XU1HiinU=
  file:
    - build/artifacts/guzzle.phar
    - build/artifacts/guzzle.zip
  on:
    repo: guzzle/guzzle
    tags: true
    all_branches: true
    php: 5.5
