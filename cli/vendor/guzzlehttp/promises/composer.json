{"name": "guzzlehttp/promises", "type": "library", "description": "Guzzle promises library", "keywords": ["promise"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}