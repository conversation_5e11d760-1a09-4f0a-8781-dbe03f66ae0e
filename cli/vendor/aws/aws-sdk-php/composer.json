{"name": "aws/aws-sdk-php", "homepage": "http://aws.amazon.com/sdkforphp", "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "keywords": ["aws", "amazon", "sdk", "s3", "ec2", "dynamodb", "cloud", "glacier"], "type": "library", "license": "Apache-2.0", "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues"}, "require": {"php": ">=5.5", "guzzlehttp/guzzle": "~5.3|~6.0.1|~6.1", "guzzlehttp/psr7": "~1.0", "guzzlehttp/promises": "~1.0", "mtdowling/jmespath.php": "~2.2"}, "require-dev": {"ext-openssl": "*", "ext-pcre": "*", "ext-spl": "*", "ext-json": "*", "ext-dom": "*", "ext-simplexml": "*", "phpunit/phpunit": "~4.0|~5.0", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "aws/aws-php-sns-message-validator": "~1.0", "nette/neon": "^2.3", "andrewsville/php-token-reflection": "^1.4", "psr/cache": "^1.0"}, "suggest": {"ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-curl": "To send requests using cURL", "doctrine/cache": "To use the DoctrineCacheAdapter", "aws/aws-php-sns-message-validator": "To validate incoming SNS notifications"}, "autoload": {"psr-4": {"Aws\\": "src/"}, "files": ["src/functions.php"]}, "autoload-dev": {"psr-4": {"Aws\\Test\\": "tests/"}, "classmap": ["build/"]}, "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}