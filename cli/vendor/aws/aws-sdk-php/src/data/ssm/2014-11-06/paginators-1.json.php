<?php
// This file was auto-generated from sdk-root/src/data/ssm/2014-11-06/paginators-1.json
return [ 'pagination' => [ 'ListAssociations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Associations', ], 'ListCommandInvocations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'CommandInvocations', ], 'ListCommands' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Commands', ], 'ListDocuments' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'DocumentIdentifiers', ], ],];
