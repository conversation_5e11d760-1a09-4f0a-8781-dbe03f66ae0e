<?php
// This file was auto-generated from sdk-root/src/data/redshift/2012-12-01/paginators-1.json
return [ 'pagination' => [ 'DescribeClusterParameterGroups' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'ParameterGroups', ], 'DescribeClusterParameters' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'Parameters', ], 'DescribeClusterSecurityGroups' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'ClusterSecurityGroups', ], 'DescribeClusterSnapshots' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'Snapshots', ], 'DescribeClusterSubnetGroups' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'ClusterSubnetGroups', ], 'DescribeClusterVersions' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'ClusterVersions', ], 'DescribeClusters' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'Clusters', ], 'DescribeDefaultClusterParameters' => [ 'input_token' => 'Marker', 'output_token' => 'DefaultClusterParameters.Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'DefaultClusterParameters.Parameters', ], 'DescribeEventSubscriptions' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'EventSubscriptionsList', ], 'DescribeEvents' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'Events', ], 'DescribeHsmClientCertificates' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'HsmClientCertificates', ], 'DescribeHsmConfigurations' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'HsmConfigurations', ], 'DescribeOrderableClusterOptions' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'OrderableClusterOptions', ], 'DescribeReservedNodeOfferings' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'ReservedNodeOfferings', ], 'DescribeReservedNodes' => [ 'input_token' => 'Marker', 'output_token' => 'Marker', 'limit_key' => 'MaxRecords', 'result_key' => 'ReservedNodes', ], ],];
