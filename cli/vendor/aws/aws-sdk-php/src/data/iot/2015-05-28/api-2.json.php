<?php
// This file was auto-generated from sdk-root/src/data/iot/2015-05-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-05-28', 'endpointPrefix' => 'iot', 'serviceFullName' => 'AWS IoT', 'signatureVersion' => 'v4', 'signingName' => 'execute-api', 'protocol' => 'rest-json', ], 'operations' => [ 'AcceptCertificateTransfer' => [ 'name' => 'AcceptCertificateTransfer', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/accept-certificate-transfer/{certificateId}', ], 'input' => [ 'shape' => 'AcceptCertificateTransferRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'TransferAlreadyCompletedException', 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'AttachPrincipalPolicy' => [ 'name' => 'AttachPrincipalPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/principal-policies/{policyName}', ], 'input' => [ 'shape' => 'AttachPrincipalPolicyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'LimitExceededException', 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], ], ], 'AttachThingPrincipal' => [ 'name' => 'AttachThingPrincipal', 'http' => [ 'method' => 'PUT', 'requestUri' => '/things/{thingName}/principals', ], 'input' => [ 'shape' => 'AttachThingPrincipalRequest', ], 'output' => [ 'shape' => 'AttachThingPrincipalResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'CancelCertificateTransfer' => [ 'name' => 'CancelCertificateTransfer', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/cancel-certificate-transfer/{certificateId}', ], 'input' => [ 'shape' => 'CancelCertificateTransferRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'TransferAlreadyCompletedException', 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'CreateCertificateFromCsr' => [ 'name' => 'CreateCertificateFromCsr', 'http' => [ 'method' => 'POST', 'requestUri' => '/certificates', ], 'input' => [ 'shape' => 'CreateCertificateFromCsrRequest', ], 'output' => [ 'shape' => 'CreateCertificateFromCsrResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'CreateKeysAndCertificate' => [ 'name' => 'CreateKeysAndCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/keys-and-certificate', ], 'input' => [ 'shape' => 'CreateKeysAndCertificateRequest', ], 'output' => [ 'shape' => 'CreateKeysAndCertificateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'CreatePolicy' => [ 'name' => 'CreatePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/policies/{policyName}', ], 'input' => [ 'shape' => 'CreatePolicyRequest', ], 'output' => [ 'shape' => 'CreatePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], [ 'shape' => 'MalformedPolicyException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'CreatePolicyVersion' => [ 'name' => 'CreatePolicyVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/policies/{policyName}/version', ], 'input' => [ 'shape' => 'CreatePolicyVersionRequest', ], 'output' => [ 'shape' => 'CreatePolicyVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'MalformedPolicyException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'VersionsLimitExceededException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'CreateThing' => [ 'name' => 'CreateThing', 'http' => [ 'method' => 'POST', 'requestUri' => '/things/{thingName}', ], 'input' => [ 'shape' => 'CreateThingRequest', ], 'output' => [ 'shape' => 'CreateThingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'ResourceAlreadyExistsException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], ], ], 'CreateTopicRule' => [ 'name' => 'CreateTopicRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/rules/{ruleName}', ], 'input' => [ 'shape' => 'CreateTopicRuleRequest', ], 'errors' => [ [ 'shape' => 'SqlParseException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'InternalException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ResourceAlreadyExistsException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], ], ], 'DeleteCACertificate' => [ 'name' => 'DeleteCACertificate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/cacertificate/{certificateId}', ], 'input' => [ 'shape' => 'DeleteCACertificateRequest', ], 'output' => [ 'shape' => 'DeleteCACertificateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'CertificateStateException', 'error' => [ 'httpStatusCode' => 406, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], ], ], 'DeleteCertificate' => [ 'name' => 'DeleteCertificate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/certificates/{certificateId}', ], 'input' => [ 'shape' => 'DeleteCertificateRequest', ], 'errors' => [ [ 'shape' => 'CertificateStateException', 'error' => [ 'httpStatusCode' => 406, ], 'exception' => true, ], [ 'shape' => 'DeleteConflictException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], ], ], 'DeletePolicy' => [ 'name' => 'DeletePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/policies/{policyName}', ], 'input' => [ 'shape' => 'DeletePolicyRequest', ], 'errors' => [ [ 'shape' => 'DeleteConflictException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'DeletePolicyVersion' => [ 'name' => 'DeletePolicyVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/policies/{policyName}/version/{policyVersionId}', ], 'input' => [ 'shape' => 'DeletePolicyVersionRequest', ], 'errors' => [ [ 'shape' => 'DeleteConflictException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'DeleteRegistrationCode' => [ 'name' => 'DeleteRegistrationCode', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/registrationcode', ], 'input' => [ 'shape' => 'DeleteRegistrationCodeRequest', ], 'output' => [ 'shape' => 'DeleteRegistrationCodeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'DeleteThing' => [ 'name' => 'DeleteThing', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/things/{thingName}', ], 'input' => [ 'shape' => 'DeleteThingRequest', ], 'output' => [ 'shape' => 'DeleteThingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'DeleteTopicRule' => [ 'name' => 'DeleteTopicRule', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/rules/{ruleName}', ], 'input' => [ 'shape' => 'DeleteTopicRuleRequest', ], 'errors' => [ [ 'shape' => 'InternalException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], ], ], 'DescribeCACertificate' => [ 'name' => 'DescribeCACertificate', 'http' => [ 'method' => 'GET', 'requestUri' => '/cacertificate/{certificateId}', ], 'input' => [ 'shape' => 'DescribeCACertificateRequest', ], 'output' => [ 'shape' => 'DescribeCACertificateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], ], ], 'DescribeCertificate' => [ 'name' => 'DescribeCertificate', 'http' => [ 'method' => 'GET', 'requestUri' => '/certificates/{certificateId}', ], 'input' => [ 'shape' => 'DescribeCertificateRequest', ], 'output' => [ 'shape' => 'DescribeCertificateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], ], ], 'DescribeEndpoint' => [ 'name' => 'DescribeEndpoint', 'http' => [ 'method' => 'GET', 'requestUri' => '/endpoint', ], 'input' => [ 'shape' => 'DescribeEndpointRequest', ], 'output' => [ 'shape' => 'DescribeEndpointResponse', ], 'errors' => [ [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], ], ], 'DescribeThing' => [ 'name' => 'DescribeThing', 'http' => [ 'method' => 'GET', 'requestUri' => '/things/{thingName}', ], 'input' => [ 'shape' => 'DescribeThingRequest', ], 'output' => [ 'shape' => 'DescribeThingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'DetachPrincipalPolicy' => [ 'name' => 'DetachPrincipalPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/principal-policies/{policyName}', ], 'input' => [ 'shape' => 'DetachPrincipalPolicyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'DetachThingPrincipal' => [ 'name' => 'DetachThingPrincipal', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/things/{thingName}/principals', ], 'input' => [ 'shape' => 'DetachThingPrincipalRequest', ], 'output' => [ 'shape' => 'DetachThingPrincipalResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'DisableTopicRule' => [ 'name' => 'DisableTopicRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/rules/{ruleName}/disable', ], 'input' => [ 'shape' => 'DisableTopicRuleRequest', ], 'errors' => [ [ 'shape' => 'InternalException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], ], ], 'EnableTopicRule' => [ 'name' => 'EnableTopicRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/rules/{ruleName}/enable', ], 'input' => [ 'shape' => 'EnableTopicRuleRequest', ], 'errors' => [ [ 'shape' => 'InternalException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], ], ], 'GetLoggingOptions' => [ 'name' => 'GetLoggingOptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/loggingOptions', ], 'input' => [ 'shape' => 'GetLoggingOptionsRequest', ], 'output' => [ 'shape' => 'GetLoggingOptionsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], ], ], 'GetPolicy' => [ 'name' => 'GetPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/policies/{policyName}', ], 'input' => [ 'shape' => 'GetPolicyRequest', ], 'output' => [ 'shape' => 'GetPolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'GetPolicyVersion' => [ 'name' => 'GetPolicyVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/policies/{policyName}/version/{policyVersionId}', ], 'input' => [ 'shape' => 'GetPolicyVersionRequest', ], 'output' => [ 'shape' => 'GetPolicyVersionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'GetRegistrationCode' => [ 'name' => 'GetRegistrationCode', 'http' => [ 'method' => 'GET', 'requestUri' => '/registrationcode', ], 'input' => [ 'shape' => 'GetRegistrationCodeRequest', ], 'output' => [ 'shape' => 'GetRegistrationCodeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'GetTopicRule' => [ 'name' => 'GetTopicRule', 'http' => [ 'method' => 'GET', 'requestUri' => '/rules/{ruleName}', ], 'input' => [ 'shape' => 'GetTopicRuleRequest', ], 'output' => [ 'shape' => 'GetTopicRuleResponse', ], 'errors' => [ [ 'shape' => 'InternalException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], ], ], 'ListCACertificates' => [ 'name' => 'ListCACertificates', 'http' => [ 'method' => 'GET', 'requestUri' => '/cacertificates', ], 'input' => [ 'shape' => 'ListCACertificatesRequest', ], 'output' => [ 'shape' => 'ListCACertificatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'ListCertificates' => [ 'name' => 'ListCertificates', 'http' => [ 'method' => 'GET', 'requestUri' => '/certificates', ], 'input' => [ 'shape' => 'ListCertificatesRequest', ], 'output' => [ 'shape' => 'ListCertificatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'ListCertificatesByCA' => [ 'name' => 'ListCertificatesByCA', 'http' => [ 'method' => 'GET', 'requestUri' => '/certificates-by-ca/{caCertificateId}', ], 'input' => [ 'shape' => 'ListCertificatesByCARequest', ], 'output' => [ 'shape' => 'ListCertificatesByCAResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'ListPolicies' => [ 'name' => 'ListPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/policies', ], 'input' => [ 'shape' => 'ListPoliciesRequest', ], 'output' => [ 'shape' => 'ListPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'ListPolicyVersions' => [ 'name' => 'ListPolicyVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/policies/{policyName}/version', ], 'input' => [ 'shape' => 'ListPolicyVersionsRequest', ], 'output' => [ 'shape' => 'ListPolicyVersionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'ListPrincipalPolicies' => [ 'name' => 'ListPrincipalPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/principal-policies', ], 'input' => [ 'shape' => 'ListPrincipalPoliciesRequest', ], 'output' => [ 'shape' => 'ListPrincipalPoliciesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'ListPrincipalThings' => [ 'name' => 'ListPrincipalThings', 'http' => [ 'method' => 'GET', 'requestUri' => '/principals/things', ], 'input' => [ 'shape' => 'ListPrincipalThingsRequest', ], 'output' => [ 'shape' => 'ListPrincipalThingsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'ListThingPrincipals' => [ 'name' => 'ListThingPrincipals', 'http' => [ 'method' => 'GET', 'requestUri' => '/things/{thingName}/principals', ], 'input' => [ 'shape' => 'ListThingPrincipalsRequest', ], 'output' => [ 'shape' => 'ListThingPrincipalsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'ListThings' => [ 'name' => 'ListThings', 'http' => [ 'method' => 'GET', 'requestUri' => '/things', ], 'input' => [ 'shape' => 'ListThingsRequest', ], 'output' => [ 'shape' => 'ListThingsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'ListTopicRules' => [ 'name' => 'ListTopicRules', 'http' => [ 'method' => 'GET', 'requestUri' => '/rules', ], 'input' => [ 'shape' => 'ListTopicRulesRequest', ], 'output' => [ 'shape' => 'ListTopicRulesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], ], ], 'RegisterCACertificate' => [ 'name' => 'RegisterCACertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/cacertificate', ], 'input' => [ 'shape' => 'RegisterCACertificateRequest', ], 'output' => [ 'shape' => 'RegisterCACertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], [ 'shape' => 'RegistrationCodeValidationException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'CertificateValidationException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'LimitExceededException', 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'RegisterCertificate' => [ 'name' => 'RegisterCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/certificate/register', ], 'input' => [ 'shape' => 'RegisterCertificateRequest', ], 'output' => [ 'shape' => 'RegisterCertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'CertificateValidationException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'CertificateStateException', 'error' => [ 'httpStatusCode' => 406, ], 'exception' => true, ], [ 'shape' => 'CertificateConflictException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'RejectCertificateTransfer' => [ 'name' => 'RejectCertificateTransfer', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/reject-certificate-transfer/{certificateId}', ], 'input' => [ 'shape' => 'RejectCertificateTransferRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'TransferAlreadyCompletedException', 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'ReplaceTopicRule' => [ 'name' => 'ReplaceTopicRule', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/rules/{ruleName}', ], 'input' => [ 'shape' => 'ReplaceTopicRuleRequest', ], 'errors' => [ [ 'shape' => 'SqlParseException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'InternalException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], ], ], 'SetDefaultPolicyVersion' => [ 'name' => 'SetDefaultPolicyVersion', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/policies/{policyName}/version/{policyVersionId}', ], 'input' => [ 'shape' => 'SetDefaultPolicyVersionRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'SetLoggingOptions' => [ 'name' => 'SetLoggingOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/loggingOptions', ], 'input' => [ 'shape' => 'SetLoggingOptionsRequest', ], 'errors' => [ [ 'shape' => 'InternalException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], ], ], 'TransferCertificate' => [ 'name' => 'TransferCertificate', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/transfer-certificate/{certificateId}', ], 'input' => [ 'shape' => 'TransferCertificateRequest', ], 'output' => [ 'shape' => 'TransferCertificateResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'CertificateStateException', 'error' => [ 'httpStatusCode' => 406, ], 'exception' => true, ], [ 'shape' => 'TransferConflictException', 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'UpdateCACertificate' => [ 'name' => 'UpdateCACertificate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/cacertificate/{certificateId}', ], 'input' => [ 'shape' => 'UpdateCACertificateRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'UpdateCertificate' => [ 'name' => 'UpdateCertificate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/certificates/{certificateId}', ], 'input' => [ 'shape' => 'UpdateCertificateRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], [ 'shape' => 'CertificateStateException', 'error' => [ 'httpStatusCode' => 406, ], 'exception' => true, ], [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], ], ], 'UpdateThing' => [ 'name' => 'UpdateThing', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/things/{thingName}', ], 'input' => [ 'shape' => 'UpdateThingRequest', ], 'output' => [ 'shape' => 'UpdateThingResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], [ 'shape' => 'ThrottlingException', 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], [ 'shape' => 'UnauthorizedException', 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], [ 'shape' => 'ServiceUnavailableException', 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'InternalFailureException', 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], [ 'shape' => 'ResourceNotFoundException', 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], ], ], ], 'shapes' => [ 'AcceptCertificateTransferRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], 'setAsActive' => [ 'shape' => 'SetAsActive', 'location' => 'querystring', 'locationName' => 'setAsActive', ], ], ], 'Action' => [ 'type' => 'structure', 'members' => [ 'dynamoDB' => [ 'shape' => 'DynamoDBAction', ], 'lambda' => [ 'shape' => 'LambdaAction', ], 'sns' => [ 'shape' => 'SnsAction', ], 'sqs' => [ 'shape' => 'SqsAction', ], 'kinesis' => [ 'shape' => 'KinesisAction', ], 'republish' => [ 'shape' => 'RepublishAction', ], 's3' => [ 'shape' => 'S3Action', ], 'firehose' => [ 'shape' => 'FirehoseAction', ], 'cloudwatchMetric' => [ 'shape' => 'CloudwatchMetricAction', ], 'cloudwatchAlarm' => [ 'shape' => 'CloudwatchAlarmAction', ], 'elasticsearch' => [ 'shape' => 'ElasticsearchAction', ], ], ], 'ActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Action', ], 'min' => 0, 'max' => 10, ], 'AlarmName' => [ 'type' => 'string', ], 'AscendingOrder' => [ 'type' => 'boolean', ], 'AttachPrincipalPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'principal', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-iot-principal', ], ], ], 'AttachThingPrincipalRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', 'principal', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-principal', ], ], ], 'AttachThingPrincipalResponse' => [ 'type' => 'structure', 'members' => [], ], 'AttributeName' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[a-zA-Z0-9_.,@/:#-]+', ], 'AttributePayload' => [ 'type' => 'structure', 'members' => [ 'attributes' => [ 'shape' => 'Attributes', ], ], ], 'AttributeValue' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[a-zA-Z0-9_.,@/:#-]+', ], 'Attributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], ], 'AwsAccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'AwsArn' => [ 'type' => 'string', ], 'AwsIotSqlVersion' => [ 'type' => 'string', ], 'BucketName' => [ 'type' => 'string', ], 'CACertificate' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'status' => [ 'shape' => 'CACertificateStatus', ], 'creationDate' => [ 'shape' => 'DateType', ], ], ], 'CACertificateDescription' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'status' => [ 'shape' => 'CACertificateStatus', ], 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'ownedBy' => [ 'shape' => 'AwsAccountId', ], 'creationDate' => [ 'shape' => 'DateType', ], ], ], 'CACertificateStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'CACertificates' => [ 'type' => 'list', 'member' => [ 'shape' => 'CACertificate', ], ], 'CancelCertificateTransferRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], ], ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'status' => [ 'shape' => 'CertificateStatus', ], 'creationDate' => [ 'shape' => 'DateType', ], ], ], 'CertificateArn' => [ 'type' => 'string', ], 'CertificateConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CertificateDescription' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'caCertificateId' => [ 'shape' => 'CertificateId', ], 'status' => [ 'shape' => 'CertificateStatus', ], 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'ownedBy' => [ 'shape' => 'AwsAccountId', ], 'previousOwnedBy' => [ 'shape' => 'AwsAccountId', ], 'creationDate' => [ 'shape' => 'DateType', ], 'lastModifiedDate' => [ 'shape' => 'DateType', ], 'transferData' => [ 'shape' => 'TransferData', ], ], ], 'CertificateId' => [ 'type' => 'string', 'min' => 64, 'max' => 64, 'pattern' => '(0x)?[a-fA-F0-9]+', ], 'CertificatePem' => [ 'type' => 'string', 'min' => 1, 'max' => 65536, ], 'CertificateSigningRequest' => [ 'type' => 'string', 'min' => 1, ], 'CertificateStateException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 406, ], 'exception' => true, ], 'CertificateStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'REVOKED', 'PENDING_TRANSFER', 'REGISTER_INACTIVE', ], ], 'CertificateValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Certificates' => [ 'type' => 'list', 'member' => [ 'shape' => 'Certificate', ], ], 'ClientId' => [ 'type' => 'string', ], 'CloudwatchAlarmAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'alarmName', 'stateReason', 'stateValue', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'alarmName' => [ 'shape' => 'AlarmName', ], 'stateReason' => [ 'shape' => 'StateReason', ], 'stateValue' => [ 'shape' => 'StateValue', ], ], ], 'CloudwatchMetricAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'metricNamespace', 'metricName', 'metricValue', 'metricUnit', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'metricNamespace' => [ 'shape' => 'MetricNamespace', ], 'metricName' => [ 'shape' => 'MetricName', ], 'metricValue' => [ 'shape' => 'MetricValue', ], 'metricUnit' => [ 'shape' => 'MetricUnit', ], 'metricTimestamp' => [ 'shape' => 'MetricTimestamp', ], ], ], 'CreateCertificateFromCsrRequest' => [ 'type' => 'structure', 'required' => [ 'certificateSigningRequest', ], 'members' => [ 'certificateSigningRequest' => [ 'shape' => 'CertificateSigningRequest', ], 'setAsActive' => [ 'shape' => 'SetAsActive', 'location' => 'querystring', 'locationName' => 'setAsActive', ], ], ], 'CreateCertificateFromCsrResponse' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'certificatePem' => [ 'shape' => 'CertificatePem', ], ], ], 'CreateKeysAndCertificateRequest' => [ 'type' => 'structure', 'members' => [ 'setAsActive' => [ 'shape' => 'SetAsActive', 'location' => 'querystring', 'locationName' => 'setAsActive', ], ], ], 'CreateKeysAndCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'keyPair' => [ 'shape' => 'KeyPair', ], ], ], 'CreatePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyDocument', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], ], ], 'CreatePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyArn' => [ 'shape' => 'PolicyArn', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', ], ], ], 'CreatePolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyDocument', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'setAsDefault' => [ 'shape' => 'SetAsDefault', 'location' => 'querystring', 'locationName' => 'setAsDefault', ], ], ], 'CreatePolicyVersionResponse' => [ 'type' => 'structure', 'members' => [ 'policyArn' => [ 'shape' => 'PolicyArn', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', ], 'isDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], ], ], 'CreateThingRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'attributePayload' => [ 'shape' => 'AttributePayload', ], ], ], 'CreateThingResponse' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'ThingName', ], 'thingArn' => [ 'shape' => 'ThingArn', ], ], ], 'CreateTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', 'topicRulePayload', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], 'topicRulePayload' => [ 'shape' => 'TopicRulePayload', ], ], 'payload' => 'topicRulePayload', ], 'CreatedAtDate' => [ 'type' => 'timestamp', ], 'DateType' => [ 'type' => 'timestamp', ], 'DeleteCACertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], ], ], 'DeleteCACertificateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], ], ], 'DeleteConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'DeletePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], ], ], 'DeletePolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyVersionId', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', 'location' => 'uri', 'locationName' => 'policyVersionId', ], ], ], 'DeleteRegistrationCodeRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRegistrationCodeResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteThingRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], ], ], 'DeleteThingResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTopicRuleRequest' => [ 'type' => 'structure', 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], ], 'required' => [ 'ruleName', ], ], 'DeliveryStreamName' => [ 'type' => 'string', ], 'DescribeCACertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], ], ], 'DescribeCACertificateResponse' => [ 'type' => 'structure', 'members' => [ 'certificateDescription' => [ 'shape' => 'CACertificateDescription', ], ], ], 'DescribeCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], ], ], 'DescribeCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'certificateDescription' => [ 'shape' => 'CertificateDescription', ], ], ], 'DescribeEndpointRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'endpointAddress' => [ 'shape' => 'EndpointAddress', ], ], ], 'DescribeThingRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], ], ], 'DescribeThingResponse' => [ 'type' => 'structure', 'members' => [ 'defaultClientId' => [ 'shape' => 'ClientId', ], 'thingName' => [ 'shape' => 'ThingName', ], 'attributes' => [ 'shape' => 'Attributes', ], ], ], 'Description' => [ 'type' => 'string', ], 'DetachPrincipalPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'principal', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-iot-principal', ], ], ], 'DetachThingPrincipalRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', 'principal', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-principal', ], ], ], 'DetachThingPrincipalResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisableTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'DynamoDBAction' => [ 'type' => 'structure', 'required' => [ 'tableName', 'roleArn', 'hashKeyField', 'hashKeyValue', 'rangeKeyField', 'rangeKeyValue', ], 'members' => [ 'tableName' => [ 'shape' => 'TableName', ], 'roleArn' => [ 'shape' => 'AwsArn', ], 'hashKeyField' => [ 'shape' => 'HashKeyField', ], 'hashKeyValue' => [ 'shape' => 'HashKeyValue', ], 'rangeKeyField' => [ 'shape' => 'RangeKeyField', ], 'rangeKeyValue' => [ 'shape' => 'RangeKeyValue', ], 'payloadField' => [ 'shape' => 'PayloadField', ], ], ], 'ElasticsearchAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'endpoint', 'index', 'type', 'id', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'endpoint' => [ 'shape' => 'ElasticsearchEndpoint', ], 'index' => [ 'shape' => 'ElasticsearchIndex', ], 'type' => [ 'shape' => 'ElasticsearchType', ], 'id' => [ 'shape' => 'ElasticsearchId', ], ], ], 'ElasticsearchEndpoint' => [ 'type' => 'string', 'pattern' => 'https?://.*', ], 'ElasticsearchId' => [ 'type' => 'string', ], 'ElasticsearchIndex' => [ 'type' => 'string', ], 'ElasticsearchType' => [ 'type' => 'string', ], 'EnableTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'EndpointAddress' => [ 'type' => 'string', ], 'FirehoseAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'deliveryStreamName', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'deliveryStreamName' => [ 'shape' => 'DeliveryStreamName', ], ], ], 'FunctionArn' => [ 'type' => 'string', ], 'GetLoggingOptionsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetLoggingOptionsResponse' => [ 'type' => 'structure', 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'logLevel' => [ 'shape' => 'LogLevel', ], ], ], 'GetPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], ], ], 'GetPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyArn' => [ 'shape' => 'PolicyArn', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'defaultVersionId' => [ 'shape' => 'PolicyVersionId', ], ], ], 'GetPolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyVersionId', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', 'location' => 'uri', 'locationName' => 'policyVersionId', ], ], ], 'GetPolicyVersionResponse' => [ 'type' => 'structure', 'members' => [ 'policyArn' => [ 'shape' => 'PolicyArn', ], 'policyName' => [ 'shape' => 'PolicyName', ], 'policyDocument' => [ 'shape' => 'PolicyDocument', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', ], 'isDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], ], ], 'GetRegistrationCodeRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetRegistrationCodeResponse' => [ 'type' => 'structure', 'members' => [ 'registrationCode' => [ 'shape' => 'RegistrationCode', ], ], ], 'GetTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], ], ], 'GetTopicRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ruleArn' => [ 'shape' => 'RuleArn', ], 'rule' => [ 'shape' => 'TopicRule', ], ], ], 'HashKeyField' => [ 'type' => 'string', ], 'HashKeyValue' => [ 'type' => 'string', ], 'InternalException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InternalFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'IsDefaultVersion' => [ 'type' => 'boolean', ], 'IsDisabled' => [ 'type' => 'boolean', ], 'Key' => [ 'type' => 'string', ], 'KeyPair' => [ 'type' => 'structure', 'members' => [ 'PublicKey' => [ 'shape' => 'PublicKey', ], 'PrivateKey' => [ 'shape' => 'PrivateKey', ], ], ], 'KinesisAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'streamName', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'streamName' => [ 'shape' => 'StreamName', ], 'partitionKey' => [ 'shape' => 'PartitionKey', ], ], ], 'LambdaAction' => [ 'type' => 'structure', 'required' => [ 'functionArn', ], 'members' => [ 'functionArn' => [ 'shape' => 'FunctionArn', ], ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'ListCACertificatesRequest' => [ 'type' => 'structure', 'members' => [ 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListCACertificatesResponse' => [ 'type' => 'structure', 'members' => [ 'certificates' => [ 'shape' => 'CACertificates', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListCertificatesByCARequest' => [ 'type' => 'structure', 'required' => [ 'caCertificateId', ], 'members' => [ 'caCertificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'caCertificateId', ], 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListCertificatesByCAResponse' => [ 'type' => 'structure', 'members' => [ 'certificates' => [ 'shape' => 'Certificates', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListCertificatesRequest' => [ 'type' => 'structure', 'members' => [ 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListCertificatesResponse' => [ 'type' => 'structure', 'members' => [ 'certificates' => [ 'shape' => 'Certificates', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'policies' => [ 'shape' => 'Policies', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListPolicyVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], ], ], 'ListPolicyVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'policyVersions' => [ 'shape' => 'PolicyVersions', ], ], ], 'ListPrincipalPoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'principal', ], 'members' => [ 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-iot-principal', ], 'marker' => [ 'shape' => 'Marker', 'location' => 'querystring', 'locationName' => 'marker', ], 'pageSize' => [ 'shape' => 'PageSize', 'location' => 'querystring', 'locationName' => 'pageSize', ], 'ascendingOrder' => [ 'shape' => 'AscendingOrder', 'location' => 'querystring', 'locationName' => 'isAscendingOrder', ], ], ], 'ListPrincipalPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'policies' => [ 'shape' => 'Policies', ], 'nextMarker' => [ 'shape' => 'Marker', ], ], ], 'ListPrincipalThingsRequest' => [ 'type' => 'structure', 'required' => [ 'principal', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'principal' => [ 'shape' => 'Principal', 'location' => 'header', 'locationName' => 'x-amzn-principal', ], ], ], 'ListPrincipalThingsResponse' => [ 'type' => 'structure', 'members' => [ 'things' => [ 'shape' => 'ThingNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListThingPrincipalsRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], ], ], 'ListThingPrincipalsResponse' => [ 'type' => 'structure', 'members' => [ 'principals' => [ 'shape' => 'Principals', ], ], ], 'ListThingsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'attributeName' => [ 'shape' => 'AttributeName', 'location' => 'querystring', 'locationName' => 'attributeName', ], 'attributeValue' => [ 'shape' => 'AttributeValue', 'location' => 'querystring', 'locationName' => 'attributeValue', ], ], ], 'ListThingsResponse' => [ 'type' => 'structure', 'members' => [ 'things' => [ 'shape' => 'ThingAttributeList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTopicRulesRequest' => [ 'type' => 'structure', 'members' => [ 'topic' => [ 'shape' => 'Topic', 'location' => 'querystring', 'locationName' => 'topic', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'ruleDisabled' => [ 'shape' => 'IsDisabled', 'location' => 'querystring', 'locationName' => 'ruleDisabled', ], ], ], 'ListTopicRulesResponse' => [ 'type' => 'structure', 'members' => [ 'rules' => [ 'shape' => 'TopicRuleList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'LogLevel' => [ 'type' => 'string', 'enum' => [ 'DEBUG', 'INFO', 'ERROR', 'WARN', 'DISABLED', ], ], 'LoggingOptionsPayload' => [ 'type' => 'structure', 'required' => [ 'roleArn', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'logLevel' => [ 'shape' => 'LogLevel', ], ], ], 'MalformedPolicyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Marker' => [ 'type' => 'string', ], 'MaxResults' => [ 'type' => 'integer', 'min' => 1, 'max' => 10000, ], 'Message' => [ 'type' => 'string', 'max' => 128, ], 'MessageFormat' => [ 'type' => 'string', 'enum' => [ 'RAW', 'JSON', ], ], 'MetricName' => [ 'type' => 'string', ], 'MetricNamespace' => [ 'type' => 'string', ], 'MetricTimestamp' => [ 'type' => 'string', ], 'MetricUnit' => [ 'type' => 'string', ], 'MetricValue' => [ 'type' => 'string', ], 'NextToken' => [ 'type' => 'string', ], 'PageSize' => [ 'type' => 'integer', 'min' => 1, 'max' => 250, ], 'PartitionKey' => [ 'type' => 'string', ], 'PayloadField' => [ 'type' => 'string', ], 'Policies' => [ 'type' => 'list', 'member' => [ 'shape' => 'Policy', ], ], 'Policy' => [ 'type' => 'structure', 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', ], 'policyArn' => [ 'shape' => 'PolicyArn', ], ], ], 'PolicyArn' => [ 'type' => 'string', ], 'PolicyDocument' => [ 'type' => 'string', ], 'PolicyName' => [ 'type' => 'string', 'min' => 1, 'max' => 128, 'pattern' => '[\\w+=,.@-]+', ], 'PolicyVersion' => [ 'type' => 'structure', 'members' => [ 'versionId' => [ 'shape' => 'PolicyVersionId', ], 'isDefaultVersion' => [ 'shape' => 'IsDefaultVersion', ], 'createDate' => [ 'shape' => 'DateType', ], ], ], 'PolicyVersionId' => [ 'type' => 'string', 'pattern' => '[0-9]+', ], 'PolicyVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'PolicyVersion', ], ], 'Principal' => [ 'type' => 'string', ], 'PrincipalArn' => [ 'type' => 'string', ], 'Principals' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalArn', ], ], 'PrivateKey' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'PublicKey' => [ 'type' => 'string', 'min' => 1, ], 'QueueUrl' => [ 'type' => 'string', ], 'RangeKeyField' => [ 'type' => 'string', ], 'RangeKeyValue' => [ 'type' => 'string', ], 'RegisterCACertificateRequest' => [ 'type' => 'structure', 'required' => [ 'caCertificate', 'verificationCertificate', ], 'members' => [ 'caCertificate' => [ 'shape' => 'CertificatePem', ], 'verificationCertificate' => [ 'shape' => 'CertificatePem', ], 'setAsActive' => [ 'shape' => 'SetAsActive', 'location' => 'querystring', 'locationName' => 'setAsActive', ], ], ], 'RegisterCACertificateResponse' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], ], ], 'RegisterCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificatePem', ], 'members' => [ 'certificatePem' => [ 'shape' => 'CertificatePem', ], 'caCertificatePem' => [ 'shape' => 'CertificatePem', ], 'setAsActive' => [ 'shape' => 'SetAsActive', 'location' => 'querystring', 'locationName' => 'setAsActive', ], ], ], 'RegisterCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'certificateArn' => [ 'shape' => 'CertificateArn', ], 'certificateId' => [ 'shape' => 'CertificateId', ], ], ], 'RegistrationCode' => [ 'type' => 'string', 'min' => 64, 'max' => 64, 'pattern' => '(0x)?[a-fA-F0-9]+', ], 'RegistrationCodeValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'RejectCertificateTransferRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], 'rejectReason' => [ 'shape' => 'Message', ], ], ], 'ReplaceTopicRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleName', 'topicRulePayload', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', 'location' => 'uri', 'locationName' => 'ruleName', ], 'topicRulePayload' => [ 'shape' => 'TopicRulePayload', ], ], 'payload' => 'topicRulePayload', ], 'RepublishAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'topic', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'topic' => [ 'shape' => 'TopicPattern', ], ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'RuleArn' => [ 'type' => 'string', ], 'RuleName' => [ 'type' => 'string', 'min' => 1, 'max' => 128, 'pattern' => '^[a-zA-Z0-9_]+$', ], 'S3Action' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'bucketName', 'key', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'bucketName' => [ 'shape' => 'BucketName', ], 'key' => [ 'shape' => 'Key', ], ], ], 'SQL' => [ 'type' => 'string', ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SetAsActive' => [ 'type' => 'boolean', ], 'SetAsDefault' => [ 'type' => 'boolean', ], 'SetDefaultPolicyVersionRequest' => [ 'type' => 'structure', 'required' => [ 'policyName', 'policyVersionId', ], 'members' => [ 'policyName' => [ 'shape' => 'PolicyName', 'location' => 'uri', 'locationName' => 'policyName', ], 'policyVersionId' => [ 'shape' => 'PolicyVersionId', 'location' => 'uri', 'locationName' => 'policyVersionId', ], ], ], 'SetLoggingOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'loggingOptionsPayload', ], 'members' => [ 'loggingOptionsPayload' => [ 'shape' => 'LoggingOptionsPayload', ], ], 'payload' => 'loggingOptionsPayload', ], 'SnsAction' => [ 'type' => 'structure', 'required' => [ 'targetArn', 'roleArn', ], 'members' => [ 'targetArn' => [ 'shape' => 'AwsArn', ], 'roleArn' => [ 'shape' => 'AwsArn', ], 'messageFormat' => [ 'shape' => 'MessageFormat', ], ], ], 'SqlParseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'SqsAction' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'queueUrl', ], 'members' => [ 'roleArn' => [ 'shape' => 'AwsArn', ], 'queueUrl' => [ 'shape' => 'QueueUrl', ], 'useBase64' => [ 'shape' => 'UseBase64', ], ], ], 'StateReason' => [ 'type' => 'string', ], 'StateValue' => [ 'type' => 'string', ], 'StreamName' => [ 'type' => 'string', ], 'TableName' => [ 'type' => 'string', ], 'ThingArn' => [ 'type' => 'string', ], 'ThingAttribute' => [ 'type' => 'structure', 'members' => [ 'thingName' => [ 'shape' => 'ThingName', ], 'attributes' => [ 'shape' => 'Attributes', ], ], ], 'ThingAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingAttribute', ], ], 'ThingName' => [ 'type' => 'string', 'min' => 1, 'max' => 128, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ThingNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThingName', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Topic' => [ 'type' => 'string', ], 'TopicPattern' => [ 'type' => 'string', ], 'TopicRule' => [ 'type' => 'structure', 'members' => [ 'ruleName' => [ 'shape' => 'RuleName', ], 'sql' => [ 'shape' => 'SQL', ], 'description' => [ 'shape' => 'Description', ], 'createdAt' => [ 'shape' => 'CreatedAtDate', ], 'actions' => [ 'shape' => 'ActionList', ], 'ruleDisabled' => [ 'shape' => 'IsDisabled', ], 'awsIotSqlVersion' => [ 'shape' => 'AwsIotSqlVersion', ], ], ], 'TopicRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TopicRuleListItem', ], ], 'TopicRuleListItem' => [ 'type' => 'structure', 'members' => [ 'ruleArn' => [ 'shape' => 'RuleArn', ], 'ruleName' => [ 'shape' => 'RuleName', ], 'topicPattern' => [ 'shape' => 'TopicPattern', ], 'createdAt' => [ 'shape' => 'CreatedAtDate', ], 'ruleDisabled' => [ 'shape' => 'IsDisabled', ], ], ], 'TopicRulePayload' => [ 'type' => 'structure', 'required' => [ 'sql', 'actions', ], 'members' => [ 'sql' => [ 'shape' => 'SQL', ], 'description' => [ 'shape' => 'Description', ], 'actions' => [ 'shape' => 'ActionList', ], 'ruleDisabled' => [ 'shape' => 'IsDisabled', ], 'awsIotSqlVersion' => [ 'shape' => 'AwsIotSqlVersion', ], ], ], 'TransferAlreadyCompletedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'TransferCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', 'targetAwsAccount', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], 'targetAwsAccount' => [ 'shape' => 'AwsAccountId', 'location' => 'querystring', 'locationName' => 'targetAwsAccount', ], 'transferMessage' => [ 'shape' => 'Message', ], ], ], 'TransferCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'transferredCertificateArn' => [ 'shape' => 'CertificateArn', ], ], ], 'TransferConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'TransferData' => [ 'type' => 'structure', 'members' => [ 'transferMessage' => [ 'shape' => 'Message', ], 'rejectReason' => [ 'shape' => 'Message', ], 'transferDate' => [ 'shape' => 'DateType', ], 'acceptDate' => [ 'shape' => 'DateType', ], 'rejectDate' => [ 'shape' => 'DateType', ], ], ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'UpdateCACertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', 'newStatus', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], 'newStatus' => [ 'shape' => 'CACertificateStatus', 'location' => 'querystring', 'locationName' => 'newStatus', ], ], ], 'UpdateCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'certificateId', 'newStatus', ], 'members' => [ 'certificateId' => [ 'shape' => 'CertificateId', 'location' => 'uri', 'locationName' => 'certificateId', ], 'newStatus' => [ 'shape' => 'CertificateStatus', 'location' => 'querystring', 'locationName' => 'newStatus', ], ], ], 'UpdateThingRequest' => [ 'type' => 'structure', 'required' => [ 'thingName', 'attributePayload', ], 'members' => [ 'thingName' => [ 'shape' => 'ThingName', 'location' => 'uri', 'locationName' => 'thingName', ], 'attributePayload' => [ 'shape' => 'AttributePayload', ], ], ], 'UpdateThingResponse' => [ 'type' => 'structure', 'members' => [], ], 'UseBase64' => [ 'type' => 'boolean', ], 'VersionsLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'errorMessage' => [ 'type' => 'string', ], ],];
