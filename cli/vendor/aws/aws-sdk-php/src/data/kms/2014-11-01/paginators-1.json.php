<?php
// This file was auto-generated from sdk-root/src/data/kms/2014-11-01/paginators-1.json
return [ 'pagination' => [ 'ListAliases' => [ 'limit_key' => 'Limit', 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'more_results' => 'Truncated', 'result_key' => 'Aliases', ], 'ListGrants' => [ 'limit_key' => 'Limit', 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'more_results' => 'Truncated', 'result_key' => 'Grants', ], 'ListKeyPolicies' => [ 'limit_key' => 'Limit', 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'more_results' => 'Truncated', 'result_key' => 'PolicyNames', ], 'ListKeys' => [ 'limit_key' => 'Limit', 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'more_results' => 'Truncated', 'result_key' => 'Keys', ], ],];
