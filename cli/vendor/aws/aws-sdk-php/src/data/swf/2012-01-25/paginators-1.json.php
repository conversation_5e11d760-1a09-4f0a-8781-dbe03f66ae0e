<?php
// This file was auto-generated from sdk-root/src/data/swf/2012-01-25/paginators-1.json
return [ 'pagination' => [ 'GetWorkflowExecutionHistory' => [ 'limit_key' => 'maximumPageSize', 'input_token' => 'nextPageToken', 'output_token' => 'nextPageToken', 'result_key' => 'events', ], 'ListActivityTypes' => [ 'limit_key' => 'maximumPageSize', 'input_token' => 'nextPageToken', 'output_token' => 'nextPageToken', 'result_key' => 'typeInfos', ], 'ListClosedWorkflowExecutions' => [ 'limit_key' => 'maximumPageSize', 'input_token' => 'nextPageToken', 'output_token' => 'nextPageToken', 'result_key' => 'executionInfos', ], 'ListDomains' => [ 'limit_key' => 'maximumPageSize', 'input_token' => 'nextPageToken', 'output_token' => 'nextPageToken', 'result_key' => 'domainInfos', ], 'ListOpenWorkflowExecutions' => [ 'limit_key' => 'maximumPageSize', 'input_token' => 'nextPageToken', 'output_token' => 'nextPageToken', 'result_key' => 'executionInfos', ], 'ListWorkflowTypes' => [ 'limit_key' => 'maximumPageSize', 'input_token' => 'nextPageToken', 'output_token' => 'nextPageToken', 'result_key' => 'typeInfos', ], 'PollForDecisionTask' => [ 'limit_key' => 'maximumPageSize', 'input_token' => 'nextPageToken', 'output_token' => 'nextPageToken', 'result_key' => 'events', ], ],];
