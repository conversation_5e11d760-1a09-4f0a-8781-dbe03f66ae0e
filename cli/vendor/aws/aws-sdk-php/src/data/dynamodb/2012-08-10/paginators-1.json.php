<?php
// This file was auto-generated from sdk-root/src/data/dynamodb/2012-08-10/paginators-1.json
return [ 'pagination' => [ 'BatchGetItem' => [ 'input_token' => 'RequestItems', 'output_token' => 'UnprocessedKeys', ], 'ListTables' => [ 'input_token' => 'ExclusiveStartTableName', 'output_token' => 'LastEvaluatedTableName', 'limit_key' => 'Limit', 'result_key' => 'TableNames', ], 'Query' => [ 'input_token' => 'ExclusiveStartKey', 'output_token' => 'LastEvaluatedKey', 'limit_key' => 'Limit', 'result_key' => 'Items', ], 'Scan' => [ 'input_token' => 'ExclusiveStartKey', 'output_token' => 'LastEvaluatedKey', 'limit_key' => 'Limit', 'result_key' => 'Items', ], ],];
