<?php
// This file was auto-generated from sdk-root/src/data/metering.marketplace/2016-01-14/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-01-14', 'endpointPrefix' => 'metering.marketplace', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'AWSMarketplace Metering', 'signatureVersion' => 'v4', 'signingName' => 'aws-marketplace', 'targetPrefix' => 'AWSMPMeteringService', ], 'operations' => [ 'MeterUsage' => [ 'name' => 'MeterUsage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MeterUsageRequest', ], 'output' => [ 'shape' => 'MeterUsageResult', ], 'errors' => [ [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidProductCodeException', ], [ 'shape' => 'InvalidUsageDimensionException', ], [ 'shape' => 'InvalidEndpointRegionException', ], [ 'shape' => 'TimestampOutOfBoundsException', ], [ 'shape' => 'DuplicateRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'Boolean' => [ 'type' => 'boolean', ], 'DuplicateRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'InternalServiceErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, 'fault' => true, ], 'InvalidEndpointRegionException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'InvalidProductCodeException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'InvalidUsageDimensionException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'MeterUsageRequest' => [ 'type' => 'structure', 'required' => [ 'ProductCode', 'Timestamp', 'UsageDimension', 'UsageQuantity', 'DryRun', ], 'members' => [ 'ProductCode' => [ 'shape' => 'ProductCode', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'UsageDimension' => [ 'shape' => 'UsageDimension', ], 'UsageQuantity' => [ 'shape' => 'UsageQuantity', ], 'DryRun' => [ 'shape' => 'Boolean', ], ], ], 'MeterUsageResult' => [ 'type' => 'structure', 'members' => [ 'MeteringRecordId' => [ 'shape' => 'String', ], ], ], 'ProductCode' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampOutOfBoundsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'exception' => true, ], 'UsageDimension' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'UsageQuantity' => [ 'type' => 'integer', 'max' => 10000, 'min' => 0, ], 'errorMessage' => [ 'type' => 'string', ], ],];
