<?php
// This file was auto-generated from sdk-root/src/data/ds/2015-04-16/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-04-16', 'endpointPrefix' => 'ds', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'Directory Service', 'serviceFullName' => 'AWS Directory Service', 'signatureVersion' => 'v4', 'targetPrefix' => 'DirectoryService_20150416', ], 'operations' => [ 'ConnectDirectory' => [ 'name' => 'ConnectDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ConnectDirectoryRequest', ], 'output' => [ 'shape' => 'ConnectDirectoryResult', ], 'errors' => [ [ 'shape' => 'DirectoryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateAlias' => [ 'name' => 'CreateAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAliasRequest', ], 'output' => [ 'shape' => 'CreateAliasResult', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateComputer' => [ 'name' => 'CreateComputer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateComputerRequest', ], 'output' => [ 'shape' => 'CreateComputerResult', ], 'errors' => [ [ 'shape' => 'AuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateConditionalForwarder' => [ 'name' => 'CreateConditionalForwarder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConditionalForwarderRequest', ], 'output' => [ 'shape' => 'CreateConditionalForwarderResult', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateDirectory' => [ 'name' => 'CreateDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDirectoryRequest', ], 'output' => [ 'shape' => 'CreateDirectoryResult', ], 'errors' => [ [ 'shape' => 'DirectoryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateMicrosoftAD' => [ 'name' => 'CreateMicrosoftAD', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMicrosoftADRequest', ], 'output' => [ 'shape' => 'CreateMicrosoftADResult', ], 'errors' => [ [ 'shape' => 'DirectoryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'CreateSnapshot' => [ 'name' => 'CreateSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSnapshotRequest', ], 'output' => [ 'shape' => 'CreateSnapshotResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'SnapshotLimitExceededException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateTrust' => [ 'name' => 'CreateTrust', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTrustRequest', ], 'output' => [ 'shape' => 'CreateTrustResult', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DeleteConditionalForwarder' => [ 'name' => 'DeleteConditionalForwarder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConditionalForwarderRequest', ], 'output' => [ 'shape' => 'DeleteConditionalForwarderResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeleteDirectory' => [ 'name' => 'DeleteDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDirectoryRequest', ], 'output' => [ 'shape' => 'DeleteDirectoryResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeleteSnapshot' => [ 'name' => 'DeleteSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteSnapshotResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeleteTrust' => [ 'name' => 'DeleteTrust', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTrustRequest', ], 'output' => [ 'shape' => 'DeleteTrustResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DeregisterEventTopic' => [ 'name' => 'DeregisterEventTopic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterEventTopicRequest', ], 'output' => [ 'shape' => 'DeregisterEventTopicResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeConditionalForwarders' => [ 'name' => 'DescribeConditionalForwarders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConditionalForwardersRequest', ], 'output' => [ 'shape' => 'DescribeConditionalForwardersResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeDirectories' => [ 'name' => 'DescribeDirectories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDirectoriesRequest', ], 'output' => [ 'shape' => 'DescribeDirectoriesResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeEventTopics' => [ 'name' => 'DescribeEventTopics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventTopicsRequest', ], 'output' => [ 'shape' => 'DescribeEventTopicsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeSnapshots' => [ 'name' => 'DescribeSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSnapshotsRequest', ], 'output' => [ 'shape' => 'DescribeSnapshotsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeTrusts' => [ 'name' => 'DescribeTrusts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrustsRequest', ], 'output' => [ 'shape' => 'DescribeTrustsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DisableRadius' => [ 'name' => 'DisableRadius', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableRadiusRequest', ], 'output' => [ 'shape' => 'DisableRadiusResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DisableSso' => [ 'name' => 'DisableSso', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableSsoRequest', ], 'output' => [ 'shape' => 'DisableSsoResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'AuthenticationFailedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'EnableRadius' => [ 'name' => 'EnableRadius', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableRadiusRequest', ], 'output' => [ 'shape' => 'EnableRadiusResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'EnableSso' => [ 'name' => 'EnableSso', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableSsoRequest', ], 'output' => [ 'shape' => 'EnableSsoResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'AuthenticationFailedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'GetDirectoryLimits' => [ 'name' => 'GetDirectoryLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDirectoryLimitsRequest', ], 'output' => [ 'shape' => 'GetDirectoryLimitsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'GetSnapshotLimits' => [ 'name' => 'GetSnapshotLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSnapshotLimitsRequest', ], 'output' => [ 'shape' => 'GetSnapshotLimitsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RegisterEventTopic' => [ 'name' => 'RegisterEventTopic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterEventTopicRequest', ], 'output' => [ 'shape' => 'RegisterEventTopicResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RestoreFromSnapshot' => [ 'name' => 'RestoreFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreFromSnapshotRequest', ], 'output' => [ 'shape' => 'RestoreFromSnapshotResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateConditionalForwarder' => [ 'name' => 'UpdateConditionalForwarder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConditionalForwarderRequest', ], 'output' => [ 'shape' => 'UpdateConditionalForwarderResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateRadius' => [ 'name' => 'UpdateRadius', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRadiusRequest', ], 'output' => [ 'shape' => 'UpdateRadiusResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'VerifyTrust' => [ 'name' => 'VerifyTrust', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'VerifyTrustRequest', ], 'output' => [ 'shape' => 'VerifyTrustResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], ], 'shapes' => [ 'AccessUrl' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'AliasName' => [ 'type' => 'string', 'max' => 62, 'min' => 1, 'pattern' => '^(?!d-)([\\da-zA-Z]+)([-]*[\\da-zA-Z])*', ], 'Attribute' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AttributeName', ], 'Value' => [ 'shape' => 'AttributeValue', ], ], ], 'AttributeName' => [ 'type' => 'string', 'min' => 1, ], 'AttributeValue' => [ 'type' => 'string', ], 'Attributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], ], 'AuthenticationFailedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'AvailabilityZone' => [ 'type' => 'string', ], 'AvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], ], 'ClientException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'CloudOnlyDirectoriesLimitReached' => [ 'type' => 'boolean', ], 'Computer' => [ 'type' => 'structure', 'members' => [ 'ComputerId' => [ 'shape' => 'SID', ], 'ComputerName' => [ 'shape' => 'ComputerName', ], 'ComputerAttributes' => [ 'shape' => 'Attributes', ], ], ], 'ComputerName' => [ 'type' => 'string', 'max' => 15, 'min' => 1, ], 'ComputerPassword' => [ 'type' => 'string', 'max' => 64, 'min' => 8, 'pattern' => '[\\u0020-\\u00FF]+', 'sensitive' => true, ], 'ConditionalForwarder' => [ 'type' => 'structure', 'members' => [ 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], 'ReplicationScope' => [ 'shape' => 'ReplicationScope', ], ], ], 'ConditionalForwarders' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConditionalForwarder', ], ], 'ConnectDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Password', 'Size', 'ConnectSettings', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Password' => [ 'shape' => 'ConnectPassword', ], 'Description' => [ 'shape' => 'Description', ], 'Size' => [ 'shape' => 'DirectorySize', ], 'ConnectSettings' => [ 'shape' => 'DirectoryConnectSettings', ], ], ], 'ConnectDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'ConnectPassword' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'ConnectedDirectoriesLimitReached' => [ 'type' => 'boolean', ], 'CreateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'Alias', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Alias' => [ 'shape' => 'AliasName', ], ], ], 'CreateAliasResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Alias' => [ 'shape' => 'AliasName', ], ], ], 'CreateComputerRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'ComputerName', 'Password', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'ComputerName' => [ 'shape' => 'ComputerName', ], 'Password' => [ 'shape' => 'ComputerPassword', ], 'OrganizationalUnitDistinguishedName' => [ 'shape' => 'OrganizationalUnitDN', ], 'ComputerAttributes' => [ 'shape' => 'Attributes', ], ], ], 'CreateComputerResult' => [ 'type' => 'structure', 'members' => [ 'Computer' => [ 'shape' => 'Computer', ], ], ], 'CreateConditionalForwarderRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', 'DnsIpAddrs', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], ], ], 'CreateConditionalForwarderResult' => [ 'type' => 'structure', 'members' => [], ], 'CreateDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Password', 'Size', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Password' => [ 'shape' => 'Password', ], 'Description' => [ 'shape' => 'Description', ], 'Size' => [ 'shape' => 'DirectorySize', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettings', ], ], ], 'CreateDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'CreateMicrosoftADRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Password', 'VpcSettings', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Password' => [ 'shape' => 'Password', ], 'Description' => [ 'shape' => 'Description', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettings', ], ], ], 'CreateMicrosoftADResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'CreateSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Name' => [ 'shape' => 'SnapshotName', ], ], ], 'CreateSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'CreateTrustRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', 'TrustPassword', 'TrustDirection', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'TrustPassword' => [ 'shape' => 'TrustPassword', ], 'TrustDirection' => [ 'shape' => 'TrustDirection', ], 'TrustType' => [ 'shape' => 'TrustType', ], 'ConditionalForwarderIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], ], ], 'CreateTrustResult' => [ 'type' => 'structure', 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'CreatedDateTime' => [ 'type' => 'timestamp', ], 'DeleteAssociatedConditionalForwarder' => [ 'type' => 'boolean', ], 'DeleteConditionalForwarderRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], ], ], 'DeleteConditionalForwarderResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DeleteDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DeleteSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'SnapshotId', ], 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'DeleteSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'DeleteTrustRequest' => [ 'type' => 'structure', 'required' => [ 'TrustId', ], 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], 'DeleteAssociatedConditionalForwarder' => [ 'shape' => 'DeleteAssociatedConditionalForwarder', ], ], ], 'DeleteTrustResult' => [ 'type' => 'structure', 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'DeregisterEventTopicRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'TopicName', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicName' => [ 'shape' => 'TopicName', ], ], ], 'DeregisterEventTopicResult' => [ 'type' => 'structure', 'members' => [], ], 'DescribeConditionalForwardersRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainNames' => [ 'shape' => 'RemoteDomainNames', ], ], ], 'DescribeConditionalForwardersResult' => [ 'type' => 'structure', 'members' => [ 'ConditionalForwarders' => [ 'shape' => 'ConditionalForwarders', ], ], ], 'DescribeDirectoriesRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryIds' => [ 'shape' => 'DirectoryIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeDirectoriesResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryDescriptions' => [ 'shape' => 'DirectoryDescriptions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeEventTopicsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicNames' => [ 'shape' => 'TopicNames', ], ], ], 'DescribeEventTopicsResult' => [ 'type' => 'structure', 'members' => [ 'EventTopics' => [ 'shape' => 'EventTopics', ], ], ], 'DescribeSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SnapshotIds' => [ 'shape' => 'SnapshotIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeSnapshotsResult' => [ 'type' => 'structure', 'members' => [ 'Snapshots' => [ 'shape' => 'Snapshots', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeTrustsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TrustIds' => [ 'shape' => 'TrustIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeTrustsResult' => [ 'type' => 'structure', 'members' => [ 'Trusts' => [ 'shape' => 'Trusts', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^([a-zA-Z0-9_])[\\\\a-zA-Z0-9_@#%*+=:?./!\\s-]*$', ], 'DirectoryConnectSettings' => [ 'type' => 'structure', 'required' => [ 'VpcId', 'SubnetIds', 'CustomerDnsIps', 'CustomerUserName', ], 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'CustomerDnsIps' => [ 'shape' => 'DnsIpAddrs', ], 'CustomerUserName' => [ 'shape' => 'UserName', ], ], ], 'DirectoryConnectSettingsDescription' => [ 'type' => 'structure', 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'CustomerUserName' => [ 'shape' => 'UserName', ], 'SecurityGroupId' => [ 'shape' => 'SecurityGroupId', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'ConnectIps' => [ 'shape' => 'IpAddrs', ], ], ], 'DirectoryDescription' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Size' => [ 'shape' => 'DirectorySize', ], 'Alias' => [ 'shape' => 'AliasName', ], 'AccessUrl' => [ 'shape' => 'AccessUrl', ], 'Description' => [ 'shape' => 'Description', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], 'Stage' => [ 'shape' => 'DirectoryStage', ], 'LaunchTime' => [ 'shape' => 'LaunchTime', ], 'StageLastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], 'Type' => [ 'shape' => 'DirectoryType', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettingsDescription', ], 'ConnectSettings' => [ 'shape' => 'DirectoryConnectSettingsDescription', ], 'RadiusSettings' => [ 'shape' => 'RadiusSettings', ], 'RadiusStatus' => [ 'shape' => 'RadiusStatus', ], 'StageReason' => [ 'shape' => 'StageReason', ], 'SsoEnabled' => [ 'shape' => 'SsoEnabled', ], ], ], 'DirectoryDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DirectoryDescription', ], ], 'DirectoryId' => [ 'type' => 'string', 'pattern' => '^d-[0-9a-f]{10}$', ], 'DirectoryIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DirectoryId', ], ], 'DirectoryLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryLimits' => [ 'type' => 'structure', 'members' => [ 'CloudOnlyDirectoriesLimit' => [ 'shape' => 'Limit', ], 'CloudOnlyDirectoriesCurrentCount' => [ 'shape' => 'Limit', ], 'CloudOnlyDirectoriesLimitReached' => [ 'shape' => 'CloudOnlyDirectoriesLimitReached', ], 'CloudOnlyMicrosoftADLimit' => [ 'shape' => 'Limit', ], 'CloudOnlyMicrosoftADCurrentCount' => [ 'shape' => 'Limit', ], 'CloudOnlyMicrosoftADLimitReached' => [ 'shape' => 'CloudOnlyDirectoriesLimitReached', ], 'ConnectedDirectoriesLimit' => [ 'shape' => 'Limit', ], 'ConnectedDirectoriesCurrentCount' => [ 'shape' => 'Limit', ], 'ConnectedDirectoriesLimitReached' => [ 'shape' => 'ConnectedDirectoriesLimitReached', ], ], ], 'DirectoryName' => [ 'type' => 'string', 'pattern' => '^([a-zA-Z0-9]+[\\\\.-])+([a-zA-Z0-9])+$', ], 'DirectoryShortName' => [ 'type' => 'string', 'pattern' => '^[^\\\\/:*?\\"\\<\\>|.]+[^\\\\/:*?\\"<>|]*$', ], 'DirectorySize' => [ 'type' => 'string', 'enum' => [ 'Small', 'Large', ], ], 'DirectoryStage' => [ 'type' => 'string', 'enum' => [ 'Requested', 'Creating', 'Created', 'Active', 'Inoperable', 'Impaired', 'Restoring', 'RestoreFailed', 'Deleting', 'Deleted', 'Failed', ], ], 'DirectoryType' => [ 'type' => 'string', 'enum' => [ 'SimpleAD', 'ADConnector', 'MicrosoftAD', ], ], 'DirectoryUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryVpcSettings' => [ 'type' => 'structure', 'required' => [ 'VpcId', 'SubnetIds', ], 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], ], ], 'DirectoryVpcSettingsDescription' => [ 'type' => 'structure', 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'SecurityGroupId' => [ 'shape' => 'SecurityGroupId', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], ], ], 'DisableRadiusRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DisableRadiusResult' => [ 'type' => 'structure', 'members' => [], ], 'DisableSsoRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'UserName', ], 'Password' => [ 'shape' => 'ConnectPassword', ], ], ], 'DisableSsoResult' => [ 'type' => 'structure', 'members' => [], ], 'DnsIpAddrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddr', ], ], 'EnableRadiusRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RadiusSettings', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RadiusSettings' => [ 'shape' => 'RadiusSettings', ], ], ], 'EnableRadiusResult' => [ 'type' => 'structure', 'members' => [], ], 'EnableSsoRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'UserName', ], 'Password' => [ 'shape' => 'ConnectPassword', ], ], ], 'EnableSsoResult' => [ 'type' => 'structure', 'members' => [], ], 'EntityAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'EntityDoesNotExistException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'EventTopic' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicName' => [ 'shape' => 'TopicName', ], 'TopicArn' => [ 'shape' => 'TopicArn', ], 'CreatedDateTime' => [ 'shape' => 'CreatedDateTime', ], 'Status' => [ 'shape' => 'TopicStatus', ], ], ], 'EventTopics' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventTopic', ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'GetDirectoryLimitsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDirectoryLimitsResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryLimits' => [ 'shape' => 'DirectoryLimits', ], ], ], 'GetSnapshotLimitsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'GetSnapshotLimitsResult' => [ 'type' => 'structure', 'members' => [ 'SnapshotLimits' => [ 'shape' => 'SnapshotLimits', ], ], ], 'InsufficientPermissionsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'IpAddr' => [ 'type' => 'string', 'pattern' => '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$', ], 'IpAddrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddr', ], ], 'LastUpdatedDateTime' => [ 'type' => 'timestamp', ], 'LaunchTime' => [ 'type' => 'timestamp', ], 'Limit' => [ 'type' => 'integer', 'min' => 0, ], 'ManualSnapshotsLimitReached' => [ 'type' => 'boolean', ], 'NextToken' => [ 'type' => 'string', ], 'OrganizationalUnitDN' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'Password' => [ 'type' => 'string', 'pattern' => '(?=^.{8,64}$)((?=.*\\d)(?=.*[A-Z])(?=.*[a-z])|(?=.*\\d)(?=.*[^A-Za-z0-9\\s])(?=.*[a-z])|(?=.*[^A-Za-z0-9\\s])(?=.*[A-Z])(?=.*[a-z])|(?=.*\\d)(?=.*[A-Z])(?=.*[^A-Za-z0-9\\s]))^.*', 'sensitive' => true, ], 'PortNumber' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1025, ], 'RadiusAuthenticationProtocol' => [ 'type' => 'string', 'enum' => [ 'PAP', 'CHAP', 'MS-CHAPv1', 'MS-CHAPv2', ], ], 'RadiusDisplayLabel' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'RadiusRetries' => [ 'type' => 'integer', 'max' => 10, 'min' => 0, ], 'RadiusSettings' => [ 'type' => 'structure', 'members' => [ 'RadiusServers' => [ 'shape' => 'Servers', ], 'RadiusPort' => [ 'shape' => 'PortNumber', ], 'RadiusTimeout' => [ 'shape' => 'RadiusTimeout', ], 'RadiusRetries' => [ 'shape' => 'RadiusRetries', ], 'SharedSecret' => [ 'shape' => 'RadiusSharedSecret', ], 'AuthenticationProtocol' => [ 'shape' => 'RadiusAuthenticationProtocol', ], 'DisplayLabel' => [ 'shape' => 'RadiusDisplayLabel', ], 'UseSameUsername' => [ 'shape' => 'UseSameUsername', ], ], ], 'RadiusSharedSecret' => [ 'type' => 'string', 'max' => 512, 'min' => 8, 'sensitive' => true, ], 'RadiusStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Completed', 'Failed', ], ], 'RadiusTimeout' => [ 'type' => 'integer', 'max' => 20, 'min' => 1, ], 'RegisterEventTopicRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'TopicName', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicName' => [ 'shape' => 'TopicName', ], ], ], 'RegisterEventTopicResult' => [ 'type' => 'structure', 'members' => [], ], 'RemoteDomainName' => [ 'type' => 'string', 'pattern' => '^([a-zA-Z0-9]+[\\\\.-])+([a-zA-Z0-9])+[.]?$', ], 'RemoteDomainNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemoteDomainName', ], ], 'ReplicationScope' => [ 'type' => 'string', 'enum' => [ 'Domain', ], ], 'RequestId' => [ 'type' => 'string', 'pattern' => '^([A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12})$', ], 'RestoreFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'SnapshotId', ], 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'RestoreFromSnapshotResult' => [ 'type' => 'structure', 'members' => [], ], 'SID' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[&\\w+-.@]+', ], 'SecurityGroupId' => [ 'type' => 'string', 'pattern' => '^(sg-[0-9a-f]{8})$', ], 'Server' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Servers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Server', ], ], 'ServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, 'fault' => true, ], 'Snapshot' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'Type' => [ 'shape' => 'SnapshotType', ], 'Name' => [ 'shape' => 'SnapshotName', ], 'Status' => [ 'shape' => 'SnapshotStatus', ], 'StartTime' => [ 'shape' => 'StartTime', ], ], ], 'SnapshotId' => [ 'type' => 'string', 'pattern' => '^s-[0-9a-f]{10}$', ], 'SnapshotIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SnapshotId', ], ], 'SnapshotLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'SnapshotLimits' => [ 'type' => 'structure', 'members' => [ 'ManualSnapshotsLimit' => [ 'shape' => 'Limit', ], 'ManualSnapshotsCurrentCount' => [ 'shape' => 'Limit', ], 'ManualSnapshotsLimitReached' => [ 'shape' => 'ManualSnapshotsLimitReached', ], ], ], 'SnapshotName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^([a-zA-Z0-9_])[\\\\a-zA-Z0-9_@#%*+=:?./!\\s-]*$', ], 'SnapshotStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Completed', 'Failed', ], ], 'SnapshotType' => [ 'type' => 'string', 'enum' => [ 'Auto', 'Manual', ], ], 'Snapshots' => [ 'type' => 'list', 'member' => [ 'shape' => 'Snapshot', ], ], 'SsoEnabled' => [ 'type' => 'boolean', ], 'StageReason' => [ 'type' => 'string', ], 'StartTime' => [ 'type' => 'timestamp', ], 'StateLastUpdatedDateTime' => [ 'type' => 'timestamp', ], 'SubnetId' => [ 'type' => 'string', 'pattern' => '^(subnet-[0-9a-f]{8})$', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'TopicArn' => [ 'type' => 'string', ], 'TopicName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'TopicNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'TopicName', ], ], 'TopicStatus' => [ 'type' => 'string', 'enum' => [ 'Registered', 'Topic not found', 'Failed', 'Deleted', ], ], 'Trust' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TrustId' => [ 'shape' => 'TrustId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'TrustType' => [ 'shape' => 'TrustType', ], 'TrustDirection' => [ 'shape' => 'TrustDirection', ], 'TrustState' => [ 'shape' => 'TrustState', ], 'CreatedDateTime' => [ 'shape' => 'CreatedDateTime', ], 'LastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], 'StateLastUpdatedDateTime' => [ 'shape' => 'StateLastUpdatedDateTime', ], 'TrustStateReason' => [ 'shape' => 'TrustStateReason', ], ], ], 'TrustDirection' => [ 'type' => 'string', 'enum' => [ 'One-Way: Outgoing', 'One-Way: Incoming', 'Two-Way', ], ], 'TrustId' => [ 'type' => 'string', 'pattern' => '^t-[0-9a-f]{10}$', ], 'TrustIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustId', ], ], 'TrustPassword' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'TrustState' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Created', 'Verifying', 'VerifyFailed', 'Verified', 'Deleting', 'Deleted', 'Failed', ], ], 'TrustStateReason' => [ 'type' => 'string', ], 'TrustType' => [ 'type' => 'string', 'enum' => [ 'Forest', ], ], 'Trusts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Trust', ], ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'UpdateConditionalForwarderRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', 'DnsIpAddrs', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], ], ], 'UpdateConditionalForwarderResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRadiusRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RadiusSettings', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RadiusSettings' => [ 'shape' => 'RadiusSettings', ], ], ], 'UpdateRadiusResult' => [ 'type' => 'structure', 'members' => [], ], 'UseSameUsername' => [ 'type' => 'boolean', ], 'UserName' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z0-9._-]+', ], 'VerifyTrustRequest' => [ 'type' => 'structure', 'required' => [ 'TrustId', ], 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'VerifyTrustResult' => [ 'type' => 'structure', 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'VpcId' => [ 'type' => 'string', 'pattern' => '^(vpc-[0-9a-f]{8})$', ], ],];
