<?php
// This file was auto-generated from sdk-root/src/data/inspector/2016-02-16/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-02-16', 'endpointPrefix' => 'inspector', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon Inspector', 'signatureVersion' => 'v4', 'targetPrefix' => 'InspectorService', ], 'operations' => [ 'AddAttributesToFindings' => [ 'name' => 'AddAttributesToFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddAttributesToFindingsRequest', ], 'output' => [ 'shape' => 'AddAttributesToFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'CreateAssessmentTarget' => [ 'name' => 'CreateAssessmentTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAssessmentTargetRequest', ], 'output' => [ 'shape' => 'CreateAssessmentTargetResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'CreateAssessmentTemplate' => [ 'name' => 'CreateAssessmentTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAssessmentTemplateRequest', ], 'output' => [ 'shape' => 'CreateAssessmentTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'CreateResourceGroup' => [ 'name' => 'CreateResourceGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateResourceGroupRequest', ], 'output' => [ 'shape' => 'CreateResourceGroupResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteAssessmentRun' => [ 'name' => 'DeleteAssessmentRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAssessmentRunRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AssessmentRunInProgressException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'DeleteAssessmentTarget' => [ 'name' => 'DeleteAssessmentTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAssessmentTargetRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AssessmentRunInProgressException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'DeleteAssessmentTemplate' => [ 'name' => 'DeleteAssessmentTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAssessmentTemplateRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AssessmentRunInProgressException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'DescribeAssessmentRuns' => [ 'name' => 'DescribeAssessmentRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAssessmentRunsRequest', ], 'output' => [ 'shape' => 'DescribeAssessmentRunsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DescribeAssessmentTargets' => [ 'name' => 'DescribeAssessmentTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAssessmentTargetsRequest', ], 'output' => [ 'shape' => 'DescribeAssessmentTargetsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DescribeAssessmentTemplates' => [ 'name' => 'DescribeAssessmentTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAssessmentTemplatesRequest', ], 'output' => [ 'shape' => 'DescribeAssessmentTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DescribeCrossAccountAccessRole' => [ 'name' => 'DescribeCrossAccountAccessRole', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'DescribeCrossAccountAccessRoleResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], ], ], 'DescribeFindings' => [ 'name' => 'DescribeFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFindingsRequest', ], 'output' => [ 'shape' => 'DescribeFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DescribeResourceGroups' => [ 'name' => 'DescribeResourceGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeResourceGroupsRequest', ], 'output' => [ 'shape' => 'DescribeResourceGroupsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DescribeRulesPackages' => [ 'name' => 'DescribeRulesPackages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRulesPackagesRequest', ], 'output' => [ 'shape' => 'DescribeRulesPackagesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetTelemetryMetadata' => [ 'name' => 'GetTelemetryMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTelemetryMetadataRequest', ], 'output' => [ 'shape' => 'GetTelemetryMetadataResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'ListAssessmentRunAgents' => [ 'name' => 'ListAssessmentRunAgents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssessmentRunAgentsRequest', ], 'output' => [ 'shape' => 'ListAssessmentRunAgentsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'ListAssessmentRuns' => [ 'name' => 'ListAssessmentRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssessmentRunsRequest', ], 'output' => [ 'shape' => 'ListAssessmentRunsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'ListAssessmentTargets' => [ 'name' => 'ListAssessmentTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssessmentTargetsRequest', ], 'output' => [ 'shape' => 'ListAssessmentTargetsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAssessmentTemplates' => [ 'name' => 'ListAssessmentTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssessmentTemplatesRequest', ], 'output' => [ 'shape' => 'ListAssessmentTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'ListEventSubscriptions' => [ 'name' => 'ListEventSubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEventSubscriptionsRequest', ], 'output' => [ 'shape' => 'ListEventSubscriptionsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'ListFindings' => [ 'name' => 'ListFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFindingsRequest', ], 'output' => [ 'shape' => 'ListFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'ListRulesPackages' => [ 'name' => 'ListRulesPackages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRulesPackagesRequest', ], 'output' => [ 'shape' => 'ListRulesPackagesResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'PreviewAgents' => [ 'name' => 'PreviewAgents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PreviewAgentsRequest', ], 'output' => [ 'shape' => 'PreviewAgentsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], [ 'shape' => 'InvalidCrossAccountRoleException', ], ], ], 'RegisterCrossAccountAccessRole' => [ 'name' => 'RegisterCrossAccountAccessRole', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterCrossAccountAccessRoleRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidCrossAccountRoleException', ], ], ], 'RemoveAttributesFromFindings' => [ 'name' => 'RemoveAttributesFromFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveAttributesFromFindingsRequest', ], 'output' => [ 'shape' => 'RemoveAttributesFromFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'SetTagsForResource' => [ 'name' => 'SetTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetTagsForResourceRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'StartAssessmentRun' => [ 'name' => 'StartAssessmentRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartAssessmentRunRequest', ], 'output' => [ 'shape' => 'StartAssessmentRunResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], [ 'shape' => 'InvalidCrossAccountRoleException', ], [ 'shape' => 'AgentsAlreadyRunningAssessmentException', ], ], ], 'StopAssessmentRun' => [ 'name' => 'StopAssessmentRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopAssessmentRunRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'SubscribeToEvent' => [ 'name' => 'SubscribeToEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SubscribeToEventRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'UnsubscribeFromEvent' => [ 'name' => 'UnsubscribeFromEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UnsubscribeFromEventRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], 'UpdateAssessmentTarget' => [ 'name' => 'UpdateAssessmentTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAssessmentTargetRequest', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NoSuchEntityException', ], ], ], ], 'shapes' => [ 'AccessDeniedErrorCode' => [ 'type' => 'string', 'enum' => [ 'ACCESS_DENIED_TO_ASSESSMENT_TARGET', 'ACCESS_DENIED_TO_ASSESSMENT_TEMPLATE', 'ACCESS_DENIED_TO_ASSESSMENT_RUN', 'ACCESS_DENIED_TO_FINDING', 'ACCESS_DENIED_TO_RESOURCE_GROUP', 'ACCESS_DENIED_TO_RULES_PACKAGE', 'ACCESS_DENIED_TO_SNS_TOPIC', 'ACCESS_DENIED_TO_IAM_ROLE', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', 'errorCode', 'canRetry', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'errorCode' => [ 'shape' => 'AccessDeniedErrorCode', ], 'canRetry' => [ 'shape' => 'Bool', ], ], 'exception' => true, ], 'AddAttributesToFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'findingArns', 'attributes', ], 'members' => [ 'findingArns' => [ 'shape' => 'AddRemoveAttributesFindingArnList', ], 'attributes' => [ 'shape' => 'UserAttributeList', ], ], ], 'AddAttributesToFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'failedItems', ], 'members' => [ 'failedItems' => [ 'shape' => 'FailedItems', ], ], ], 'AddRemoveAttributesFindingArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 10, 'min' => 1, ], 'AgentAlreadyRunningAssessment' => [ 'type' => 'structure', 'required' => [ 'agentId', 'assessmentRunArn', ], 'members' => [ 'agentId' => [ 'shape' => 'AgentId', ], 'assessmentRunArn' => [ 'shape' => 'Arn', ], ], ], 'AgentAlreadyRunningAssessmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentAlreadyRunningAssessment', ], 'max' => 10, 'min' => 1, ], 'AgentFilter' => [ 'type' => 'structure', 'required' => [ 'agentHealths', 'agentHealthCodes', ], 'members' => [ 'agentHealths' => [ 'shape' => 'AgentHealthList', ], 'agentHealthCodes' => [ 'shape' => 'AgentHealthCodeList', ], ], ], 'AgentHealth' => [ 'type' => 'string', 'enum' => [ 'HEALTHY', 'UNHEALTHY', ], ], 'AgentHealthCode' => [ 'type' => 'string', 'enum' => [ 'IDLE', 'RUNNING', 'SHUTDOWN', 'UNHEALTHY', 'THROTTLED', 'UNKNOWN', ], ], 'AgentHealthCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentHealthCode', ], 'max' => 10, 'min' => 0, ], 'AgentHealthList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentHealth', ], 'max' => 10, 'min' => 0, ], 'AgentId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'AgentIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentId', ], 'max' => 500, 'min' => 0, ], 'AgentPreview' => [ 'type' => 'structure', 'required' => [ 'agentId', ], 'members' => [ 'agentId' => [ 'shape' => 'AgentId', ], 'autoScalingGroup' => [ 'shape' => 'AutoScalingGroup', ], ], ], 'AgentPreviewList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AgentPreview', ], 'max' => 100, 'min' => 0, ], 'AgentsAlreadyRunningAssessmentException' => [ 'type' => 'structure', 'required' => [ 'message', 'agents', 'agentsTruncated', 'canRetry', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'agents' => [ 'shape' => 'AgentAlreadyRunningAssessmentList', ], 'agentsTruncated' => [ 'shape' => 'Bool', ], 'canRetry' => [ 'shape' => 'Bool', ], ], 'exception' => true, ], 'AmiId' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Arn' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'AssessmentRulesPackageArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 50, 'min' => 1, ], 'AssessmentRun' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'assessmentTemplateArn', 'state', 'durationInSeconds', 'rulesPackageArns', 'userAttributesForFindings', 'createdAt', 'stateChangedAt', 'dataCollected', 'stateChanges', 'notifications', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'AssessmentRunName', ], 'assessmentTemplateArn' => [ 'shape' => 'Arn', ], 'state' => [ 'shape' => 'AssessmentRunState', ], 'durationInSeconds' => [ 'shape' => 'AssessmentRunDuration', ], 'rulesPackageArns' => [ 'shape' => 'AssessmentRulesPackageArnList', ], 'userAttributesForFindings' => [ 'shape' => 'UserAttributeList', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'startedAt' => [ 'shape' => 'Timestamp', ], 'completedAt' => [ 'shape' => 'Timestamp', ], 'stateChangedAt' => [ 'shape' => 'Timestamp', ], 'dataCollected' => [ 'shape' => 'Bool', ], 'stateChanges' => [ 'shape' => 'AssessmentRunStateChangeList', ], 'notifications' => [ 'shape' => 'AssessmentRunNotificationList', ], ], ], 'AssessmentRunAgent' => [ 'type' => 'structure', 'required' => [ 'agentId', 'assessmentRunArn', 'agentHealth', 'agentHealthCode', 'telemetryMetadata', ], 'members' => [ 'agentId' => [ 'shape' => 'AgentId', ], 'assessmentRunArn' => [ 'shape' => 'Arn', ], 'agentHealth' => [ 'shape' => 'AgentHealth', ], 'agentHealthCode' => [ 'shape' => 'AgentHealthCode', ], 'agentHealthDetails' => [ 'shape' => 'Message', ], 'autoScalingGroup' => [ 'shape' => 'AutoScalingGroup', ], 'telemetryMetadata' => [ 'shape' => 'TelemetryMetadataList', ], ], ], 'AssessmentRunAgentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentRunAgent', ], 'max' => 500, 'min' => 0, ], 'AssessmentRunDuration' => [ 'type' => 'integer', 'max' => 86400, 'min' => 180, ], 'AssessmentRunFilter' => [ 'type' => 'structure', 'members' => [ 'namePattern' => [ 'shape' => 'NamePattern', ], 'states' => [ 'shape' => 'AssessmentRunStateList', ], 'durationRange' => [ 'shape' => 'DurationRange', ], 'rulesPackageArns' => [ 'shape' => 'FilterRulesPackageArnList', ], 'startTimeRange' => [ 'shape' => 'TimestampRange', ], 'completionTimeRange' => [ 'shape' => 'TimestampRange', ], 'stateChangeTimeRange' => [ 'shape' => 'TimestampRange', ], ], ], 'AssessmentRunInProgressArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 10, 'min' => 1, ], 'AssessmentRunInProgressException' => [ 'type' => 'structure', 'required' => [ 'message', 'assessmentRunArns', 'assessmentRunArnsTruncated', 'canRetry', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'assessmentRunArns' => [ 'shape' => 'AssessmentRunInProgressArnList', ], 'assessmentRunArnsTruncated' => [ 'shape' => 'Bool', ], 'canRetry' => [ 'shape' => 'Bool', ], ], 'exception' => true, ], 'AssessmentRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentRun', ], 'max' => 10, 'min' => 0, ], 'AssessmentRunName' => [ 'type' => 'string', 'max' => 140, 'min' => 1, ], 'AssessmentRunNotification' => [ 'type' => 'structure', 'required' => [ 'date', 'event', 'error', ], 'members' => [ 'date' => [ 'shape' => 'Timestamp', ], 'event' => [ 'shape' => 'InspectorEvent', ], 'message' => [ 'shape' => 'Message', ], 'error' => [ 'shape' => 'Bool', ], 'snsTopicArn' => [ 'shape' => 'Arn', ], 'snsPublishStatusCode' => [ 'shape' => 'AssessmentRunNotificationSnsStatusCode', ], ], ], 'AssessmentRunNotificationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentRunNotification', ], 'max' => 50, 'min' => 0, ], 'AssessmentRunNotificationSnsStatusCode' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'TOPIC_DOES_NOT_EXIST', 'ACCESS_DENIED', 'INTERNAL_ERROR', ], ], 'AssessmentRunState' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'START_DATA_COLLECTION_PENDING', 'START_DATA_COLLECTION_IN_PROGRESS', 'COLLECTING_DATA', 'STOP_DATA_COLLECTION_PENDING', 'DATA_COLLECTED', 'EVALUATING_RULES', 'FAILED', 'COMPLETED', 'COMPLETED_WITH_ERRORS', ], ], 'AssessmentRunStateChange' => [ 'type' => 'structure', 'required' => [ 'stateChangedAt', 'state', ], 'members' => [ 'stateChangedAt' => [ 'shape' => 'Timestamp', ], 'state' => [ 'shape' => 'AssessmentRunState', ], ], ], 'AssessmentRunStateChangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentRunStateChange', ], 'max' => 50, 'min' => 0, ], 'AssessmentRunStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentRunState', ], 'max' => 50, 'min' => 0, ], 'AssessmentTarget' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'resourceGroupArn', 'createdAt', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'AssessmentTargetName', ], 'resourceGroupArn' => [ 'shape' => 'Arn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'AssessmentTargetFilter' => [ 'type' => 'structure', 'members' => [ 'assessmentTargetNamePattern' => [ 'shape' => 'NamePattern', ], ], ], 'AssessmentTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentTarget', ], 'max' => 10, 'min' => 0, ], 'AssessmentTargetName' => [ 'type' => 'string', 'max' => 140, 'min' => 1, ], 'AssessmentTemplate' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'assessmentTargetArn', 'durationInSeconds', 'rulesPackageArns', 'userAttributesForFindings', 'createdAt', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'AssessmentTemplateName', ], 'assessmentTargetArn' => [ 'shape' => 'Arn', ], 'durationInSeconds' => [ 'shape' => 'AssessmentRunDuration', ], 'rulesPackageArns' => [ 'shape' => 'AssessmentTemplateRulesPackageArnList', ], 'userAttributesForFindings' => [ 'shape' => 'UserAttributeList', ], 'createdAt' => [ 'shape' => 'Timestamp', ], ], ], 'AssessmentTemplateFilter' => [ 'type' => 'structure', 'members' => [ 'namePattern' => [ 'shape' => 'NamePattern', ], 'durationRange' => [ 'shape' => 'DurationRange', ], 'rulesPackageArns' => [ 'shape' => 'FilterRulesPackageArnList', ], ], ], 'AssessmentTemplateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentTemplate', ], 'max' => 10, 'min' => 0, ], 'AssessmentTemplateName' => [ 'type' => 'string', 'max' => 140, 'min' => 1, ], 'AssessmentTemplateRulesPackageArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 50, 'min' => 0, ], 'AssetAttributes' => [ 'type' => 'structure', 'required' => [ 'schemaVersion', ], 'members' => [ 'schemaVersion' => [ 'shape' => 'NumericVersion', ], 'agentId' => [ 'shape' => 'AgentId', ], 'autoScalingGroup' => [ 'shape' => 'AutoScalingGroup', ], 'amiId' => [ 'shape' => 'AmiId', ], 'hostname' => [ 'shape' => 'Hostname', ], 'ipv4Addresses' => [ 'shape' => 'Ipv4AddressList', ], ], ], 'AssetType' => [ 'type' => 'string', 'enum' => [ 'ec2-instance', ], ], 'Attribute' => [ 'type' => 'structure', 'required' => [ 'key', ], 'members' => [ 'key' => [ 'shape' => 'AttributeKey', ], 'value' => [ 'shape' => 'AttributeValue', ], ], ], 'AttributeKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'AttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], 'max' => 50, 'min' => 0, ], 'AttributeValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'AutoScalingGroup' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'AutoScalingGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingGroup', ], 'max' => 20, 'min' => 0, ], 'BatchDescribeArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 10, 'min' => 1, ], 'Bool' => [ 'type' => 'boolean', ], 'CreateAssessmentTargetRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentTargetName', 'resourceGroupArn', ], 'members' => [ 'assessmentTargetName' => [ 'shape' => 'AssessmentTargetName', ], 'resourceGroupArn' => [ 'shape' => 'Arn', ], ], ], 'CreateAssessmentTargetResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentTargetArn', ], 'members' => [ 'assessmentTargetArn' => [ 'shape' => 'Arn', ], ], ], 'CreateAssessmentTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentTargetArn', 'assessmentTemplateName', 'durationInSeconds', 'rulesPackageArns', ], 'members' => [ 'assessmentTargetArn' => [ 'shape' => 'Arn', ], 'assessmentTemplateName' => [ 'shape' => 'AssessmentTemplateName', ], 'durationInSeconds' => [ 'shape' => 'AssessmentRunDuration', ], 'rulesPackageArns' => [ 'shape' => 'AssessmentTemplateRulesPackageArnList', ], 'userAttributesForFindings' => [ 'shape' => 'UserAttributeList', ], ], ], 'CreateAssessmentTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentTemplateArn', ], 'members' => [ 'assessmentTemplateArn' => [ 'shape' => 'Arn', ], ], ], 'CreateResourceGroupRequest' => [ 'type' => 'structure', 'required' => [ 'resourceGroupTags', ], 'members' => [ 'resourceGroupTags' => [ 'shape' => 'ResourceGroupTags', ], ], ], 'CreateResourceGroupResponse' => [ 'type' => 'structure', 'required' => [ 'resourceGroupArn', ], 'members' => [ 'resourceGroupArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteAssessmentRunRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentRunArn', ], 'members' => [ 'assessmentRunArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteAssessmentTargetRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentTargetArn', ], 'members' => [ 'assessmentTargetArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteAssessmentTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentTemplateArn', ], 'members' => [ 'assessmentTemplateArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAssessmentRunsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentRunArns', ], 'members' => [ 'assessmentRunArns' => [ 'shape' => 'BatchDescribeArnList', ], ], ], 'DescribeAssessmentRunsResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentRuns', 'failedItems', ], 'members' => [ 'assessmentRuns' => [ 'shape' => 'AssessmentRunList', ], 'failedItems' => [ 'shape' => 'FailedItems', ], ], ], 'DescribeAssessmentTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentTargetArns', ], 'members' => [ 'assessmentTargetArns' => [ 'shape' => 'BatchDescribeArnList', ], ], ], 'DescribeAssessmentTargetsResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentTargets', 'failedItems', ], 'members' => [ 'assessmentTargets' => [ 'shape' => 'AssessmentTargetList', ], 'failedItems' => [ 'shape' => 'FailedItems', ], ], ], 'DescribeAssessmentTemplatesRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentTemplateArns', ], 'members' => [ 'assessmentTemplateArns' => [ 'shape' => 'BatchDescribeArnList', ], ], ], 'DescribeAssessmentTemplatesResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentTemplates', 'failedItems', ], 'members' => [ 'assessmentTemplates' => [ 'shape' => 'AssessmentTemplateList', ], 'failedItems' => [ 'shape' => 'FailedItems', ], ], ], 'DescribeCrossAccountAccessRoleResponse' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'valid', 'registeredAt', ], 'members' => [ 'roleArn' => [ 'shape' => 'Arn', ], 'valid' => [ 'shape' => 'Bool', ], 'registeredAt' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'findingArns', ], 'members' => [ 'findingArns' => [ 'shape' => 'BatchDescribeArnList', ], 'locale' => [ 'shape' => 'Locale', ], ], ], 'DescribeFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'findings', 'failedItems', ], 'members' => [ 'findings' => [ 'shape' => 'FindingList', ], 'failedItems' => [ 'shape' => 'FailedItems', ], ], ], 'DescribeResourceGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'resourceGroupArns', ], 'members' => [ 'resourceGroupArns' => [ 'shape' => 'BatchDescribeArnList', ], ], ], 'DescribeResourceGroupsResponse' => [ 'type' => 'structure', 'required' => [ 'resourceGroups', 'failedItems', ], 'members' => [ 'resourceGroups' => [ 'shape' => 'ResourceGroupList', ], 'failedItems' => [ 'shape' => 'FailedItems', ], ], ], 'DescribeRulesPackagesRequest' => [ 'type' => 'structure', 'required' => [ 'rulesPackageArns', ], 'members' => [ 'rulesPackageArns' => [ 'shape' => 'BatchDescribeArnList', ], 'locale' => [ 'shape' => 'Locale', ], ], ], 'DescribeRulesPackagesResponse' => [ 'type' => 'structure', 'required' => [ 'rulesPackages', 'failedItems', ], 'members' => [ 'rulesPackages' => [ 'shape' => 'RulesPackageList', ], 'failedItems' => [ 'shape' => 'FailedItems', ], ], ], 'DurationRange' => [ 'type' => 'structure', 'members' => [ 'minSeconds' => [ 'shape' => 'AssessmentRunDuration', ], 'maxSeconds' => [ 'shape' => 'AssessmentRunDuration', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'EventSubscription' => [ 'type' => 'structure', 'required' => [ 'event', 'subscribedAt', ], 'members' => [ 'event' => [ 'shape' => 'InspectorEvent', ], 'subscribedAt' => [ 'shape' => 'Timestamp', ], ], ], 'EventSubscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSubscription', ], 'max' => 50, 'min' => 1, ], 'FailedItemDetails' => [ 'type' => 'structure', 'required' => [ 'failureCode', 'retryable', ], 'members' => [ 'failureCode' => [ 'shape' => 'FailedItemErrorCode', ], 'retryable' => [ 'shape' => 'Bool', ], ], ], 'FailedItemErrorCode' => [ 'type' => 'string', 'enum' => [ 'INVALID_ARN', 'DUPLICATE_ARN', 'ITEM_DOES_NOT_EXIST', 'ACCESS_DENIED', 'LIMIT_EXCEEDED', 'INTERNAL_ERROR', ], ], 'FailedItems' => [ 'type' => 'map', 'key' => [ 'shape' => 'Arn', ], 'value' => [ 'shape' => 'FailedItemDetails', ], ], 'FilterRulesPackageArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 50, 'min' => 0, ], 'Finding' => [ 'type' => 'structure', 'required' => [ 'arn', 'attributes', 'userAttributes', 'createdAt', 'updatedAt', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'schemaVersion' => [ 'shape' => 'NumericVersion', ], 'service' => [ 'shape' => 'ServiceName', ], 'serviceAttributes' => [ 'shape' => 'InspectorServiceAttributes', ], 'assetType' => [ 'shape' => 'AssetType', ], 'assetAttributes' => [ 'shape' => 'AssetAttributes', ], 'id' => [ 'shape' => 'FindingId', ], 'title' => [ 'shape' => 'Text', ], 'description' => [ 'shape' => 'Text', ], 'recommendation' => [ 'shape' => 'Text', ], 'severity' => [ 'shape' => 'Severity', ], 'numericSeverity' => [ 'shape' => 'NumericSeverity', ], 'confidence' => [ 'shape' => 'IocConfidence', ], 'indicatorOfCompromise' => [ 'shape' => 'Bool', ], 'attributes' => [ 'shape' => 'AttributeList', ], 'userAttributes' => [ 'shape' => 'UserAttributeList', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'FindingFilter' => [ 'type' => 'structure', 'members' => [ 'agentIds' => [ 'shape' => 'AgentIdList', ], 'autoScalingGroups' => [ 'shape' => 'AutoScalingGroupList', ], 'ruleNames' => [ 'shape' => 'RuleNameList', ], 'severities' => [ 'shape' => 'SeverityList', ], 'rulesPackageArns' => [ 'shape' => 'FilterRulesPackageArnList', ], 'attributes' => [ 'shape' => 'AttributeList', ], 'userAttributes' => [ 'shape' => 'AttributeList', ], 'creationTimeRange' => [ 'shape' => 'TimestampRange', ], ], ], 'FindingId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'FindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Finding', ], 'max' => 10, 'min' => 0, ], 'GetTelemetryMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentRunArn', ], 'members' => [ 'assessmentRunArn' => [ 'shape' => 'Arn', ], ], ], 'GetTelemetryMetadataResponse' => [ 'type' => 'structure', 'required' => [ 'telemetryMetadata', ], 'members' => [ 'telemetryMetadata' => [ 'shape' => 'TelemetryMetadataList', ], ], ], 'Hostname' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'InspectorEvent' => [ 'type' => 'string', 'enum' => [ 'ASSESSMENT_RUN_STARTED', 'ASSESSMENT_RUN_COMPLETED', 'ASSESSMENT_RUN_STATE_CHANGED', 'FINDING_REPORTED', 'OTHER', ], ], 'InspectorServiceAttributes' => [ 'type' => 'structure', 'required' => [ 'schemaVersion', ], 'members' => [ 'schemaVersion' => [ 'shape' => 'NumericVersion', ], 'assessmentRunArn' => [ 'shape' => 'Arn', ], 'rulesPackageArn' => [ 'shape' => 'Arn', ], ], ], 'InternalException' => [ 'type' => 'structure', 'required' => [ 'message', 'canRetry', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'canRetry' => [ 'shape' => 'Bool', ], ], 'exception' => true, 'fault' => true, ], 'InvalidCrossAccountRoleErrorCode' => [ 'type' => 'string', 'enum' => [ 'ROLE_DOES_NOT_EXIST_OR_INVALID_TRUST_RELATIONSHIP', 'ROLE_DOES_NOT_HAVE_CORRECT_POLICY', ], ], 'InvalidCrossAccountRoleException' => [ 'type' => 'structure', 'required' => [ 'message', 'errorCode', 'canRetry', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'errorCode' => [ 'shape' => 'InvalidCrossAccountRoleErrorCode', ], 'canRetry' => [ 'shape' => 'Bool', ], ], 'exception' => true, ], 'InvalidInputErrorCode' => [ 'type' => 'string', 'enum' => [ 'INVALID_ASSESSMENT_TARGET_ARN', 'INVALID_ASSESSMENT_TEMPLATE_ARN', 'INVALID_ASSESSMENT_RUN_ARN', 'INVALID_FINDING_ARN', 'INVALID_RESOURCE_GROUP_ARN', 'INVALID_RULES_PACKAGE_ARN', 'INVALID_RESOURCE_ARN', 'INVALID_SNS_TOPIC_ARN', 'INVALID_IAM_ROLE_ARN', 'INVALID_ASSESSMENT_TARGET_NAME', 'INVALID_ASSESSMENT_TARGET_NAME_PATTERN', 'INVALID_ASSESSMENT_TEMPLATE_NAME', 'INVALID_ASSESSMENT_TEMPLATE_NAME_PATTERN', 'INVALID_ASSESSMENT_TEMPLATE_DURATION', 'INVALID_ASSESSMENT_TEMPLATE_DURATION_RANGE', 'INVALID_ASSESSMENT_RUN_DURATION_RANGE', 'INVALID_ASSESSMENT_RUN_START_TIME_RANGE', 'INVALID_ASSESSMENT_RUN_COMPLETION_TIME_RANGE', 'INVALID_ASSESSMENT_RUN_STATE_CHANGE_TIME_RANGE', 'INVALID_ASSESSMENT_RUN_STATE', 'INVALID_TAG', 'INVALID_TAG_KEY', 'INVALID_TAG_VALUE', 'INVALID_RESOURCE_GROUP_TAG_KEY', 'INVALID_RESOURCE_GROUP_TAG_VALUE', 'INVALID_ATTRIBUTE', 'INVALID_USER_ATTRIBUTE', 'INVALID_USER_ATTRIBUTE_KEY', 'INVALID_USER_ATTRIBUTE_VALUE', 'INVALID_PAGINATION_TOKEN', 'INVALID_MAX_RESULTS', 'INVALID_AGENT_ID', 'INVALID_AUTO_SCALING_GROUP', 'INVALID_RULE_NAME', 'INVALID_SEVERITY', 'INVALID_LOCALE', 'INVALID_EVENT', 'ASSESSMENT_TARGET_NAME_ALREADY_TAKEN', 'ASSESSMENT_TEMPLATE_NAME_ALREADY_TAKEN', 'INVALID_NUMBER_OF_ASSESSMENT_TARGET_ARNS', 'INVALID_NUMBER_OF_ASSESSMENT_TEMPLATE_ARNS', 'INVALID_NUMBER_OF_ASSESSMENT_RUN_ARNS', 'INVALID_NUMBER_OF_FINDING_ARNS', 'INVALID_NUMBER_OF_RESOURCE_GROUP_ARNS', 'INVALID_NUMBER_OF_RULES_PACKAGE_ARNS', 'INVALID_NUMBER_OF_ASSESSMENT_RUN_STATES', 'INVALID_NUMBER_OF_TAGS', 'INVALID_NUMBER_OF_RESOURCE_GROUP_TAGS', 'INVALID_NUMBER_OF_ATTRIBUTES', 'INVALID_NUMBER_OF_USER_ATTRIBUTES', 'INVALID_NUMBER_OF_AGENT_IDS', 'INVALID_NUMBER_OF_AUTO_SCALING_GROUPS', 'INVALID_NUMBER_OF_RULE_NAMES', 'INVALID_NUMBER_OF_SEVERITIES', ], ], 'InvalidInputException' => [ 'type' => 'structure', 'required' => [ 'message', 'errorCode', 'canRetry', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'errorCode' => [ 'shape' => 'InvalidInputErrorCode', ], 'canRetry' => [ 'shape' => 'Bool', ], ], 'exception' => true, ], 'IocConfidence' => [ 'type' => 'integer', 'max' => 10, 'min' => 0, ], 'Ipv4Address' => [ 'type' => 'string', 'max' => 15, 'min' => 7, ], 'Ipv4AddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ipv4Address', ], 'max' => 50, 'min' => 0, ], 'LimitExceededErrorCode' => [ 'type' => 'string', 'enum' => [ 'ASSESSMENT_TARGET_LIMIT_EXCEEDED', 'ASSESSMENT_TEMPLATE_LIMIT_EXCEEDED', 'ASSESSMENT_RUN_LIMIT_EXCEEDED', 'RESOURCE_GROUP_LIMIT_EXCEEDED', 'EVENT_SUBSCRIPTION_LIMIT_EXCEEDED', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'errorCode', 'canRetry', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'errorCode' => [ 'shape' => 'LimitExceededErrorCode', ], 'canRetry' => [ 'shape' => 'Bool', ], ], 'exception' => true, ], 'ListAssessmentRunAgentsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentRunArn', ], 'members' => [ 'assessmentRunArn' => [ 'shape' => 'Arn', ], 'filter' => [ 'shape' => 'AgentFilter', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'ListMaxResults', ], ], ], 'ListAssessmentRunAgentsResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentRunAgents', ], 'members' => [ 'assessmentRunAgents' => [ 'shape' => 'AssessmentRunAgentList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAssessmentRunsRequest' => [ 'type' => 'structure', 'members' => [ 'assessmentTemplateArns' => [ 'shape' => 'ListParentArnList', ], 'filter' => [ 'shape' => 'AssessmentRunFilter', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'ListMaxResults', ], ], ], 'ListAssessmentRunsResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentRunArns', ], 'members' => [ 'assessmentRunArns' => [ 'shape' => 'ListReturnedArnList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAssessmentTargetsRequest' => [ 'type' => 'structure', 'members' => [ 'filter' => [ 'shape' => 'AssessmentTargetFilter', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'ListMaxResults', ], ], ], 'ListAssessmentTargetsResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentTargetArns', ], 'members' => [ 'assessmentTargetArns' => [ 'shape' => 'ListReturnedArnList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListAssessmentTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'assessmentTargetArns' => [ 'shape' => 'ListParentArnList', ], 'filter' => [ 'shape' => 'AssessmentTemplateFilter', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'ListMaxResults', ], ], ], 'ListAssessmentTemplatesResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentTemplateArns', ], 'members' => [ 'assessmentTemplateArns' => [ 'shape' => 'ListReturnedArnList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEventSubscriptionsMaxResults' => [ 'type' => 'integer', ], 'ListEventSubscriptionsRequest' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'ListEventSubscriptionsMaxResults', ], ], ], 'ListEventSubscriptionsResponse' => [ 'type' => 'structure', 'required' => [ 'subscriptions', ], 'members' => [ 'subscriptions' => [ 'shape' => 'SubscriptionList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListFindingsRequest' => [ 'type' => 'structure', 'members' => [ 'assessmentRunArns' => [ 'shape' => 'ListParentArnList', ], 'filter' => [ 'shape' => 'FindingFilter', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'ListMaxResults', ], ], ], 'ListFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'findingArns', ], 'members' => [ 'findingArns' => [ 'shape' => 'ListReturnedArnList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListMaxResults' => [ 'type' => 'integer', ], 'ListParentArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 50, 'min' => 0, ], 'ListReturnedArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 100, 'min' => 0, ], 'ListRulesPackagesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'ListMaxResults', ], ], ], 'ListRulesPackagesResponse' => [ 'type' => 'structure', 'required' => [ 'rulesPackageArns', ], 'members' => [ 'rulesPackageArns' => [ 'shape' => 'ListReturnedArnList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'Locale' => [ 'type' => 'string', 'enum' => [ 'EN_US', ], ], 'Long' => [ 'type' => 'long', ], 'Message' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'MessageType' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'NamePattern' => [ 'type' => 'string', 'max' => 140, 'min' => 1, ], 'NoSuchEntityErrorCode' => [ 'type' => 'string', 'enum' => [ 'ASSESSMENT_TARGET_DOES_NOT_EXIST', 'ASSESSMENT_TEMPLATE_DOES_NOT_EXIST', 'ASSESSMENT_RUN_DOES_NOT_EXIST', 'FINDING_DOES_NOT_EXIST', 'RESOURCE_GROUP_DOES_NOT_EXIST', 'RULES_PACKAGE_DOES_NOT_EXIST', 'SNS_TOPIC_DOES_NOT_EXIST', 'IAM_ROLE_DOES_NOT_EXIST', ], ], 'NoSuchEntityException' => [ 'type' => 'structure', 'required' => [ 'message', 'errorCode', 'canRetry', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'errorCode' => [ 'shape' => 'NoSuchEntityErrorCode', ], 'canRetry' => [ 'shape' => 'Bool', ], ], 'exception' => true, ], 'NumericSeverity' => [ 'type' => 'double', 'max' => 10.0, 'min' => 0.0, ], 'NumericVersion' => [ 'type' => 'integer', 'min' => 0, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'PreviewAgentsMaxResults' => [ 'type' => 'integer', ], 'PreviewAgentsRequest' => [ 'type' => 'structure', 'required' => [ 'previewAgentsArn', ], 'members' => [ 'previewAgentsArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'PreviewAgentsMaxResults', ], ], ], 'PreviewAgentsResponse' => [ 'type' => 'structure', 'required' => [ 'agentPreviews', ], 'members' => [ 'agentPreviews' => [ 'shape' => 'AgentPreviewList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ProviderName' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'RegisterCrossAccountAccessRoleRequest' => [ 'type' => 'structure', 'required' => [ 'roleArn', ], 'members' => [ 'roleArn' => [ 'shape' => 'Arn', ], ], ], 'RemoveAttributesFromFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'findingArns', 'attributeKeys', ], 'members' => [ 'findingArns' => [ 'shape' => 'AddRemoveAttributesFindingArnList', ], 'attributeKeys' => [ 'shape' => 'UserAttributeKeyList', ], ], ], 'RemoveAttributesFromFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'failedItems', ], 'members' => [ 'failedItems' => [ 'shape' => 'FailedItems', ], ], ], 'ResourceGroup' => [ 'type' => 'structure', 'required' => [ 'arn', 'tags', 'createdAt', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'ResourceGroupTags', ], 'createdAt' => [ 'shape' => 'Timestamp', ], ], ], 'ResourceGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceGroup', ], 'max' => 10, 'min' => 0, ], 'ResourceGroupTag' => [ 'type' => 'structure', 'required' => [ 'key', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'ResourceGroupTags' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceGroupTag', ], 'max' => 10, 'min' => 1, ], 'RuleName' => [ 'type' => 'string', 'max' => 1000, ], 'RuleNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleName', ], 'max' => 50, 'min' => 0, ], 'RulesPackage' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'version', 'provider', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'RulesPackageName', ], 'version' => [ 'shape' => 'Version', ], 'provider' => [ 'shape' => 'ProviderName', ], 'description' => [ 'shape' => 'Text', ], ], ], 'RulesPackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RulesPackage', ], 'max' => 10, 'min' => 0, ], 'RulesPackageName' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ServiceName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'SetTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'Severity' => [ 'type' => 'string', 'enum' => [ 'Low', 'Medium', 'High', 'Informational', 'Undefined', ], ], 'SeverityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Severity', ], 'max' => 50, 'min' => 0, ], 'StartAssessmentRunRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentTemplateArn', ], 'members' => [ 'assessmentTemplateArn' => [ 'shape' => 'Arn', ], 'assessmentRunName' => [ 'shape' => 'AssessmentRunName', ], ], ], 'StartAssessmentRunResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentRunArn', ], 'members' => [ 'assessmentRunArn' => [ 'shape' => 'Arn', ], ], ], 'StopAssessmentRunRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentRunArn', ], 'members' => [ 'assessmentRunArn' => [ 'shape' => 'Arn', ], ], ], 'SubscribeToEventRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'event', 'topicArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'event' => [ 'shape' => 'InspectorEvent', ], 'topicArn' => [ 'shape' => 'Arn', ], ], ], 'Subscription' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'topicArn', 'eventSubscriptions', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'topicArn' => [ 'shape' => 'Arn', ], 'eventSubscriptions' => [ 'shape' => 'EventSubscriptionList', ], ], ], 'SubscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subscription', ], 'max' => 50, 'min' => 0, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 10, 'min' => 0, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'TelemetryMetadata' => [ 'type' => 'structure', 'required' => [ 'messageType', 'count', ], 'members' => [ 'messageType' => [ 'shape' => 'MessageType', ], 'count' => [ 'shape' => 'Long', ], 'dataSize' => [ 'shape' => 'Long', ], ], ], 'TelemetryMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TelemetryMetadata', ], 'max' => 5000, 'min' => 0, ], 'Text' => [ 'type' => 'string', 'max' => 20000, 'min' => 0, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampRange' => [ 'type' => 'structure', 'members' => [ 'beginDate' => [ 'shape' => 'Timestamp', ], 'endDate' => [ 'shape' => 'Timestamp', ], ], ], 'UnsubscribeFromEventRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'event', 'topicArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'event' => [ 'shape' => 'InspectorEvent', ], 'topicArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateAssessmentTargetRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentTargetArn', 'assessmentTargetName', 'resourceGroupArn', ], 'members' => [ 'assessmentTargetArn' => [ 'shape' => 'Arn', ], 'assessmentTargetName' => [ 'shape' => 'AssessmentTargetName', ], 'resourceGroupArn' => [ 'shape' => 'Arn', ], ], ], 'UserAttributeKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeKey', ], 'max' => 10, 'min' => 0, ], 'UserAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], 'max' => 10, 'min' => 0, ], 'Version' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], ],];
