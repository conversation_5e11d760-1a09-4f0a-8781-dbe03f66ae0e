<?php
// This file was auto-generated from sdk-root/src/data/logs/2014-03-28/paginators-1.json
return [ 'pagination' => [ 'DescribeDestinations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'limit', 'result_key' => 'destinations', ], 'DescribeLogGroups' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'limit', 'result_key' => 'logGroups', ], 'DescribeLogStreams' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'limit', 'result_key' => 'logStreams', ], 'DescribeMetricFilters' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'limit', 'result_key' => 'metricFilters', ], 'DescribeSubscriptionFilters' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'limit', 'result_key' => 'subscriptionFilters', ], 'FilterLogEvents' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'limit', 'result_key' => [ 'events', 'searchedLogStreams', ], ], 'GetLogEvents' => [ 'input_token' => 'nextToken', 'output_token' => 'nextForwardToken', 'limit_key' => 'limit', 'result_key' => 'events', ], ],];
