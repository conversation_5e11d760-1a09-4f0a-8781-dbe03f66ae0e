<?php

	/**
	 * WT Cloud Single Website Installation System
	 * -------------------------------------------
	 * @version  0.0.1
	 * <AUTHOR> <<EMAIL>>
	 */
	
	// Required Files
	require_once('vendor/autoload.php');

	// Settings
	$settings = array(

		// Folders locations
		'temp_path' => '.temp',
		'system_path' => 'system',
		'panels_path' => 'system/panels',

		// Account
		'username' => '<EMAIL>',
		'password' => 'a72:83?.zelA:%0F24',

		// Repositories
		'panels_repo' => 'panels',
		'system_repo' => 'wt-cloud-system'

	);

	// Instantiate Climate
	// --------------------------------
	// http://climate.thephpleague.com/
	$climate = new League\CLImate\CLImate;

	// Are we in the right directory ?
	if(!strstr($argv[0], 'cli/')) return $climate->backgroundRed('Error: Please be sure that you\'re executing this script from the root directory');

	// Okay let's check if the folders already exists or not
	if(!is_dir($settings['system_path'])) @mkdir($settings['system_path']);
	if(!is_dir($settings['panels_path'])) @mkdir($settings['panels_path']);

	// Delete the .temp folder if it exists
	if(is_dir($settings['temp_path'])) removeFolder($settings['temp_path']);

	// Create the temporary folder
	@mkdir($settings['temp_path']);

	// Clone the system & panels into the temporary folder
	shell_exec('git clone http://'. urlencode($settings['username']) .':'. urlencode($settings['password']) .'@slsapp.com/git/wt/'. $settings['system_repo'] .'.git '. $settings['temp_path'] .'/wt-cloud-system');
	shell_exec('git clone http://'. urlencode($settings['username']) .':'. urlencode($settings['password']) .'@slsapp.com/git/wt/'. $settings['panels_repo'] .'.git '. $settings['temp_path'] .'/wt-cloud-panels');

	// Remove .git & .gitignore from folders
	if(file_exists($settings['temp_path'] .'/wt-cloud-system/.gitignore')) unlink($settings['temp_path'] .'/wt-cloud-system/.gitignore');
	if(file_exists($settings['temp_path'] .'/wt-cloud-panels/.gitignore')) unlink($settings['temp_path'] .'/wt-cloud-panels/.gitignore');
	if(is_dir($settings['temp_path'] .'/wt-cloud-system/.git')) removeFolder($settings['temp_path'] .'/wt-cloud-system/.git');
	if(is_dir($settings['temp_path'] .'/wt-cloud-panels/.git')) removeFolder($settings['temp_path'] .'/wt-cloud-panels/.git');

	// Now let's merge the files into the panels & system folder with "ditto" command
	shell_exec('ditto '. $settings['temp_path'] .'/wt-cloud-panels '. $settings['panels_path']);
	shell_exec('ditto '. $settings['temp_path'] .'/wt-cloud-system '. $settings['system_path']);

	// Remove the temporary folder
	if(is_dir($settings['temp_path'])) removeFolder($settings['temp_path']);

	// Success !
	$climate->lightGreen('System installed/updated with success !');

	// -------------------------------------------------------------------------------------- //
	// Helpful Functions

	/**
	 * Remove Folder Recursively
	 * @param  string $dir
	 * @return N/A
	 */
	function removeFolder($dir) {
		$it = new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS);
		$files = new RecursiveIteratorIterator($it,
		             RecursiveIteratorIterator::CHILD_FIRST);
		foreach($files as $file) {
		    if ($file->isDir()){
		        rmdir($file->getRealPath());
		    } else {
		        unlink($file->getRealPath());
		    }
		}
		rmdir($dir);
	}
	