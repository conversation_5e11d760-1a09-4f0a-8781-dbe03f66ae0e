<?php

	/**
	 * WT Cloud Single Website Panel CLI
	 * -------------------------------------------
	 * @version  0.0.1
	 * <AUTHOR> <<EMAIL>>
	 */
	
	// Required Files & Config
	require_once 'vendor/autoload.php';
	$config = include 'config.php';

	// Instantiate Climate
	// --------------------------------
	// http://climate.thephpleague.com/
	$climate = new League\CLImate\CLImate;

	// Parameters
	$db_port = (isset($argv[2])) ? ';port='. $argv[2] : null;
	$location = (isset($argv[1]) && strstr($argv[1], '/')) ? $argv[1] : null;
	$category = (strstr($location, '/')) ? explode('/', $location)[0] : null;
	$panel_name = (strstr($location, '/')) ? end((explode('/', $location))) : null;
	$panel_title = (strstr($location, '/')) ? ucwords(str_replace('-', ' ', end((explode('/', $location))))) : null;
	$class = ($panel_name) ? "$category-$panel_name" : null;
	$destination_folder = "panels/$category/$panel_name";
	$override = (is_dir("system/panels/$location")) ? true : false;

	// No system folder !
	if(!is_dir('system')) return $climate->backgroundRed('Error: WT Cloud System & Panels are not installed. Please run php/cli install.');

	// No panel specified :(
	if(!$location) return $climate->backgroundRed('Error: Panel name/location required.');

	// Panel already exists !
	if(is_dir($destination_folder)) return $climate->backgroundRed('Error: Panel name/location already exists.');

	// Are we in the right directory ?
	if(!strstr($argv[0], 'cli/')) return $climate->backgroundRed('Error: Please be sure that you\'re executing this script from the root directory');

	// Check if the folders exists, if
	// not, create them.
	if(!is_dir('panels')) @mkdir('panels');
	if(!is_dir("panels/$category")) @mkdir("panels/$category");
	if(!is_dir($destination_folder)) @mkdir($destination_folder);
	if(!is_dir("$destination_folder/assets")) @mkdir("$destination_folder/assets");
	if(!is_dir("$destination_folder/assets/scss")) @mkdir("$destination_folder/assets/scss");
	if(!is_dir("$destination_folder/assets/js")) @mkdir("$destination_folder/assets/js");
	if(!is_dir("$destination_folder/views")) @mkdir("$destination_folder/views");

	// Is that a override panel ?
	if($override && is_dir("system/panels/$location")) {
		// Copy the existing files
		$scss_file = file_get_contents("system/panels/$location/assets/scss/default.scss");
		$js_file = file_get_contents("system/panels/$location/assets/js/default.js");
		$html_file = file_get_contents("system/panels/$location/views/default.html");
		// Replace panels names
		$scss_file = str_replace($class, "$class-override", $scss_file);
		$js_file = str_replace($class, "$class-override", $js_file);
		$html_file = str_replace($class, "$class-override", $html_file);
	} else {
		// Not a override, create the new files !
		$scss_file = file_get_contents("cli/templates/default.scss");
		$js_file = file_get_contents("cli/templates/default.js");
		$html_file = file_get_contents("cli/templates/default.html");
		// Replace panels names
		$scss_file = str_replace('{name}', $class, $scss_file);
		$js_file = str_replace('{name}', $class, $js_file);
		$html_file = str_replace('{name}', $class, $html_file);
	}

	// Create the files
	createFile("$destination_folder/assets/scss/default.scss", $scss_file);
	createFile("$destination_folder/assets/js/default.js", $js_file);
	createFile("$destination_folder/views/default.html", $html_file);

	// We were just cloning the panel, stop here.
	if($override) return $climate->lightGreen('Panel created with success !');

	// Files are now created, we will try to connect to the database now.
	// ------------------------------------------------------------------
	$db_details = $config['database']['local'];
	try {
		$db = new PDO('mysql:host=127.0.0.1;dbname='. $db_details['database'] . $db_port .';charset=utf8', $db_details['username'], $db_details['password']);
		$db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	} catch (Exception $e) {
		return $climate->backgroundRed('Error: Can\'t connect to the database. '. $e->getMessage());
	}

	// Check if the panel already exists
	$query = $db->prepare('SELECT * FROM exp_panels_paneldefinition WHERE folder = ?');
	$query->bindParam(1, $location, PDO::PARAM_STR);
	$query->execute();
	$row = $query->fetch(PDO::FETCH_ASSOC);
	if($row) {
	    $climate->backgroundRed('Warning: The panel already exists in the database.');
	    return $climate->lightGreen('Panel created with success !');
	}

	// Create the panel
	$query = $db->prepare('INSERT INTO exp_panels_paneldefinition(shortname, name, instructions, folder) VALUES(:shortname, :name, :instructions, :folder)');
	$query->execute(array(
		'shortname' => uniqid(),
		'name' => $panel_title,
		'instructions' => '',
		'folder' => $location
	));

	// Last Insert ID
	$id = $db->lastInsertId();

	// Insert Blank Field
	$query = $db->prepare('INSERT INTO `exp_panels_atomdefinition`(`paneldefinition_id`, `shortname`, `name`, `instructions`, `order`, `type`, `settings`) VALUES(:paneldefinition_id, :shortname, :name, :instructions, :order, :type, :settings)');
	$query->execute(array(
		'paneldefinition_id' => $id,
		'shortname' => 'blank',
		'name' => 'Blank',
		'instructions' => '',
		'order' => 1,
		'type' => 'text',
		'settings' => '{"field_fmt":"none","field_content_type":"all","field_text_direction":"ltr","field_maxl":"256","col_required":"n","col_admin_only":"y","col_tab":"Content","col_custom_width":"100","col_search":"n"}'
	));

	// Success !
	$climate->lightGreen('Panel created with success !');

	// -------------------------------------------------------------------------------------- //
	// Helpful Functions
	
	/**
	 * Create new file
	 * ---------------
	 * @param  string $destination
	 * @param  string $content
	 * @return N/A
	 */
	function createFile($destination, $content) {
		$fp = fopen($destination, 'w+');
		fwrite($fp, $content);
		fclose($fp);
	}
	