{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "hash": "debb480e30ab64f558071b9fe4e19d92", "packages": [{"name": "aws/aws-sdk-php", "version": "3.18.10", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "483e21b602fc5c9993437e6e04e44d936392c344"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/483e21b602fc5c9993437e6e04e44d936392c344", "reference": "483e21b602fc5c9993437e6e04e44d936392c344", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~5.3|~6.0.1|~6.1", "guzzlehttp/promises": "~1.0", "guzzlehttp/psr7": "~1.0", "mtdowling/jmespath.php": "~2.2", "php": ">=5.5"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-json": "*", "ext-openssl": "*", "ext-pcre": "*", "ext-simplexml": "*", "ext-spl": "*", "nette/neon": "^2.3", "phpunit/phpunit": "~4.0|~5.0", "psr/cache": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Aws\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "time": "2016-05-18 20:23:24"}, {"name": "guzzlehttp/guzzle", "version": "6.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d094e337976dff9d8e2424e8485872194e768662"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d094e337976dff9d8e2424e8485872194e768662", "reference": "d094e337976dff9d8e2424e8485872194e768662", "shasum": ""}, "require": {"guzzlehttp/promises": "~1.0", "guzzlehttp/psr7": "~1.1", "php": ">=5.5.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~4.0", "psr/log": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2016-03-21 20:02:09"}, {"name": "guzzlehttp/promises", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "c10d860e2a9595f8883527fa0021c7da9e65f579"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/c10d860e2a9595f8883527fa0021c7da9e65f579", "reference": "c10d860e2a9595f8883527fa0021c7da9e65f579", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-05-18 16:56:05"}, {"name": "guzzlehttp/psr7", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "31382fef2889136415751badebbd1cb022a4ed72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/31382fef2889136415751badebbd1cb022a4ed72", "reference": "31382fef2889136415751badebbd1cb022a4ed72", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "PSR-7 message implementation", "keywords": ["http", "message", "stream", "uri"], "time": "2016-04-13 19:56:01"}, {"name": "league/climate", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/climate.git", "reference": "834cb907c89eb31e2171b68ee25c0ed26c8f34f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/climate/zipball/834cb907c89eb31e2171b68ee25c0ed26c8f34f4", "reference": "834cb907c89eb31e2171b68ee25c0ed26c8f34f4", "shasum": ""}, "require": {"php": ">=5.4.0", "seld/cli-prompt": "~1.0"}, "require-dev": {"mikey179/vfsstream": "~1.4", "mockery/mockery": "dev-master", "phpunit/phpunit": "~4.6"}, "type": "library", "autoload": {"psr-4": {"League\\CLImate\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://joe.codes/", "role": "Developer"}], "description": "PHP's best friend for the terminal. CLImate allows you to easily output colored text, special formats, and more.", "keywords": ["cli", "colors", "command", "php", "terminal"], "time": "2015-08-13 16:50:51"}, {"name": "mtdowling/jmespath.php", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "192f93e43c2c97acde7694993ab171b3de284093"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/192f93e43c2c97acde7694993ab171b3de284093", "reference": "192f93e43c2c97acde7694993ab171b3de284093", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "time": "2016-01-05 18:25:05"}, {"name": "psr/http-message", "version": "1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "85d63699f0dbedb190bbd4b0d2b9dc707ea4c298"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/85d63699f0dbedb190bbd4b0d2b9dc707ea4c298", "reference": "85d63699f0dbedb190bbd4b0d2b9dc707ea4c298", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2015-05-04 20:22:00"}, {"name": "seld/cli-prompt", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/Seldaek/cli-prompt.git", "reference": "8cbe10923cae5bcd7c5a713f6703fc4727c8c1b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/cli-prompt/zipball/8cbe10923cae5bcd7c5a713f6703fc4727c8c1b4", "reference": "8cbe10923cae5bcd7c5a713f6703fc4727c8c1b4", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\CliPrompt\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Allows you to prompt for user input on the command line, and optionally hide the characters they type", "keywords": ["cli", "console", "hidden", "input", "prompt"], "time": "2016-04-18 09:31:41"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": []}