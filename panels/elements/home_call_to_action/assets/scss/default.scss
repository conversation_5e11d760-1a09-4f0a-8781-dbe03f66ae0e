
//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.elements-home_call_to_action {
	background: url(/assets/images/design/cta_bg.png);
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	padding-top: 40px;
	padding-bottom: 40px;
	@include breakpoint(medium down){
//		padding-bottom: 55px;

	}
	.row{
		.column{
			@include breakpoint(small only){
				padding-top: 30px;
			}

			.content {
				display: flex;
			    justify-content: space-between;
			    align-items: center;
				
				@include breakpoint(medium down) {
			    	flex-direction: column; 
				}
			}
			&:first-child{
				position: relative;

				h3{
					color: $white;
					font-size: 30px;
					margin-bottom: 0;
					 
					@include breakpoint(medium down){
						margin-bottom: 20px;
					}
					@include breakpoint(small down){
						font-size: 1.7em;
						text-align: center;
					}
				}
			}
			&:last-child{
				div.button-wrap{
					display: flex;
				    justify-content: flex-end;
				    flex-wrap: wrap;
					margin-left: 20px;

					@include breakpoint(medium down) {
						position: relative;
						transform: none;
						margin-left: 0;
						flex-direction: column; 
						justify-content: center;
					}
					a.button{
						margin-top: 10px;
						margin-bottom: 10px;
						min-width: 190px;
						position: relative;
						padding: 22px 10px 15px;
						span {
							@include transitions();
							display: block;
							width: 100%;
						    height: 7px;
						    background: darken($primary-color, 10%);
						    @include horizontal-align();
						    bottom: -7px;
						    min-width: 190px;
						}
						@include breakpoint(small down){
							min-width: auto;
						}
						&:first-of-type{
							background-color: $white;
							color: $primary-color;
							
							span {
								background: white;
								bottom: -6px;
							}
							&:hover{
								color: darken($primary-color, 10%);
							}
							@include breakpoint(medium down){
//								margin-right: 20px;
							}
							@include breakpoint(small only){
								margin-right: 0;
								margin-bottom: 20px;
							}
						}
						&:nth-of-type(2){
							margin-left: 20px;

							@include breakpoint(medium down){
								margin-left: 0;
							}
						}
					}
				}
			}
		}
	}
}