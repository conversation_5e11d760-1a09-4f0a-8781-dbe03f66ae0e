
//  Header
// - - - - - - - - - - - - - - - - - - - - - - - -
.elements-header-override {
	padding: 0;
	&-logo {
		display: block;
		width: 100%;
		max-width: 217px;
		@include breakpoint(medium down) {
			padding: 10px 0;
		}
	}
	&-navigation {
		@include breakpoint(medium down) {
			display: none;
		}
	}
	ul.subnav {
		float: right;
		padding: 23px 0 30px;
		margin-bottom: 0;
		margin-right: 8px;
		@include breakpoint(large down) {
			padding: 23px 0 26px;
		}
		li {
			display: inline-block;
			font-family: 'AzoSansMedium';
			
			&:after {
				content: "|";
				color: $alt-gray-4;
			}
			a {
				padding: 0 10px;
				margin-right: 0;
				text-transform: uppercase;
				color: $primary-color;
				font-size: 15px;
				@include breakpoint(large down) {
					font-size: 13px;
				}
				&:hover{
					color: darken($primary-color,10%);
				}
			}
			&:last-child {
				&:after {
					display: none;
				}
				a {
					padding-right: 0;
				}
			}
		}
	}
	ul.menu {
		float: right;
		width: auto;
		li {
			margin-right: 13px;
			position: relative;
			@include breakpoint(xlarge only){
				margin-right: 6px;
			}
			@include breakpoint(large down){
				margin-right: 4px;
			}
			a {
				margin-right: 0;
				text-transform: uppercase;
				color: $secondary-color;
				padding: 0 8px 25px;
				border-bottom: 5px solid transparent;
				@include breakpoint(large down){
					padding: 0 3px 25px;
				}
				@include breakpoint(large down) {
					font-size: 0.9em;
				}
				&.active, &:hover {
					color: $primary-color;
					border-bottom: 5px solid $primary-color;
				}
				i{
					padding-left: 10px;
					margin-top: -2px;
					@include breakpoint(large down){
						padding-left: 4px;
					}
				}
			}
			&.about, &.programmes, &.alumni, &.testimonials {
				
				>a {
					cursor: auto;
				}

				&:hover {
					>a {
						border-bottom: 5px solid transparent;
					}
					div.dropdown {
						display: block; 
					}
				}
			}
			div.dropdown {
				position: absolute;
				display: none;
				z-index: 2;
				width: 270px;
				box-shadow: 0 13px 25px rgba(black, 0.4);
				ul {
					margin-left: 0;
					li {
						display: block;
						margin-right: 0;
						margin-bottom: 0;
						&.last {
							a {
								background: darken($light-gray, 4%);
							}
						}
						a {
							display: block;
							font-size: 0.76em;
						    padding: 15px 20px;
						    position: relative;
						    background: white;
						    border-bottom: 1px solid $alt-gray-3;
						    &:hover {
						    	color: $primary-color;
						    }
						    i {
						    	@include vertical-align();
						    	right: 20px;
						    }
						}
						&:last-child {
							a {
								border-bottom: none;
							}
						}
					}
				}
			}
			&:last-child{
				margin-right: 0;
			}
		}
	}
}

div.dropdown-mobile {

	ul {
		margin: 15px 0 0;
		padding: 0;

		li {
			display: block;
			font-size: 0.9em;

			a {
				padding-top: 0;
				padding-bottom: 0;

				&:hover {
					background: none;
				}

				&.active {
					color: $primary-color;
					background: none;
				}
			}
		}
	}
}