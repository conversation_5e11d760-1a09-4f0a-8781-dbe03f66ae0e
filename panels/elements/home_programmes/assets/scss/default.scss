
//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.elements-home_programmes {
	padding: 80px 0;
	@include breakpoint(medium down){
		padding: 70px 0;
	}
	@include breakpoint(small down){
		padding: 50px 0;
	}
	.row{
		@include breakpoint(medium down){
			margin: 0;
		}
		.column{
			&:last-of-type {
				.content {
					@include breakpoint(medium down){
						margin-bottom: 0;
					}
				}
			}
			.content{
				text-align: center;
				padding: 15px 30px 30px;
				@include breakpoint(medium down){
					padding: 0;
					margin-bottom: 40px;
				}
				i {
					color: $primary-color;
					font-size: 3em;
					margin-bottom: 20px;
					display: block;
				}
				p {
					color: $body-font-color;
				}
				span.link_button{
					padding: 16px 20px;
					min-width: 260px;
					position: relative;

					&:hover {
						background: $white;
						color: $primary-color;
					}
					span.bottom {
						@include transitions();
						display: none;
						width: 100.6%;
					    height: 7px;
					    background: darken($primary-color, 10%);
					    @include horizontal-align();
					    bottom: -7px;
					    min-width: 260px;
					}
					@include breakpoint(small down){
						min-width: auto;
					}
				}

				&:hover{
					-webkit-box-shadow: 0px 1px 35px 0px rgba(0,0,0,0.15);
					-moz-box-shadow: 0px 15px 35px 0px rgba(0,0,0,0.15);
					box-shadow: 0px 15px 35px 0px rgba(0,0,0,0.15);
					@include transitions();
					span.button{
						background-color: $primary-color;
						color: $white;
						span.bottom {
							display: block;
						}
						&:hover {
							background: darken($primary-color, 5%);
						}
					}

					@include breakpoint(medium down){
						&:hover{
							box-shadow: none;
						}
					}
				}
				h4{
					font-size: 1.2em;
					text-transform: uppercase;
					color: $secondary-color;
					font-family: $body-font-family;
					line-height: 1.6em;

				}
				p{
					line-height: 1.8em;
					margin-bottom: 20px;
				}
			}
		}
	}
}