<div id="{panels:id}" class="column small-12 elements-event_listing">
	<div class="row">
		<div class="column small-12">

			<a href="/events/past-events" class="link-button view-past">View Past Events<i class="icon icon-chevron-right2"></i></a>

			{exp:entries:events channel="events" dynamic="no" show_future_entries="yes" start_on="{current_time format='%Y-%m-%d 00:01'}"  limit="5" orderby="date" sort="asc" paginate_base="{triggers:original_uri}" paginate="bottom" disable="category_fields|member_data|primary_category" parse="inward" {triggers:entries}}

				<div class="event-container">
					{if events:event_image}
					<a href="{events:page_uri}" class="image-container" style="background-image: url({events:event_image});background-size: contain;background-repeat: no-repeat;background-position: top center;">
						<div class="image"></div>
					</a>
					{/if}
					<div class="text-container {if events:event_image}{if:else}no-image{/if}">
						<div class="date">
							<p class="day"><i class="icon icon-calendar5"></i>{events:entry_date format="%l, %j%S %F %Y"}</p>
							{if events:event_invitation_only == "Yes"}<p class="invitation">Invitation Only</p>{/if}
						</div>
						<a href="{events:page_uri}" class="title"><h4>{events:title}</h4></a>
						<div class="event-type">
							<p>
								<i class="icon {events:categories}{if events:category_id == "15"}flaticon-people{if:elseif events:category_id == "16"}flaticon-interface{if:elseif events:category_id == "17"}flaticon-social{if:elseif events:category_id == "18"}flaticon-icon-2994{if:elseif events:category_id == "19"}flaticon-up-arrow{if:else}flaticon-icon-2994{/if}{/events:categories}"></i>
								{events:categories}
										{events:category_name}&nbsp
								{/events:categories}
							</p>
						</div>
						<p>
							{exp:ce_str:ing remove_html="yes" length truncate="110"}
								{events:event_summary}
							{/exp:ce_str:ing}
						</p>
						<a href="{events:page_uri}" class="link-button">Find out More<i class="icon icon-chevron-right2"></i></a>
					</div>
				</div>

				{events:paginate}
					{sn:global_pagination}
				{/events:paginate}

			{/exp:entries:events}

		</div>
	</div>
</div>