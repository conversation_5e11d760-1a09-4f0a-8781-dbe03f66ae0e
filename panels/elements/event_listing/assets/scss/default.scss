
//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.elements-event_listing {
	.row {
		.column {
			.link-button {
				text-transform: uppercase;
			    font-family: "AzoSansMedium",Helvetica,Arial,sans-serif;
			    position: relative;
			    padding-right: 20px;
			    font-size: 1.05em;
			    i {
		    	    position: absolute;
				    right: 0;
				    position: absolute;
				    top: 50%;
				    -webkit-transform: translateY(-50%);
				    -ms-transform: translateY(-50%);
				    transform: translateY(-50%);
				    font-size: 1.1em;
			    }
			}
			a.link-button.view-past + .event-container {
				margin-top: 60px;
			}
			div.intro_row {
				margin-bottom: 40px;
			}
			.search-container {
				position: relative;
				margin-bottom: 70px;
				input {
					width: 100%;
					display: inline-block;
					height: 70px;
					padding: 0 30px;
					border: 1px solid #F3F3F3;
					color: $dark-gray;
					box-shadow: 0px 15px 35px 0px rgba(0,0,0,0.05);
					font-family: $body-font-family;
					margin-bottom: 0;
					font-size: 1.4em;
				}
				::-webkit-input-placeholder {
   					color: #D1D1D1;
				}
				:-moz-placeholder { /* Firefox 18- */
   					color: #D1D1D1;
				}
				::-moz-placeholder {  /* Firefox 19+ */
   					color: #D1D1D1;
				}
				:-ms-input-placeholder {  
   					color: #D1D1D1;
				}
				&:after {
					content:"\e1ba";
					font-family: 'icomoon';
					position: absolute;
					right: 30px;
					pointer-events: none;
					color: $primary-color;
					font-size: 1.5em;
					@include vertical-align();
				}	
			}
			div.event-container {
				margin-bottom: 60px;
				a.image-container {
					display: inline-block;
					width: 30%;
					height: 193px;
					float: right;
					margin-left: 20px;
					margin-top: 10px;
					background-size: cover;
					background-position: center;
					
					@include breakpoint (small down) {
						width: 100%;
						display: block;
						margin-left: 0;
						margin-bottom: 15px;
						float: none;
					}
					.image {
//						background: url(/assets/images/design/event-1.png);
						
						width: 100%;
						height: 100%;
					}
				}
				.text-container {
					width: 67%;
					display: inline-block;
					@include breakpoint (large down) {
						width: 66%;
					}
					@include breakpoint (small down) {
						width: 100%;
						display: block;
					}
					.date {
						margin-bottom: 10px;
						p.day {
							position: relative;
							font-size: 1.1em;
							padding-left: 30px;
							margin-bottom: 0;
							display: inline-block;
							vertical-align: top;
							i {
								position: absolute;
								left: 0;
								color: $primary-color;
								top: 7px;
								font-size: 1.1em;
							}
						}
						p.invitation {
							color: #BE2322;
							font-size: 1.1em;
							display: inline-block;
							margin-left: 20px;
							position: relative;
							padding-left: 20px;
							margin-bottom: 0;
							@include breakpoint (large down) {
								display: block;
								margin-left: 0;
								padding-left: 0;
							}
							&:before {
								content:"";
								height: 20px;
								width: 1px;
								background: $text-color;
								position: absolute;
								left: 0;
								@include vertical-align();
								@include breakpoint (large down) {
									display: none;
								}
							}
						}
					}
					a.title {
						text-transform: uppercase;

						&:hover {
							h4 {
								color: $primary-color;
							}
						}
					}
					h4 {
						font-family: $font-medium;
						font-size: 1.4em;
						@include transitions();
					}
					.event-type {
						margin-top: 10px;
						p {
							position: relative;
							text-transform: uppercase;
							color: $secondary-color;
							font-size: 1em;
							padding-left: 37px;
							margin-bottom: 0;
						}
						i {
							position: absolute;
							padding: 0;
							color: $white;
							background: $primary-color;
							font-size: 0.7em;
							left: 0;
							top: 3px;
							width: 25px;
							height: 25px;
							line-height: 25px;
							text-align: center;
						}
					}
					.link-button {
						text-transform: uppercase;
						font-family: $font-medium;
						position: relative;
						padding-right: 20px;
						font-size: 1.05em;
						i {
							position: absolute;
							right: 0;
							@include vertical-align();
							font-size: 1.1em;
						}
						&:hover {
							color: darken($primary-color, 10%);
						}
					}
				}
				.no-image {
					width: 100%;
				}
			}
		}
	}
}