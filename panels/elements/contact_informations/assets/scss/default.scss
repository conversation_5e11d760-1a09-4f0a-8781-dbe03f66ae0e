
//  Settings
// - - - - - - - - - - - - - - - - - - - - - - - -


//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.elements-contact-informations {
	
	h4{
		font-size:2.3rem;
		margin-bottom: 30px;

		@include breakpoint(small down) {
			font-size: 1.6rem;
		}
	}

	div.contact-info {
		margin-top: 20px;
		@include breakpoint(medium down) {
			margin-top: 0;
		}
		ul {
			list-style: none;
			margin-left: 0;
			margin-right: 30px;
			li {
				padding-left: 32px;
				position: relative;
				margin-bottom: 38px;
				@include breakpoint(medium down) {
					margin-bottom: 20px;
				}
				p {
					margin-bottom: 0;
					font-size: 20px;
					line-height: 1.5em;
				}
				i {
					position: absolute;
					left: 0;
					top: 4px;
					font-size: 1.1em;
					color: $primary-color;
					&.icon-phone5 {
						top: 5px;
					}
					&.icon-envelop {
						top: 9px;
					}
					&.icon-location2{
						top: 1px;
					}
					&.icon-chevron-right3{
						position:relative;
						font-size: .8rem;
						padding-left: 15px;
						top: 0px;
					}
					
				}
				a {
					display: inline-block;
					color: $dark-gray;
					font-size: 20px;
					i {
						font-size: 0.9em;
					}
					&:hover {
						color: $primary-color;
					}
				}

				a.google_map{
					color: $primary-color; 
					&:hover {
						color: darken($primary-color, 10%);
					}
				}
			}
			li.google_map{
					padding-left:0;
					text-transform: uppercase;
					p{
					 font-size: 16px;
					 font-weight: 600;
					}
				}
		}
	}
	div.contact-hours {
		p {
			&.day {
				margin-bottom: 0;
			}
			&.title {
				i {
					font-size: 0.8em;
				}
			}
		}
	}

}