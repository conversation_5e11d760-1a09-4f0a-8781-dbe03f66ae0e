
//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.elements-featured_testimonials {

	padding: 100px 0 50px;

	div.intro-text {
		margin-bottom: 40px;

		p {
			text-align: center;
		}
	}
	div.view-more {
		text-align: center;
		margin-top: 20px;
		a.button {
			min-width: 200px; 
			@include breakpoint(small down) {
				min-width: auto;
			}
		}
	}
	div.highlightvideo {
		display:none;
	}
	div.highlight-video {
		display: none; 
		.testimonial-container {
			margin-bottom: 50px;
			a.image-container {
				width: 100%;
				height: 253px;
				display: block;
				position: relative;
				margin-bottom: 25px;

				.image {
					background-size: cover;
					background-position: center;
					background-repeat: no-repeat;
					width: 100%;
					height: 100%;
					display: block;	
				}
				&:before {
					content:"";
					background: url(/assets/images/design/overlay-video.png);
					background-size: cover;
					background-position: center;
					background-repeat: no-repeat;
					width: 100%;
					height: 100%;
					position: absolute;
					left: 0;
					top: 0;
				}
				&:after {
					content:"\e385";
					font-family: 'icomoon';
					@include horizontal-vertical-align();
					color: $white;
					font-size: 2.1em;
					background: $primary-color;
					padding: 5px 10px 4px 16px;
					border-radius: 100%;
					@include transitions();
				}
				&:hover {
					&:after {
						background: darken($primary-color, 10%);
					}
				}
			}
			h5 {
				text-transform: uppercase;
				font-family: $font-medium;
				font-size: 1.2em;
			}
			p {
				font-size: 1em;
				margin-bottom: 9px;
			}
			a.link-button {
				text-transform: uppercase;
				font-family: $font-medium;
				position: relative;
				padding-right: 20px;
				font-size: 1.2em;
				i {
					position: absolute;
					right: 0;
					@include vertical-align();
					font-size: 1.05em;
					@include transitions();
					color: $primary-color;
				}
				&:hover {
					color: darken($primary-color, 10%);
					i {
						color: darken($primary-color, 10%);
					}
				}
			}
		}
	}
}