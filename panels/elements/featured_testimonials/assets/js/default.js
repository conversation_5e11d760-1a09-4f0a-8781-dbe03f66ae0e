$(document).ready(function() {
	
	if ($('div.highlight-video').length < 4) {
        $('div.elements-featured_testimonials div.view-more').hide();
    }

    $(function () {
	    $("div.highlight-video").slice(0, 3).show();
	    $("#loadMore").on('click', function (e) {
	        e.preventDefault();
	        $("div.highlight-video:hidden").slice(0, 3).slideDown();
	        if ($("div.highlight-video:hidden").length == 0) {
	            $("#loadMore").fadeOut('slow');
	        } 
	        $('html,body').animate({
	            scrollTop: $(this).offset().top
	        }, 1500);
	        auto_height();
	    });
	});

	$('a[href="#top"]').click(function () {
	    $('body,html').animate({
	        scrollTop: 0
	    }, 600);
	    return false;
	});

	$(window).scroll(function () {
	    if ($(this).scrollTop() > 50) {
	        $('.totop a').fadeIn();
	    } else {
	        $('.totop a').fadeOut();
	    }
	});

});