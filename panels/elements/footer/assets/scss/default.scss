
//  Footer
// - - - - - - - - - - - - - - - - - - - - - - - -
.elements-footer-override {
	border-top: 1px solid $medium-gray;
	padding-top: 50px;


	// Logo
	&-logo {
		display: block;
		max-width: 50%;
	}

	div.footer-items {
		display: flex;
    	justify-content: space-between;
		
		@include breakpoint(medium down){
    	    flex-wrap: wrap;
		}
	}

	div.item {
		@include breakpoint(medium down){
    	    margin-bottom: 30px;
		}
	}

	// Footer Copyright
	div.footer-copyright {
		margin-top: 15px;
		padding: 40px 0;
		position: relative;
		font-size: 0.9em;
		@include breakpoint(medium down){
			padding: 20px 0;
		}
		div {
			@include breakpoint(medium down){
				text-align: center;
			}
		}
		a{
			display: inline-block;
			img{
				max-width: 97px;

			}
		}
		div.links {
			@include breakpoint(medium down){
				margin-top: 8px;
			}
			a {
				display: inline-block;
				vertical-align: top;
				position: relative;
				margin-left: 0;
				color: $body-font-color;
				&:after{
					content: "|";
					padding: 0 10px;
					color: $text-color;
				}
				&:last-child{
					&:after{
						display: none;
					}
				}
				&:hover{
					color: $primary-color;

				}
			}
		}
		@include breakpoint(small only) {
			>div { text-align: center; }
		}
	}

	div {
		color: $body-font-color;
		&.school {
			width: 180px; 
			padding-right: .9375rem;

			@include breakpoint(medium down){
				width: 50%;
				padding-left: 0;
				padding-right: 1.5625rem;
			}

			@include breakpoint(small down) {
				width: 100%;
				padding-left: 0;
				padding-right: 0;
				margin-bottom: 40px;
			}

			div.container {
				
			}
			img {
				margin-top: 10px;
				width: 150px;
				max-width: 100%;
			}
		}

		&.about-us{
			width: 26%;
			padding-left: .9375rem;
			padding-right: .9375rem;

			@include breakpoint(medium down){
				width: 50%;
				padding-left: 1.5625rem;
				padding-right: 0;
			}

			@include breakpoint(small down) {
				width: 100%;
				padding-left: 0;
				padding-right: 0;
			}

			div.container {
//				padding-left: 10px;
//				@include breakpoint(medium down){
//					padding-right: 0;
//				}
			}
			p {
				font-size: 1.05em;
				color: $body-font-color;
			}
		}
		&.our-programme {
			width: 26%;
			padding-left: .9375rem;
			padding-right: .9375rem;

			@include breakpoint(medium down){
				width: 50%;
				padding-left: 0;
				padding-right: 1.5625rem;
			}

			@include breakpoint(small down) {
				width: 100%;
				padding-left: 0;
				padding-right: 0;
				margin-bottom: 40px;
			}

			div.container {
//				padding-right: 40px;
//    			padding-left: 10px; 
//    			@include breakpoint(large down){
//					padding-right: 20px;
//					padding-left: 10px;
//				}
//				@include breakpoint(medium down){
//					padding: 0;
//				}
			}
			a:hover{
				color: $primary-color;
				i{
					color: $primary-color;
				}
			}
		}

		&.contact{
			width: 270px;
			padding-left: .9375rem;

			@include breakpoint(medium down){
				width: 50%;
				padding-left: 1.5625rem;
				padding-right: 0;
			}

			@include breakpoint(small down) {
				width: 100%;
				padding-left: 0;
				padding-right: 0;
			}

			a.button {
				@include breakpoint(small down) {
					display: inline-block;
				}
			}

//			div.container {
//				padding-left: 35px;
//				padding-right: 70px;
//				@include breakpoint(large down){
//					padding-right: 0;
//				}
//				@include breakpoint(medium down){
//					padding: 0;
//				}
//			}
			div.listing-items{
				.item{
					margin-bottom: 30px;
					padding-left: 34px;
					a{
						color: $body-font-color;
						word-break: break-word;

						&:hover{
							color: $primary-color;
							@include transitions();
							i{
								color: $primary-color;
								@include transitions();

							}

						}
					}
				}
			}

			.about-social{
				ul{
					list-style-type: none;
					margin: 0;
					li{
						display: inline-block;
						padding-right: 10px;
						a{
							height: 40px;
							width: 40px;
							color: $white;
							background-color: $primary-color;
							border-radius: 100%;
							display: block;
							text-align: center;

							i{
								line-height: 40px;
								font-size: 22px;
								&.icon-linkedin2, &.icon-youtube3 {
									padding-left: 4px;
								}
							}
							&:hover{
								background-color: darken($primary-color, 10%);
							}
						}
					}
				}
			}
		}

		h4{
			margin-bottom: 30px;
			font-size: 1.6rem;
		}
		h5 {
			font-size: 1.2rem;
		}
		p{
			line-height: 1.8;
			font-size: 1.1em;
		}
		
	}

	// Listing Items
	div.listing-items {
		.item {
			display: block;
			margin-bottom: 25px;
			position: relative;
			color: $body-font-color;
			font-size: 1.05em;
			i{
				right: 0;
				color: $secondary-color;
				font-size: 18px;
				@include vertical-align();
				@include transitions;
			}
			
			&:last-child { margin-bottom: 0; }
		}
		&.icons .item {
			position: relative;
			padding-left: 23px;
			i.icon {
				position: absolute;
				top: 5px;
				left: 0;
				&.icon-location2, &.icon-phone5 {
					top: 10px;
				}
				&.icon-envelop {
					top: 13px;
					font-size: 0.9em;
				}
			}
		}
	}

}