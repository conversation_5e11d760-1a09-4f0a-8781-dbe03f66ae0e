
//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.elements-home_past_participants {
	background: lighten($secondary-color, 10%);
	padding-top: 70px;
	@include breakpoint(small down){
		padding-top: 50px;
	}
	h3{
		color: $white;
		text-align: center;
		margin-bottom: 70px;
		padding: 0 40px;
		@include breakpoint(medium down){
			margin-bottom: 40px;
		}
	}

	.row{
		margin: 0;
		.column{
			

			div.tabs-wrap{
				background-color: $white;
				border-top-left-radius: 5px;
				border-top-right-radius: 5px;
				padding: 0;
				border-bottom: 1px solid $medium-gray;
				height: 100%;
				margin: 0;
				float: left;
				width: 100%;
				@include breakpoint(large down){
					margin: 0;
				}
				@include breakpoint(medium down){
					margin: 0;
				}
				div.tab-box{

					width: 30%;
					display: inline-block;
					float: left;


					ul{
						list-style-type: none;
						margin-left: 0;
						margin-bottom: 0;
						li{
							&:first-child{
								a{
									border-top-left-radius: 5px;
									@include breakpoint(small down) {
										border-top-right-radius: 5px;
									}
								}
							}
							&:last-child {
								a {
									border-bottom: none;
								}
							}
							a{
								display: block;
								text-align: center;
								padding: 34px;
								text-transform: uppercase;
								background-color: $primary-color;
								color: $white;
								font-family: $font-medium;
								border-bottom: 1px solid darken($primary-color, 5%);
								@include breakpoint(medium down){
									padding: 29px 20px;
								}
								@include breakpoint(small down){
									padding: 10px 0;
								}
								&:hover {
									background: darken($primary-color, 10%);
								}
							}
							&.active{
								a{
									background-color: $white;
									color: $primary-color;
									&:hover {
										background-color: $white;
									}
								}
								
							}

						}
					}

					@include breakpoint(small down){
						display: block;
						width: 100%;
					}


				}

				div.logo-box{

					padding: 30px 40px 0;
					display: inline-block;
					width: 70%;
					height: 100%;
					float: left;
					@include breakpoint(large down){
						padding: 30px 20px 0;
					}
					@include breakpoint(small down){
						display: block;
						width: 100%;
					}


					.row{

						.column{

							text-align: center;


							.logo-box-logo{
								border: 1px solid $medium-gray;
								width: 100%;
								height: 90px;
								padding: 16px;
								margin-bottom: 20px;
								div.background-image {
									width: 100%;
									height: 100%;
//									background-image: url('../images/design/logo-box-sample.png');
									background-size: contain;
									background-position: center;
									background-repeat: no-repeat;
								}
								
								@include breakpoint(medium down){
									margin-left: auto;
									margin-right: auto;
								}
							}

						}
					}


				}
			}
		}
	}
}