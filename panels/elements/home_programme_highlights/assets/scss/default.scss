//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.elements-home_programme_highlights {
	background: url(/assets/images/design/events-bg.png);
	background-position: bottom center;
	background-size: cover;
	background-repeat: no-repeat;
	padding: 50px 0 60px;

	@include breakpoint(medium down){
		padding: 50px 0;
	}

	@include breakpoint(small down) {
		padding: 50px 0;
	}

	.unslider-arrow {
		display: none;
	}

	h2 {
		text-align: center;
		margin-bottom: 45px;
		font-size: 2.3em;

		@include breakpoint(small only){
			margin-bottom: 30px;
			font-size: 1.625rem;
		}
	}

	div.info-container {
		padding-bottom: 10px;

		@include breakpoint(large down){
//			padding-left: 40px;
		}

		@include breakpoint(medium down) {
			padding-left: 0;
		}
	}

	.video-col{

		.video-img{

			position: relative;
			background: $white;
//					background-image: url('../images/design/hp-video-sample-cover.png');
			background-position: center;
			background-repeat: no-repeat;
			background-size: cover;
			height: 280px;
			width: 100%;
			margin-bottom: 0;
//			box-shadow: 18px 18px 35px rgba(0,0,0,0.3);
			@include breakpoint(large down){
				height: 280px;
			}
			@include breakpoint(medium down){
				margin-bottom: 36px;
			}
			@include breakpoint(small only) {
				margin-bottom: 30px;
				height: 280px;
			}
			i{
				@include horizontal-vertical-align;
				width: 70px;
				height: 70px;
				line-height: 70px;
				background-color: $primary-color;
				color: $white;
				font-size: 2em;
				text-align: center;
				border-radius: 100%;
				@include transitions();
			}
			&:hover {
				i {
					background: darken($primary-color, 10%);
				}
			}

		}

		>a{
			text-transform: uppercase;
			color: $primary-color;
			font-family: $font-medium;
			position: relative;
			display: block;
			font-size: 1.1em;
			i{
				display: inline-block; 
				padding-left: 8px;
			}
			&:hover{
				color: darken($primary-color,10%);
			}

			@include breakpoint(medium down){
				margin-bottom: 40px;
			}
		}
	}

	.text-col{

		p, h6{
			font-size: 1.2em;
		}
		p{
			margin-bottom: 0;
		}
		h6{
			color: $primary-color;
			font-family: $font-medium;
			margin-bottom: 0;
		}
	}


	div.year-links{
		font-size: 1.2em;
		float: left;
		width: 100%;
		z-index: 10;

		a.year-links-testimonials{
			margin-top: 20px;
			text-transform: uppercase;
			color: $primary-color;
			font-family: $font-medium;
			position: relative;
			display: block;
			font-size: 16px;

			i{
				display: inline-block;
				padding-left: 10px;
			}
			&:hover{
				color: darken($primary-color,10%);
			}

			@include breakpoint(medium down){
				margin-bottom: 40px;
				padding-left: 25px;
			}
		}

		ul{
			list-style-type: none;
			margin-left: 60px;
			float: left;
			margin-top: -70px;

			@include breakpoint(medium down) {
				margin-left: 25px;
				margin-top: 0;
			}
			
			li{
				display: inline-block;
				&:after{
					content: "|";
					padding: 0 10px;
					color: $dark-gray;
				}
				&:last-child{
					&:after{
						display: none;
					}
				}

				a{
					font-family: $font-medium;
					color: $dark-gray;
					&:hover{
						color: $primary-color;
					}
				}

				a.active {
					color: $primary-color;
				}
			}
		}
	}

	.program-slider__item > div {
		display: flex;

		@include breakpoint(medium down) {
			flex-direction: column;
		}
	}

	.video-col {
		width: 35%;

		@include breakpoint(medium down) {
			width: 100%;
		}
	}

	.text-col {
		width: 65%;
		padding-left: 40px;
		display: flex;
		align-items: center;

		@include breakpoint(medium down) {
			width: 100%;
			padding-left: 0;
			text-align: center;
		}
	}

	nav.unslider-nav {
		height: 50px;
	    margin-top: 20px;
	    position: relative;
	    z-index: 4;

		ol {

			@include breakpoint(medium down) {
				margin-left: 1.5625rem;
			}

			li {
				background: $white;
				border: 1px solid darken($medium-gray, 6%);
				height: 14px;
				width: 14px;
				border-radius: 100%;
			}

			li.unslider-active {
				background: $primary-color;
				border: 1px solid $primary-color;
			}
		}
	}
}