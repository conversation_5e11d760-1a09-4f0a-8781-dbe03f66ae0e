
//  Settings
// - - - - - - - - - - - - - - - - - - - - - - - -


//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.elements-form-override {
	margin-bottom: 40px;
	margin-top: 40px;

    form {
    	background-color: #f7f7f7;
	    padding: 50px;
	    border: 1px solid #d3d1d3;
	    @include breakpoint(medium down) {
	    	padding: 30px;
	    }
	    div.dform {
	    	position: relative;
	    	div.dfinput_full {
	    		text-align: center;
	    	}
	    }
    }

    #forms_field_5,#forms_field_6,#forms_field_8{
    	width: 48%;
    		@include breakpoint(small down) {
		    	width: 100%;
		    }
	    	input{
	    		border: 1px solid #e9e9e9;
	    		&::-webkit-input-placeholder { color:#B8B8B8; }
				&::-moz-placeholder { color:#B8B8B8; } /* firefox 19+ */
				&:-ms-input-placeholder { color:#B8B8B8; } /* ie */
				&input:-moz-placeholder { color:#B8B8B8; }
	    	}
    }
	
	#forms_field_7{
		
	    position: absolute;
	    top: 0%;
	    right: 0%;
	    width: 48%;
	    @include breakpoint(small down) {
	    	width: 100%;
	    	position: relative;
	    }
		    textarea{
		    	height: 230px;
		    	border: 1px solid #e9e9e9;
		    	&::-webkit-input-placeholder { color:#B8B8B8; }
				&::-moz-placeholder { color:#B8B8B8; } /* firefox 19+ */
				&:-ms-input-placeholder { color:#B8B8B8; } /* ie */
				&input:-moz-placeholder { color:#B8B8B8; }
				@include breakpoint(small down) {
			    	height: 190px;
			    }
		    }
	}

	.button{    	
    	padding-top: 20px;
    	@include breakpoint(small down) {
	    	width: 100%;
	    }
	}

}


//outline: #e9e9e9 solid 1px;