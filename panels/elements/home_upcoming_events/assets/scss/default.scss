
//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.elements-home_upcoming_events {
	padding-top: 100px;
	padding-bottom: 50px;

	@include breakpoint(medium down) {
		padding-top: 60px;
		padding-bottom: 30px;
	}

	@include breakpoint(small down) {
		padding-top: 40px;
		padding-bottom: 25px;
	}

	.row{

		.column{

			h3{
				margin-bottom: 50px;
				@include breakpoint(medium down){
					margin-bottom: 30px;
				}
			}

			>p{
				margin-bottom: 60px;
				@include breakpoint(medium down){
					margin-bottom: 30px;
				}
			}

			>a{
				text-transform: uppercase;
				font-family: $font-medium;
				font-size: 1.1em;
				display: block;
				i{
					padding-left: 10px;
					vertical-align: top;
				}
				@include breakpoint(medium down){
					margin-bottom: 40px;
				}
			}


			.padding-right {
				padding-left: 40px;
				@include breakpoint (large only) {
					padding-left: 30px;
				}
				@include breakpoint (medium down) {
					padding-left: 0;
				}
				.events-container {
					-webkit-box-shadow: 0px 15px 35px 0px rgba(0,0,0,0.15);
					-moz-box-shadow: 0px 15px 35px 0px rgba(0,0,0,0.15);
					box-shadow: 0px 15px 35px 0px rgba(0,0,0,0.15);

					background: $white;
					border: 1px solid #F1F1F1;
					border-radius: 10px;
					overflow: hidden;
					margin-bottom: 20px;
					font-size: 0.9em;
					@include breakpoint (medium down) {
						margin-bottom: 40px;
					}
					@include breakpoint (small down) {
						margin-bottom: 20px;
					}
					.headings-container {
						background: $primary-color;
						padding: 17px 0;
						@include breakpoint (small down) {
							display: none;
						}
						.date, .type, .detail {
							text-transform: uppercase;
							font-family: $font-medium;
							font-size: 1.1em;
							background: none;
							color: $white;
						}
						.type{
							padding-left: 20px;
						}
					}
					.date, .type, .detail {
						display: inline-block; 
						color: $secondary-color;
						
					}
					.date {
						width: 15%;
						padding-left: 0;
						text-align: center;

						@include breakpoint (small down) {
							width: 100%;
							padding: 0;
							text-align: left;
						}
						
					}
					.type {
						width: 25%;
						@include breakpoint (small down) {
							width: 100%;
						}
					}
					.detail {
						width: 59%;
						@include breakpoint (small down) {
							width: 100%;
							padding: 0;
						}

						
					}
					a {
						position: relative;
						border-bottom: 1px solid #F1F1F1;
						display: block;
						color: $medium-gray;
						padding-right: 30px;
						
						&:hover{
							div.type {
								div.content-container {
									div.icon-container {
										&.enquire-icon {
											i {
												background: $green-highlight;
											}
										}
										i {
											background: $green-highlight;
										}
									}
								}
							}
							div.detail{
								.info-link{
									@include transitions;
									background: $green-highlight;
									color: $white;

									@include breakpoint(small down) {
										background: none;
										color: $green-highlight;
									}
								}
								.row-btn.enquire-btn{
									background-color: $green-highlight;
									@include transitions;
								}
							}
							
						}
						@include breakpoint (small down) {
							padding: 20px 30px;
						}
						&:last-child {
							border-bottom: none;
						}
						.date, .type {
							text-transform: uppercase;
							display: inline-block;
							vertical-align: middle;
							@include breakpoint (small down) {
								margin-bottom: 12px;
							}	
						}
						.date{
							color: $body-font-color;
						}
						.type {
							font-size: 0.9em;
							position: relative;
							background: #FAFAFA;
							padding: 0 20px;
							height: 100px;
							@include breakpoint(large down){
								padding: 17px;
							}
							@include breakpoint(medium down){
								padding: 13px;
							}
							.hide-large {
								display: none;
								@include breakpoint (small down) {
									display: inline-block;
									color: $body-font-color;
								}
							}
							.content-container {
								position: absolute;
								width: 76%;
								@include vertical-align();
								@include breakpoint (small down) {
									
									display: none;
								}
								.icon-container {
									display: inline-block;
									vertical-align: middle;
									position: relative;
									width: 30%;
									i{
										display: inline-block;
										border-radius: 100%;
										height: 35px;
										width: 35px;
										background-color: $primary-color;
										line-height: 35px;
										color: $white;
										text-align: center;
										font-size: 1.2em;
										@include transitions();
									}
									&.enquire-icon{
										i{
											background-color: $green-highlight;
										}
									}

									@include breakpoint (small down) {
										display: none;
									}
								}
								.type-text {
									display: inline-block;
									vertical-align: middle;
									width: 70%;
									p {
										margin-bottom: 0;
										line-height: 1.5;
										font-size: 1.1em;
										padding-left: 5px;
										@include breakpoint (large down) {
											font-size: 1em;
										}
									}
								}
							}
							@include breakpoint (small down) {
								background: none;
								padding: 0;
								height: auto;
								font-size: 1em;
							}
							i {
								color: $primary-color;
								font-size: 1.1em;
								@include breakpoint (small down) {
									display: none;
								}
							}
						}
						.type-icon {
							display: inline-block;
							vertical-align: middle;
						}
						.detail {
							position: relative;
							display: inline-block;
							vertical-align: middle;
							text-transform: uppercase;
							padding-left: 15px;
							color: $body-font-color;
							@include breakpoint (small down) {
								padding-left: 0;
							}

							.detail-text{
								display: inline-block;
								width: 65%;
								@include breakpoint (small down) {
									width: 100%;
								}
							}

							span.info-link{
								width: 115px;
								padding: 10px 10px;
								text-align: center;
								font-family: $font-medium;
								color: $primary-color;
								@include transitions;
								right: 0;
								@include vertical-align();
								@include breakpoint(small only){
									width: 100%;
									display: block;
									margin-top: 20px;
									text-align: left;
									position: relative;
									transform: none;
									padding: 0;
								}
							}

							span.row-btn.enquire-btn{
								width: 115px;
								display: inline-block;
								text-align: center;
								text-transform: uppercase;
								color: $white;
								font-size: 0.9em;
								padding: 10px;
								font-family: $font-medium;
								background-color: $green-highlight;
								@include transitions;
								@include vertical-align;
								right: 0;
								@include breakpoint(small only){
									display: block;
									width: 100%;
									margin-top: 20px;
									position: relative;
									transform: none;
								}
							}
						}
						.date-small, .type-small, .detail-small {
							display: none;
							@include breakpoint (small down) {
								display: block;
								color: $secondary-color;
								font-family: $font-medium;
								text-transform: uppercase;
								margin-right: 3px;
								vertical-align: top;
							}
						}




					}
				}
			}
			
		}
	}
}