<div id="{panels:id}" class="column small-12 elements-home_upcoming_events">
	<div class="row">
		<div class="column small-12 large-4 upcoming-text">
			{if heading}
				<h3>{heading}</h3>
			{/if}
			{if description}
				<p>{description}</p>
			{/if}

			{button}
				{if type == "page"}
					<a href="{if entry_id}{structure:page_uri_for:{entry_id}}{/if}">{button_text} <i class="icon icon-chevron-right2"></i></a>
				{if:elseif type == "url"}
					<a href="{url}">{button_text} <i class="icon icon-chevron-right2"></i></a>
				{if:elseif type == "video"}
					<a href="#" data-lightbox-video="{video}">{button_text} <i class="icon icon-chevron-right2"></i></a>
				{if:elseif type == "form"}
					<a href="#" data-lightbox-ajax="form/{form}">{button_text} <i class="icon icon-chevron-right2"></i></a>
				{if:elseif type == "ajax"}	
					<a href="#" data-lightbox-ajax="{ajax}">{button_text} <i class="icon icon-chevron-right2"></i></a>
				{if:elseif type == "slide_toggle"}	
					<a href="#" data-slide-toggle="{slide_toggle}">{button_text} <i class="icon icon-chevron-right2"></i></a>
				{/if}
			{/button}
			
		</div>
		

		

		<div class="column small-12 large-8">

			<div class="padding-right">

				<div class="events-container">

					<div class="headings-container">
						<div class="date">Date</div>
						<div class="type">Type</div>
						<div class="detail">Details</div>
					</div>

					{exp:entries:events channel="events" show_future_entries="yes" start_on="{current_time format='%Y-%m-%d 00:01'}" orderby="date" sort="asc" dynamic="no" limit="4" parse="inward"}

						<a href="{events:page_uri}" class="event-row">
							
							<div class="date">
								<div class="date-small">Date:</div>
								{events:entry_date format="%d %M"}
							</div>

							<div class="type">
								<div class="hide-large">
									<div class="type-small">Type:</div>
									{events:categories}
									        {events:category_name}&nbsp
									{/events:categories}
								</div>
								<div class="content-container">
									<div class="icon-container">
										<i class="icon {events:categories limit="1"}{if events:category_id == "15"}flaticon-people{if:elseif events:category_id == "16"}flaticon-interface{if:elseif events:category_id == "17"}flaticon-social{if:elseif events:category_id == "18"}flaticon-icon-2994{if:elseif events:category_id == "19"}flaticon-up-arrow{if:else}flaticon-icon-2994{/if}{/events:categories}"></i>

										

									</div>
									<div class="type-text">
										<p>
											{events:categories limit="1"}
											        {events:category_name}
											{/events:categories}
										</p>
									</div>
								</div>
							</div>

							<div class="detail">
								<div class="detail-small">Detail:</div>
								{!-- {events:title} --}
								<span class="detail-text">{events:title}</span>
								<span class="info-link">More info</span>
							</div>
						</a>

					{/exp:entries:events}

						

					
				</div>

			</div>

		</div>





	</div>
</div>