//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.elements-home_video_panel {
	position: relative;
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
//	background-image: url('../images/design/header_sample.png');
		
	.video-container{
		position: absolute;
    	width: 100%;
    	height: 100%;
    	top: 0;
    	left: 0;
    	overflow: hidden;
    	@include breakpoint(medium down) { display: none; }
    	video {
    		width: 120%;
    		position: absolute;
    		left: 0;
    		top: -100px;
    	}

	}

	div.overlay {
		display: block;
		background-image: url(/assets/images/design/homepage_video_overlay_opacity-75.png);
		background-position: bottom center;
		background-repeat: no-repeat;
		background-size: cover;
		width: 100%;
		height: 101%;
		position: absolute;
		top: 0;
		left: 0;

	}

	>.row {
		@include breakpoint(xlarge up) { max-width: 1200px !important; }
		@include breakpoint(large only) { max-width: 980px !important; }
		@include breakpoint(medium only) { max-width: 75rem !important; }
		margin: 0 auto  !important;
		z-index: 1;
		position: relative;
		padding: 150px 0 170px;
		@include breakpoint(medium down) {
			padding: 120px 0 140px;
		}
		.column{
			text-align: center;

			h1 {
				color: white;
				margin-bottom: 40px;
				max-width: 800px;
				margin-right: auto;
				margin-left: auto;
			}

			a.button{
				min-width: 250px;
				padding: 20px;
				@include breakpoint(small down){
					width: 100%;
				}
				&:first-of-type{
					border: 2px solid $white;
					background-color: transparent;
					color: $white;
					margin-right: 20px;
					&:hover{
						background-color: rgba(255,255,255,0.1);
					}
				}
				&:last-of-type{
					background-color: $white;
					border: 1px solid $white;
					color: $primary-color;
					@include breakpoint(small down){
						margin-bottom: 0;
					}
					&:hover{
						color: darken($primary-color,10%);
					}

				}
			}
		}


	}
}

.act-lightbox-wrap {

	.brochure_wrap {
		position: relative;
	}

	.notice.success {
		background-color: transparent;
		padding: 0;
		border: 1px solid transparent;
		background: $primary-color;
    	text-align: center;
    	color: $white;
		padding: 25px 50px;

    	a.button {
    		display: block;
    		margin: 20px auto 0;
    		width: 280px;
    		color: $primary-color;
    		background: $white;
    		max-width: 100%;
    	}
	}
}