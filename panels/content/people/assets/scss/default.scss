
//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.content-people {
	.row{
		.column{
//			&:nth-child(n+4) {
//				margin-top: 20px;
//			}
//			@include breakpoint(medium down) {
//				margin-top: 20px;
//				&:nth-child(1), &:nth-child(2) {
//					margin-top: 0;
//				}
//			}
			.people{
				margin-right: 20px;
				@include breakpoint(small down) {
					margin: 0 auto;
					max-width: 270px;
				}
				.people-photo{
//					background-image: url('../images/design/person_box_sample.png');
					background-size: cover;
					background-repeat: no-repeat;
					background-position: top;
					width: 100%;
					height: 200px;
					margin-bottom: 20px;
					@include breakpoint(large down) {
						height: 180px;
					}
				}

				.people-text{
					position: relative;

					h4{
						text-transform: uppercase;
						font-size: 19px;
						font-family: $font-medium;
					}
					.people-inst{
						display: block;
						font-family: $font-medium;
						font-style: italic;
						color: $dark-gray-alt;
						text-transform: none;
						font-size: 18px;
					}
					hr{
						width: 80px;
						height: 2px;
						display: block;
						margin: 10px 0;
						background-color: black;
					}
					.position{
						display: block;
						color: $dark-gray-alt;
						font-family: $font-medium;
						margin-bottom: 10px;
						font-size: 18px;
					}
					a{
						text-transform: uppercase;
						font-family: $font-medium;
						position: relative;
						i{
							padding-left: 15px;
							&:before{
								position: absolute;
								top: 0;
								font-size: 20px;
							}
						}

					}
				}
			}
		}
	}
}

div.lightbox {
	div.lightbox-container {
		padding: 20px;
		@include breakpoint(large down) {
			padding: 0;
		}
		div.image-container {
			width: 168px;
			display: inline-block;
			vertical-align: top;
			@include breakpoint(large down) {
				width: 144px;
			}
			div.background-image {
//				background-image: url('../images/design/person_box_sample.png');
				background-size: cover;
				background-repeat: no-repeat;
				background-position: center;
				width: 100%;
				height: 168px;
				margin-bottom: 12px;
				@include breakpoint(large down) {
					height: 144px;
				}
			}
			p {
				font-size: 16px;
				i {
					color: $primary-color;
				}
			}
		}
		div.text-container {
			display: inline-block;
			width: 75%;
			padding-left: 40px;
			@include breakpoint(large down) {
				width: 70%;
			}
			@include breakpoint(medium down) {
				width: 100%;
				padding-left: 0;
				margin-top: 10px;
			}
			h5{
				text-transform: uppercase;
				font-size: 19px;
				font-family: $font-medium;
			}
			p.position{
				display: block;
				color: $primary-color;
				font-style: italic;
				font-family: $font-medium;
				margin-bottom: 20px;
				font-size: 18px;
			}
			p.text {
				@include breakpoint(small down) {
					font-size: 15px;
				}
			}
			a {
				font-size: 1.1em;
			}
	 	}
	}
}