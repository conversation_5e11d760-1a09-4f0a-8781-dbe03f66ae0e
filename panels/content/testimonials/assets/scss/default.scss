
//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.content-testimonials {
	position: relative;
	margin-top: 30px;

	a.unslider-arrow {
		display: none;
	}
	div.row {
		&.quotes {
			margin-top: -57px;
			div.quotes-slider {
				i {
					color: $primary-color;
					font-size: 5.5em;
					margin-right: 45px;
				}
			}
			div.unslider {
				width: 100%;
				text-align: right;
				a.unslider-arrow {
					display: none;
				}
			}
		}
		div.column {
			div.testimonials-slider {
//				box-shadow: 0 15px 26px rgba(black, 0.1);
				
				border: 1px solid darken($medium-gray, 6%);

				ul {
					margin-left: 0;
					margin-bottom: 0;
					li {
						list-style: none;
						div.testimonial-slider-panel {
						padding: 30px 30px 70px;
//							
						}
						div.testimonial-slider-text {
							p {
								font-size: 1em;
								font-family: $font-italic;
								margin-bottom: 25px;
							}
						}
						div.testimonial-slider-author {
							div.image-container {
								width: 80px;
								height: 80px;
								display: inline-block;
								margin-right: 22px;
								vertical-align: middle;
								@include breakpoint(small down) {
									width: 100%;
									margin-bottom: 10px;
								}
								div.background-image {
//								    background: url(/assets/images/design/profile-image.jpg); 
							    	background-repeat: no-repeat;
								    background-size: cover;
								    background-position: center;
								    border-radius: 50%;
								    width: 100%;
								    height: 100%;
								    display: block;
								    @include breakpoint(small down) {
										width: 80px;
									}
								}
							}
							div.text-container {
								display: inline-block;
								width: 70%;
								vertical-align: middle;
								@include breakpoint(small down) {
									width: 100%;
									margin-bottom: 20px;
								}
								h4 {
									font-family: $font-medium;
									text-transform: uppercase;
									font-size: 1.2em;
									margin-bottom: 0;
									@include breakpoint(small down) {
										font-size: 1.1em;
									}
								}
								p {
									color: $primary-color;
									font-family: $demi-bold-italic;
									margin-bottom: 0;
								}
							}
						}
					}
				}
			}

			nav.unslider-nav {
				height: 50px;
			    margin-top: -50px;
			    position: relative;
			    z-index: 4;
//			    @include breakpoint(medium down){
//					margin: -60px 40px 0;
//    				max-width: 100%;
//				}

//				@include breakpoint(small down) {
//					margin: -60px 20px 0;
//				}

				ol {
					float: left;
					margin-left: 40px;
					@include breakpoint(medium down) {
						margin-left: 1.5625rem;
					}
					li {
						background: $white;
						border: 1px solid darken($medium-gray, 6%);
						height: 14px;
						width: 14px;
						border-radius: 100%;
					}

					li.unslider-active {
						background: $primary-color;
						border: 1px solid $primary-color;
					}
				}
			}
		}
	}
}