
//  Settings
// - - - - - - - - - - - - - - - - - - - - - - - -

//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.content-text-override {
	h1, h2, h3, h4, h5, h6 {
		margin-bottom: 14px;
	}
	table {
		margin-top: 20px;
		tbody {
			tr {
				&:first-child {
					background: $primary-color;
					color: white;
					text-transform: uppercase;
					font-family: $font-medium;
				}
				td {
					font-size: 18px;
				}
			}
		} 
	}
	p {
		margin-bottom: 30px;
	}
	ul {
		margin-left: 20px;
		margin-top: 20px;
		margin-bottom: 20px;
		li {
			list-style: none;
			padding-left: 24px;
			position: relative;
			font-size: 18px;
			margin-bottom: 14px;
			&:after {
				content: "";
				background: darken($dark-gray, 12%);
				width: 8px;
				height: 8px;
				border-radius: 50%;
				left: 0;
				top: 9px;
				position: absolute;
			}
		}
	}
	ol {
		margin-left: 40px;
		margin-top: 20px;
		li {
			font-size: 18px;
			margin-bottom: 14px;
		}
	}
}