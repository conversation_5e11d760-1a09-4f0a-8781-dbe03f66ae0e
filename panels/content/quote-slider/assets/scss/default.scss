
//  Style
// - - - - - - - - - - - - - - - - - - - - - - - -
div.content-quote-slider {

	div.row{




		h3{
			text-align: center;
			margin-bottom: 45px;
			font-size: 2.35em;

			@include breakpoint(small down){
				font-size: 2em;
			}
		}

		div.unslider {
			margin: 0 auto;
			-webkit-box-shadow: 0px 1px 35px 0px rgba(0,0,0,0.15);
			-moz-box-shadow: 0px 15px 35px 0px rgba(0,0,0,0.15);
			box-shadow: 0px 15px 35px 0px rgba(0,0,0,0.15);
			@include breakpoint(medium down){
				-webkit-box-shadow: none;
				-moz-box-shadow: none;
				box-shadow: none;
			}

			div.quotes-slider {
				border: 1px solid $alt-gray-3;
				background: $white;
				position: relative;

				@include breakpoint(medium down){
					margin: 0 1.5625rem;
    				max-width: 100%;
				}

				@include breakpoint(small down) {
					margin: 0 20px;
				}

				ul {

					li {

						div.quote-slider-panel {
							padding: 50px 40px 75px;
							float: left;

							div.quote-slider-text {
								margin-bottom: 26px;

								p {
									font-size: 1.1em;
									line-height: 1.8;
									margin: 0;
									font-style: italic;
								}
							}

							div.quote-slider-author {

								div.person-photo{
									background-image: url('../images/design/person_sample.png');
									background-size: cover;
									background-position: center;
									background-repeat: no-repeat;
									height: 80px;
									width: 80px;
									border-radius: 100%;
									display: inline-block;
								}

								div.person-info{
									display: inline-block;
									padding-left: 20px;
									vertical-align: top;
									span{
										font-family: $demi-bold-regular;
										display: block;
										vertical-align: middle;
										font-size: 1.2em;
										&.name{
											padding-top: 15px;
											color: $black;
											text-transform: uppercase;
										}
										&.position{
											color: $primary-color;
											font-style: italic;

										}
									}
								}
							}
						}
					}

					li::before {
			            content: ""; 
			            color: $primary-color;
			            display: inline; 
			            margin-left: 0;
			            position: relative;
			        }
				}
			}

			nav.unslider-nav {
				height: 60px;
			    margin-top: -60px;
			    position: relative;

			    @include breakpoint(medium down){
					margin: -60px 40px 0;
    				max-width: 100%;
				}

				@include breakpoint(small down) {
					margin: -60px 20px 0;
				}

				ol {
					float: left;
					margin-left: 40px;
					@include breakpoint(medium down) {
						margin-left: 1.5625rem;
					}
					li {
						background: $alt-gray-3;
						border: 1px solid $alt-gray-3;
						height: 12px;
						width: 12px;
						border-radius: 100%;
					}

					li.unslider-active {
						background: $primary-color;
						border: 1px solid $primary-color;
					}
				}

			}

			a.unslider-arrow {
				display: none;
			}
		}

		div.quotemarks-slider {
			position: relative;
			margin: 0 auto;
			width: 100%;

			@include breakpoint(medium down) {
				margin: 0 40px;
				width: 100%;
			}

			@include breakpoint(small down) {
				margin: 0 20px;
			}

			div.quotes-link {
				float: left;
				width: 100%;
				margin-top: 15px;

				a.link{
					color: $primary-color;
					text-transform: uppercase;
					font-family: $alt-font-family;
					margin: 0 0 0 40px;
					font-size: 1.2em;
					@include breakpoint(medium down) {
						margin: 0 0 0 1.5625rem;
					}
					&:hover {
						color: $secondary-color;
					}

					i {
						position: relative;
						margin-right: 10px;
						top: 2px;
					}
				}
			}

			div.quotemarks {
				position: absolute;
				top: -45px;
				right: 40px;

				@include breakpoint(small down) {
					display: none;
				}

				img {
					max-width: 120px;
					margin: 0;
				}
			}
		}
	}
}